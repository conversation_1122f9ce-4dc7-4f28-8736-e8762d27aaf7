{"compilerOptions": {"target": "ES6", "module": "commonjs", "lib": ["dom", "dom.iterable", "esnext"], "types": ["vite/client", "vite-plugin-svgr/client"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist"}, "include": ["src/**/*"]}