// @ts-nocheck
import { contextBridge, ipcRenderer, webFrame } from 'electron';


// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object

contextBridge.exposeInMainWorld('electron', {
  send: ({ channel, data }) => {
    ipcRenderer.send(channel, data);
  },

  receive: (channel, func) => { 
    // Deliberately strip event as it includes `sender`
    ipcRenderer.on(channel, (event, ...args) => func(...args));
  },
  invoke: (channel, data) => {
    return ipcRenderer.invoke(channel, data);
  },

  sendSync: ({ channel, data }) => {
    return ipcRenderer.sendSync(channel, data);
  },
  handleZoom: (callback) => {
    ipcRenderer.on('zoom',(event,zoomFactor)=>{
      webFrame.setZoomFactor(zoomFactor);
      if(callback)
      callback();
    })
  },
  isWeb: false
});
