// @ts-nocheck
import { ipcMain } from 'electron';
import Pusher from "pusher-js";
import config from './config';
import { showNotification, setNotificationConstants, markAsReadNotification, notificationEventsHandler } from './notification';
import { commonPusherHandlerInit, customNotificationHandler, uploadLogsToS3Handler } from './commonPusherHandler';

const { pusherNotification, commonAppEventsofPusher } = config

let pusher = null;
let channelId = null;
let userId = null;
let mainWindow;
let channelWindow;
let pusherHeader = {};
let isPrivateChannelDisconnected = false;
const [publicChannel, privateChannel, buyerChannel, sellerChannel] = pusherNotification.channels;
const {privateEvents,publicEvents,buyerEvents,sellerEvents} = pusherNotification.channelEvents;

function pusherInit(browserWindow, _channelWindow, store) {
    console.log("Apps pusher initialised");
    mainWindow = browserWindow;
    channelWindow = _channelWindow;
    ipcMain.on(channelWindow.pusher, (event, data) => {
        console.log("Create Pusher Channels");
        createPusherChannel(data.user ,data.pusherId, data.emailId, data.accessToken, store);
    });
    ipcMain.on(channelWindow.showNotification, (event, notificationList) => {
        if(notificationList && Array.isArray(notificationList)){
            notificationList.forEach(notificationSchema => showNotification(notificationSchema.notification));
            notificationList = notificationList.map(notificationSchema => notificationSchema.notification);
        }
        markAsReadNotification(notificationList);
    });
    ipcMain.on(channelWindow.refreshPrivateChannel,(event,data)=>{
        if(pusher && isPrivateChannelDisconnected){
            reconnectToPrivateChannel(data.accessToken);
        }
    })
    ipcMain.on(channelWindow.isMarkAsReadNotification, (event, isRead) => {
        if(isRead && store?.has('notificationListOfUsers')){
            const _notificationListOfUsers = store.get('notificationListOfUsers');
            if(_notificationListOfUsers[userId]?.length > 0){
                _notificationListOfUsers[userId] = undefined;
                store.set('notificationListOfUsers',_notificationListOfUsers);
            }
        }
    })
    ipcMain.on(channelWindow.logout, (event, data) => {
        console.log("logout");
        setNotificationConstants(null,store);
        unsubscribeToChannel();
    });
    commonPusherHandlerInit(browserWindow,_channelWindow);
}

const createPusherChannel = (user, pusherId, emailId, accessToken, store) => {
    console.log("pusher", pusher===null);
    if(pusher === null){
        userId=user.id;
        pusherHeader.accessToken = accessToken;
        pusherHeader.emailId = emailId;
        setNotificationConstants(userId, store, channelWindow, mainWindow);
        console.log("Pusher Notifier init for user = ");
        pusher = new Pusher(pusherNotification.pusher.key, {
            cluster: pusherNotification.pusher.cluster,
            channelAuthorization: {
                endpoint: `${pusherNotification.authUrl}/notification/auth`,
                headers: pusherHeader,
            },
        });
        subscribeToGlobalChannel();
        subscribeToPrivateChannel(pusherId);
        if(user.type === 'SELLER') subscribeToSellerChannel();
        if(user.type === 'BUYER') subscribeToBuyerChannel();
    }
    else{
        if(isPrivateChannelDisconnected){
            console.log("Creating Fresh private channel");
            reconnectToPrivateChannel(accessToken);
        }
    }
}

const reconnectToPrivateChannel = (accessToken)=>{
    pusher.unsubscribe(`${privateChannel}${channelId}`);
    pusherHeader.accessToken = accessToken;
    subscribeToPrivateChannel(channelId);
}

const subscribeToGlobalChannel = ()=>{
    console.log("Pusher Notifier init");
    const channel = pusher.subscribe(publicChannel);
    channel.bind("pusher:subscription_succeeded", () => {console.log("Subscribed to public channel :")});
    channel.bind("pusher:subscription_error", (error) => {console.log("Error public channel :"+error)});
    notificationEventsHandler(channel, publicEvents);
    customNotificationHandler(channel, commonAppEventsofPusher.publicEvents.customNotification);
}
const subscribeToBuyerChannel = ()=>{
    const channel = pusher.subscribe(buyerChannel);
    channel.bind("pusher:subscription_succeeded", () => {console.log("Subscribed to buyer channel :")});
    channel.bind("pusher:subscription_error", (error) => {console.log("Error buyer channel :"+error)});
    notificationEventsHandler(channel, buyerEvents);
    customNotificationHandler(channel, commonAppEventsofPusher.buyerEvents.customNotification);
}
const subscribeToSellerChannel = ()=>{
    const channel = pusher.subscribe(sellerChannel);
    channel.bind("pusher:subscription_succeeded", () => {console.log("Subscribed to seller channel :")});
    channel.bind("pusher:subscription_error", (error) => {console.log("Error seller channel :"+error)});
    notificationEventsHandler(channel, sellerEvents);
    customNotificationHandler(channel, commonAppEventsofPusher.sellerEvents.customNotification);
}

const subscribeToPrivateChannel = (pusherId)=>{
    channelId = pusherId;
    console.log("Pusher Private channel init = ");
    const channel = pusher.subscribe(`${privateChannel}${channelId}`);
    channel.bind("pusher:subscription_succeeded", () => {
        console.log("Subscribed to private channel :");
        isPrivateChannelDisconnected=false;
    });
    channel.bind("pusher:subscription_error", (error) => { 
        console.log("Error private channel :", error);
        isPrivateChannelDisconnected=true;
        setTimeout(()=>{
            mainWindow?.webContents.send(channelWindow.getAccessToken, pusherId);
        },5000);
    });
    notificationEventsHandler(channel, privateEvents);
    customNotificationHandler(channel, commonAppEventsofPusher.privateEvents.customNotification);
    uploadLogsToS3Handler(channel)
}

const unsubscribeToChannel = () => {
    if(pusher){
        pusher.unsubscribe(publicChannel);
        pusher.unsubscribe(`${privateChannel}${channelId}`);
        pusher.unsubscribe(buyerChannel);
        pusher.unsubscribe(sellerChannel);
    }
    pusher = null;
    channelId = null;
    userId = null;
    pusherHeader={};
    isPrivateChannelDisconnected=false;
}

export default pusherInit