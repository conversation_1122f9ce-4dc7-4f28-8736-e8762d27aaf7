import { app } from 'electron';

export const isDev = !app.isPackaged;

export const urlMasks = [
    {
      "key": "bryzos.com",
      "mask": "TL0hk"
    },
    {
      "key": "vercel.app",
      "mask": "70N1sX"
    },
    {
      "key": "bryzoswidget.com",
      "mask": "x8nZVM7I"
    },
    {
      "key": "bryzosservices.com",
      "mask": "G6Tmy"
    },
    {
      "key": "imgix.net",
      "mask": "uDLGFhKx"
    },
    {
      "key": "imagekit.io",
      "mask": "Ny7PH02R"
    },
    {
      "key": "cloudfront.net",
      "mask": "xXKaBdR"
    },
    {
      "key": "capgo.app",
      "mask": "a8CfxyV7a"
    },
    {
      "key": "amazonaws.com",
      "mask": "U12FEsJH"
    },
    {
      "key": "cassinfo.com",
      "mask": "Fr0mj1v3J"
    },
    {
      "key": "truevault.com",
      "mask": "hsQ6oqGZt"
    },
    {
      "key": "wss://ws-ap2.pusher.com",
      "mask": "h0ttI"
    },
    {
      "key": "|",
      "mask": "__PIPE__"
    }
  ]