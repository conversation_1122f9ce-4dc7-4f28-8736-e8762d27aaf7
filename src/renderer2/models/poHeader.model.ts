import * as yup from 'yup';

export const poHeaderSchema = yup.object().shape({
    isEdit: yup.boolean().default(true),
    buyer_internal_po: yup.string().trim().required('Po Job Name is not valid'),
    order_type: yup.string(),
    delivery_date: yup.string(),
    shipping_details: yup.object().shape({
        line1: yup.string().trim().required('Address is not valid'),
        line2: yup.string().trim().optional(),
        city: yup.string().trim().required('City is not valid'),
        state_id: yup.number().required('State is not valid'),
        zip: yup.string().trim().required('Zip is not valid'),
        validating_state_id_zip: yup.boolean().default(false).defined()
    })
});
