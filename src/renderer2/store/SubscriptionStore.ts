// import { ReferenceDataProduct } from "@bryzos/giss-ui-library";
import { create } from "zustand";


const commonSubscriptionStore = {
    subscriptionDialogOpen: false,
    userSubscription: null,
    isStripeLoaded: false,
    isFromSetting: false,
    subscriptionsPricing: null,
    userList: [],
    uploadUserList: [],

}

export const useSubscriptionStore = create<any>((set: any) => ({
    ...commonSubscriptionStore,
    setSubscriptionDialogOpen: (subscriptionDialogOpen: boolean) => set({ subscriptionDialogOpen }),
    setUserSubscription: (userSubscription: any) => set({ userSubscription }),
    setIsStripeLoaded: (isStripeLoaded: boolean) => set({ isStripeLoaded }),
    setIsFromSetting: (isFromSetting: boolean) => set({ isFromSetting }),
    setSubscriptionsPricing: (subscriptionsPricing: any) => set({ subscriptionsPricing }),
    closeSubscriptionDialog: () => set({ subscriptionDialogOpen: false, isStripeLoaded: false }),
    setUserList: (userList: any) => set({ userList }),
    setUploadUserList: (uploadUserList: any) => set({ uploadUserList }),
    resetSubscriptionStore: () => set((state : any) => ({
      ...commonSubscriptionStore
    })),
}))