import { create } from 'zustand';

interface AppState {
  mainWindowWidth: number | null;
  setMainWindowWidth: (mainWindowWidth: number | null) => void;
  screenWidth: number;
  screenHeight: number;
  setScreenWidth: (screenWidth: number) => void;
  setScreenHeight: (screenHeight: number) => void;
  isFullScreen: boolean;
  setIsFullScreen: (isFullScreen: boolean) => void;
  fullScreenHeight: number;
  setFullScreenHeight: (fullScreenHeight: number) => void;
  fullScreenWidth: number;
  setFullScreenWidth: (fullScreenWidth: number) => void;
  workAreaHeight: number;
  setWorkAreaHeight: (workAreaHeight: number) => void;
  workAreaWidth: number;
  setWorkAreaWidth: (workAreaWidth: number) => void;
}

const commonStore = {
    mainWindowWidth: null,
    screenWidth: 1440,
    screenHeight: 1024,
    isFullScreen: false,
    fullScreenHeight: 0,
    fullScreenWidth: 0,
    workAreaHeight: 0,
    workAreaWidth: 0,
}
  
  
  export const useAppStore = create<AppState>((set, get) => ({
    ...commonStore,
    setMainWindowWidth: (mainWindowWidth: number | null) => set({ mainWindowWidth }),
    setScreenWidth: (screenWidth: number) => set({ screenWidth }),
    setScreenHeight: (screenHeight: number) => set({ screenHeight }),
    setIsFullScreen: (isFullScreen: boolean) => set({ isFullScreen }),
    setFullScreenHeight: (fullScreenHeight: number) => set({ fullScreenHeight }),
    setFullScreenWidth: (fullScreenWidth: number) => set({ fullScreenWidth }),
    setWorkAreaHeight: (workAreaHeight: number) => set({ workAreaHeight }),
    setWorkAreaWidth: (workAreaWidth: number) => set({ workAreaWidth }),
    resetAppStore: () => set(() => ({
      ...commonStore
    })),
  }));
    