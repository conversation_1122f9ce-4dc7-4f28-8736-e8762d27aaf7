import { create } from "zustand";

type OrderManagementStoreType = {
    orderManagementData: any[];
    setOrderManagementData: (orderManagementData: any) => void;
    resetOrderManagementStore: () => void;
}

const commonOrderManagementStore = {
    orderManagementData: [],
}

export const useOrderManagementStore = create<OrderManagementStoreType>((set) => ({
    ...commonOrderManagementStore,
    setOrderManagementData: (orderManagementData: any) => set({orderManagementData}),
    resetOrderManagementStore: () => set({...commonOrderManagementStore}),
}))