// @ts-nocheck
import axios from "axios";
import { saveAs } from 'file-saver';
import rg4js from "raygun4js";
import { commomKeys, localStorageKeys, routes } from "./common";
import { Auth } from 'aws-amplify';
import { ReferenceDataProduct } from "./types/ReferenceDataProduct";
import { getQuantityGroup, getRegionByZip } from "./pages/search/searchUIUtils";
import { ProductPricingModel } from "./types/Search";
import { dollarSign, neutralPricingPrefix, newPricingPrefix, noIdGeneric, orderIncrementPrefix, orderType, priceUnits, referenceProductItem, referenceProductQuantity, useBuyerSettingStore, useGlobalStore, userRole, useSearchStore } from "@bryzos/giss-ui-library";
import { v4 as uuidv4 } from 'uuid';
import { getPriceExample } from "./utility/priceIntegratorExample";
import dayjs from "dayjs";
import { useLeftPanelStore } from "./component/LeftPanel/LeftPanelStore";
import { createExcelBlob } from "./pages/exporttToExcelUtils";


export const VERSION_NUMBER = "*******";
export const commonRaygunError = "common-raygun-error";
export const trueVaultRaygunError = "trueVault-raygun-error";
export const unhandledError = "unhandled-error";
export const pdfGenreationError = "pdf-generation-error";

export const SEND_INVOICES_TO = "<EMAIL>";
export const SHIPPING_DOCS_TO = "<EMAIL>";
export const MENU_ANIMATION_DURATION = 700;
export const mainConfig = {
  log: {
    shareLogSize: 0
  } 
};

let channelWindow = {}
let navigate = null;
let userAppData = null;
export const setChannelWindow = (channelWindowList: {}) => {
  channelWindow = channelWindowList
}

export const getChannelWindow = () => {
  return channelWindow;
}

export const setNavigate = (_navigate) => {
  if(navigate === null){
    navigate = _navigate;
  }
}

export const navigatePage = (currentPage, nextPage) => {
  if(currentPage === routes.orderConfirmationPageSeller 
    || currentPage === routes.acceptOrderPage
    || currentPage === routes.orderConfirmationPage
    ){
    navigate(nextPage.path,{ ...nextPage.state ,replace: true})
  }else{
    navigate(nextPage.path, nextPage.state)
  }
}
 
// Format a number as a currency value with a dollar sign
export function formatCurrency(number) {
  return Number(number).toLocaleString('en-US', {
    currency: 'USD',
    style: 'currency',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).replace("$", "$ ");
}

// Format a number with commas without dollar sign
export function formatNumber(number: number) {
  return number.toLocaleString('en-US');
}

export const formatToTwoDecimalPlaces = (
  value?: string
) => {
  if (value && +value) {
   return Number(value).toLocaleString('en-US',{
    style:'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
   });
  } else {
    return '0.00';
  }
};

export const formatPhoneNumberRemovingCountryCode = (phoneNo: string)=>{
  if(phoneNo)
  return formatPhoneNumber( phoneNo.replace(commomKeys.countryCode,'') );
  return phoneNo;
}

export const formatPhoneNumber = (phoneNo: string) => {
  const phoneNumber = phoneNo.replace(/\D/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) {
    return phoneNumber;
  } else if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  } else {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
};

export const formatPhoneNumberWithCountryCode = (phoneNo: string) => {
    return commomKeys.countryCode + phoneNo.replace(/[^\w\s]/gi, '').replace(/\s+/g, '');
};

export const addWorkingDaysToDate = (days: number) => {
  let date = new Date();
  let count = 0;

  while (count < days) {
    date.setDate(date.getDate() + 1);
    if (date.getDay() !== 0 && date.getDay() !== 6) {
      count++;
    }
  }
  return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear().toString().slice(2)}`;
};

export const formatEIN = (value: string) => {
  const ein = value.replace(/\D/g, '');
  const einLength = ein.length;
  if (einLength < 3) {
    return ein;
  } else {
    const firstPart = ein.slice(0, 2);
    const secondPart = ein.slice(2, 9);
    return `${firstPart}-${secondPart}`;
  }
};


export async function downloadFilesUsingFetch(url: RequestInfo | URL, fileName: string | undefined, type: any) {
  try {
    const response = await fetch(url);
    const responseData = await response.blob();

    if(responseData.type === 'application/json'){
      return false;
    }
    const blob = new Blob([responseData], { type: type });
    saveAs(blob, fileName);
    return true;
  } catch (error) {
    console.error('Error downloading the file:', error);
  }
}

export async function downloadFiles(url: string, fileName: string | undefined, type: any) {
  try {
    const response = await axios.get(url,{
      responseType: 'blob'
    });

    if(response.data.type === 'application/json'){
      return false;
    }
    const blob = new Blob([response.data], { type: type });
    saveAs(blob, fileName);
    return true;
  } catch (error) {
    console.error('Error downloading the file:', error);
  }
}

export const downloadFileWithAnyExtension = async (url: string) => {
  let index = url.length-1;
  for(  ;index >= 0 ;index-- ){
  if(url.charAt(index)==='/'){
      break;
  }
  }
  let fileName = url.substring(index+1, url.length);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

export const dispatchRaygunError = (error: unknown, tags: string | string[]) => {
  rg4js('send', {
    error: error,
    tags: tags
  })
}

export const formatCurrencyWithComma = (value: string) => {
  if (value) {
    return value.replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
  } else {
    return "";
  }
};

export const removeCommaFromCurrency = (value: string) => {
  if (value) {
    return value.replace(/\,/g, "");
  } else {
    return "";
  }
};

export const getFloatRemainder = (dividend: string | number | null, divisor: number) => {
  if (dividend && +divisor) {
    dividend = +dividend;
    divisor = +divisor;

    var dividendDecimalCount = (dividend.toString().split('.')[1] ?? '').length;
    var divisorDecimalCount = (divisor.toString().split('.')[1] ?? '').length;
    var decimalCount = dividendDecimalCount > divisorDecimalCount ? dividendDecimalCount : divisorDecimalCount;

    var dividendInt = parseInt(dividend.toFixed(decimalCount).replace('.', ''));
    var divisorInt = parseInt(divisor.toFixed(decimalCount).replace('.', ''));

    return (dividendInt % divisorInt) / Math.pow(10, decimalCount);
  } else {
    throw new Error("dividend or divisor value is invalid");
  }
}

export const get2DigitFormater = () => {
  return  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});
}

export const get4DigitFormater = () => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 4,
    maximumFractionDigits: 4,
});
}

export const format4DigitAmount = (value: string | number) => {
  const decimal4Formater = get4DigitFormater();
  const _value = +value ? +value : 0;
  return decimal4Formater.format(_value).replace("$", "");
};

export const format2DigitAmount = (value: string | number) => {
  const decimal2Formater = get2DigitFormater();
  const _value = +value ? +value : 0;
  return decimal2Formater.format(_value).replace("$", "");
};

export function getNumericVersion(version: string | undefined)
{
  if (version && typeof version === "string") {
    return Number(version.split(".").join(""));
  }
  return false; 
}

export async function getAccessToken() {
  const user = await Auth.currentSession();
  const accessToken = user.getAccessToken();
  const jwt = accessToken.getJwtToken();
  return jwt;
}

export const getProductMapping = (productList: ReferenceDataProduct[], userData) => {
  const productMap = {};
  productList.forEach((product) => {
    // product.Buyer_Pricing_CWT = product.Actual_Buyer_Pricing_CWT;
    // product.Buyer_Pricing_Ea = product.Actual_Buyer_Pricing_Ea;
    // product.Buyer_Pricing_Ft = product.Actual_Buyer_Pricing_Ft;
    // product.Buyer_Pricing_LB = product.Actual_Buyer_Pricing_LB;
    // product.Buyer_Pricing_Net_Ton = product.Actual_Buyer_Pricing_Net_Ton;

    // product.Seller_Pricing_CWT = product.Actual_Seller_Pricing_CWT;
    // product.Seller_Pricing_Ea = product.Actual_Seller_Pricing_Ea;
    // product.Seller_Pricing_Ft = product.Actual_Seller_Pricing_Ft;
    // product.Seller_Pricing_LB = product.Actual_Seller_Pricing_LB;
    // product.Seller_Pricing_Net_Ton = product.Actual_Seller_Pricing_Net_Ton;

    // product.Neutral_Pricing_CWT = product.Actual_Neutral_Pricing_CWT;
    // product.Neutral_Pricing_Ea = product.Actual_Neutral_Pricing_Ea;
    // product.Neutral_Pricing_Ft = product.Actual_Neutral_Pricing_Ft;
    // product.Neutral_Pricing_LB = product.Actual_Neutral_Pricing_LB;
    // product.Neutral_Pricing_Net_Ton = product.Actual_Neutral_Pricing_Net_Ton;

    // const spreadRate = !product.is_safe_product_code ? userData.disc_discount_rate : 1;
    // if (userData.disc_is_discounted) {
    //   product.Buyer_Pricing_CWT = "$" + (product[userData.disc_discount_pricing_column + '_CWT'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Ea = "$" + (product[userData.disc_discount_pricing_column + '_Ea'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Ft = "$" + (product[userData.disc_discount_pricing_column + '_Ft'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_LB = "$" + (product[userData.disc_discount_pricing_column + '_LB'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Net_Ton = "$" + (product[userData.disc_discount_pricing_column + '_Net_Ton'].trim().replace(/[\$,]/g, '') * spreadRate);

    // }

    Object.keys(product).forEach(key => {
      if(key.toLowerCase().includes(neutralPricingPrefix)){
        let newKey = key.toLowerCase();
        if(key.toLowerCase().includes(priceUnits.ea)){
          newKey = neutralPricingPrefix + priceUnits.pc;
        }
        product[newKey] = product[key];
      }
      if (key.toLowerCase().includes(orderIncrementPrefix)) {
        let newKey = key.toLowerCase();
        if (key.toLowerCase().includes(priceUnits.ea)) {
          newKey = orderIncrementPrefix + priceUnits.pc;
        }
        product[newKey] = product[key];
        if (newKey !== key) delete product[key];
      }
    });
    product.QUM_Dropdown_Options = product.QUM_Dropdown_Options.toLowerCase().replace(priceUnits.ea, priceUnits.pc);
    product.PUM_Dropdown_Options = product.PUM_Dropdown_Options.toLowerCase().replace(priceUnits.ea, priceUnits.pc);


    productMap[product.Product_ID] = product;
  });
  return productMap;
}

export const updatedAllProductsData = (products: ReferenceDataProduct[]) => {
  if (products?.length) {
    return products.map(product => ({
      ...product,
      Actual_Buyer_Pricing_CWT: product.Buyer_Pricing_CWT,
      Actual_Buyer_Pricing_Ea: product.Buyer_Pricing_Ea,
      Actual_Buyer_Pricing_Ft: product.Buyer_Pricing_Ft,
      Actual_Buyer_Pricing_LB: product.Buyer_Pricing_LB,
      Actual_Buyer_Pricing_Net_Ton: product.Buyer_Pricing_Net_Ton,

      Actual_Seller_Pricing_CWT: product.Seller_Pricing_CWT,
      Actual_Seller_Pricing_Ea: product.Seller_Pricing_Ea,
      Actual_Seller_Pricing_Ft: product.Seller_Pricing_Ft,
      Actual_Seller_Pricing_LB: product.Seller_Pricing_LB,
      Actual_Seller_Pricing_Net_Ton: product.Seller_Pricing_Net_Ton,

      Actual_Neutral_Pricing_CWT: product.Neutral_Pricing_CWT,
      Actual_Neutral_Pricing_Ea: product.Neutral_Pricing_Ea,
      Actual_Neutral_Pricing_Ft: product.Neutral_Pricing_Ft,
      Actual_Neutral_Pricing_LB: product.Neutral_Pricing_LB,
      Actual_Neutral_Pricing_Net_Ton: product.Neutral_Pricing_Net_Ton,
    }));
  }
  return products;
}


export const handleConsoleLog = (component: string, log: string, source) => {
  console.log({
    sourceType:'renderer',
    source,
    location: location.pathname,
    component,
}, log);
}

export const handleConsoleError = (component: string, error, source) => {
  console.error({
    sourceType:'renderer',
    source,
    location: location.pathname,
    component,
}, error);
}

export const handleConsoleWarn = (component: string, warn, source) => {
  console.warn({
    sourceType:'renderer',
    source,
    location: location.pathname,
    component,
}, error);
  // console.warn(` [renderer|${component}|${location.pathname}] `, warn);
}

export const priceFormatter = ( product: ProductPricingModel ) => {
  const { userData }: any = useGlobalStore.getState();
  const { selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
    const decimal2Formater = get2DigitFormater();
    const decimal4Formater = get4DigitFormater();
    const quantityGroup = getQuantityGroup(orderSizeSliderValue);
    let price = +(String(removeCommaFromCurrency((product[`${selectedPriceUnit}_price`])?.replace(dollarSign, ""))) ?? 0);
    const qttyMultiplier = userData.multipliers["" + product.id] ? userData.multipliers["" + product.id][quantityGroup] : userData.multipliers["3010"];
    const regionGroup = getRegionByZip(Number(searchZipCode));
    const regionMultiplier = userData.multipliers["" + product.id] ? userData.multipliers["" + product.id][regionGroup] : userData.multipliers["10010"][regionGroup];
    if (selectedPriceUnit === referenceProductQuantity.lb && !product.is_safe_product_code) {
        return decimal4Formater.format(price ? price * qttyMultiplier * regionMultiplier : 0).replace(dollarSign, "");
    } else {
        return decimal2Formater.format(price ? price * qttyMultiplier * regionMultiplier : 0).replace(dollarSign, "");
    }
};


export const newPriceFormatter = (product: ProductPricingModel) => {
  const productId = product.id;
  const { buyerSetting } = useBuyerSettingStore.getState();
  const decimal2Formater = get2DigitFormater();
  const decimal4Formater = get4DigitFormater();
  const { selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
  if (selectedPriceUnit === referenceProductQuantity.lb && !product.is_safe_product_code) {
    return decimal4Formater.format(product[`${selectedPriceUnit}_price`] ? product[`${selectedPriceUnit}_price`] : 0).replace(dollarSign, "");
  } else {
    return decimal2Formater.format(product[`${selectedPriceUnit}_price`] ? product[`${selectedPriceUnit}_price`] : 0).replace(dollarSign, "");
  }
}


export const  getUserAppData = () => userAppData
export const  setUserAppData = (data) => { 
  userAppData = data 
}

export const stringifyError = (err) => {
  let lastTwoTraces
  let error = err;
  if (err instanceof Error) {
    if (err.stack) {
      const stackLines = err.stack.split("\n");
      lastTwoTraces = stackLines.slice(0, 3).join("\n"); // First line is error message, next two are traces
    }
    error = {
      name: err.name,
      message: err.message,
      stack: lastTwoTraces
    };
  }

  return error
}

export const removeAutoCredential = (channelWindow) => {
  try{
    if(channelWindow?.handleLogout){
      window.electron.send({ channel: channelWindow.handleLogout });
    }
  document.cookie.split(";").forEach((c) => {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
    });
  }catch(err){
    console.error(err);
  }
}

export const clearLocal = (key: string) => {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    console.warn('Could not clear value from localStorage', e);
  }
}

export function setLocal(key: string, value: any) {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (e) {
    console.warn('Could not store value in localStorage', e);
  }
}

export function getLocal<T = any>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (e) {
    console.warn('Could not read value from localStorage', e);
    return defaultValue;
  }
}

export const getFormattedProductPricingForSearchSelection = (product: ReferenceDataProduct) => {
  const lineSessionId = uuidv4();
  const productWithPrice: ProductPricingModel = {
      "id": product.Product_ID,
      "UI_Description": product.UI_Description,
      "cwt_price": product.cwt_price,
      "ft_price": product.ft_price,
      "lb_price": product.lb_price,
      "pc_price": product.pc_price,
      "product_type_pipe": product?.Key2 ? product.Key2 === referenceProductItem.pipe : false,
      "line_session_id": lineSessionId,
      "is_safe_product_code": product.is_safe_product_code,
      "domestic_material_only": product.domestic_material_only,
      // [neutralPricingPrefix+ priceUnits.ea]: Number(product?.[neutralPricingPrefix+ priceUnits.ea]) || '',
      // [neutralPricingPrefix+ priceUnits.cwt]: Number(product?.[neutralPricingPrefix+ priceUnits.cwt]) || '',
      // [neutralPricingPrefix+ priceUnits.ft]: Number(product?.[neutralPricingPrefix+ priceUnits.ft]) || '',
      // [neutralPricingPrefix+ priceUnits.lb]: Number(product?.[neutralPricingPrefix+ priceUnits.lb]) || '',
  }
  Object.keys(priceUnits).forEach((key: any) => {
    if(product?.[neutralPricingPrefix+key]){
      productWithPrice[neutralPricingPrefix+ key] = Number(product?.[neutralPricingPrefix + key]) || '';
    }
  })
  return productWithPrice;
}

export const formatPhoneNumberWithHyphen = (phoneNo: string) => {
  const phoneNumber = phoneNo.replace(/\D/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) {
      return phoneNumber;
  } else if (phoneNumberLength < 7) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;
  } else {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
};

export const unformatPhoneNumber = (formattedPhoneNo: string) => {
  return `+1${formattedPhoneNo.replace(/\D/g, '')}`;
};


export const getHomeRoute = () => {
  const { userData } = useGlobalStore.getState();
  if(userData?.data?.type === "SELLER"){
    return routes.previewOrderPage;
  }else{
    return routes.homePage;
  }
}

export const fetchPrice = async (productData: any, zipCode: string, orderSize: number) => {
    try{
        const productMapping = useGlobalStore.getState().productMapping;
        const setShortListedSearchProductsData = useSearchStore.getState().setShortListedSearchProductsData;
        const shortListedSearchProductsData = useSearchStore.getState().shortListedSearchProductsData;
        const orderSizeSliderValue = useSearchStore.getState().orderSizeSliderValue;
        const buyerSetting = useBuyerSettingStore.getState().buyerSetting;
        const selectedDomesticOption = useSearchStore.getState().selectedDomesticOption;
        const defaultZipCode = buyerSetting?.price_search_zip || '63105';
        const _zipcode = zipCode ? zipCode : defaultZipCode;
        let productList: any = [];
        if (productData?.length > 0) {
            productData?.forEach((product: any) => {
                const productId = product.product_id || product.id;
                const productDataObj = { ...productMapping[productId] };
                productList.push({ ...product, ...productDataObj });
            })
        }
        let productIdList = productList.filter((product: any) => !product.is_safe_product_code).map((product: any) => product.product_id || product.id);
        if (productIdList.length === 0 && !Array.isArray(productData)) productIdList = productData.Product_ID;
        const newPrices = String(productIdList).length ? await getPriceExample(productIdList, _zipcode, Math.floor(orderSize || orderSizeSliderValue)) : {};
        if(Array.isArray(productData)){
            productList = productList.map((product: any) => {
              const productId = product.product_id || product.id;
                Object.keys(priceUnits).forEach((key: any) => {
                    (newPrices?.[productId]?.[key] || product?.[neutralPricingPrefix + key]) &&
                        (product[`${key}_price`] = (product.is_safe_product_code) ? product?.[neutralPricingPrefix + key] : newPrices[productId]?.[key]);
                })
                if(Array.isArray(productData) && product?.price) product[`${productData[0].price_unit}_price`] = parseFloat(product.price.replace(/[$,]/g, ""));
                const productWithPrice = getFormattedProductPricingForSearchSelection(product);
                return productWithPrice;
            })
        }else{
            const spreadProduct = {...productData};
            Object.keys(priceUnits).forEach((key: any) => {
                (newPrices?.[key] || spreadProduct?.[neutralPricingPrefix + key]) &&
                    (spreadProduct[`${key}_price`] = (spreadProduct.is_safe_product_code) ? spreadProduct?.[neutralPricingPrefix + key] : newPrices[key]);
            })
            const productWithPrice = getFormattedProductPricingForSearchSelection(spreadProduct);
            productList = [productWithPrice, ...shortListedSearchProductsData];
        }
        setShortListedSearchProductsData(productList);
        if (selectedDomesticOption) {
            productList = productList.filter(product => product.domestic_material_only);
        }
        return productList;
    }catch(error){
        return [];
    }
}

export const updateSelectedPriceSearchData = (searchProductsData: ProductPricingModel[]) => {
    const selectedSavedSearch = useSearchStore.getState().selectedSavedSearch;
    const savedSearchProducts = useSearchStore.getState().savedSearchProducts;
    const setSelectedSavedSearch = useSearchStore.getState().setSelectedSavedSearch;
    const setSavedSearchProducts = useSearchStore.getState().setSavedSearchProducts;
    const { selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
    const savedSearchData = getLocal(localStorageKeys.instantPriceSearch, null) || selectedSavedSearch;

    if(savedSearchData?.id){
      const updatedSelectedPriceSearchData = {
          ...savedSearchData,
          products: searchProductsData.map((product: ProductPricingModel) => {
              return {
                  product_id: product.id,
                  product_description: product.UI_Description,
                  price: newPriceFormatter(product),
                  price_unit: selectedPriceUnit.toLowerCase(),
                  domestic_material_only: product.domestic_material_only
              }
          }),
          item_count: searchProductsData.length,
          zipcode: searchZipCode,
          order_size: String(orderSizeSliderValue),
          time_stamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      setSelectedSavedSearch(updatedSelectedPriceSearchData);
      setLocal(localStorageKeys.instantPriceSearch, updatedSelectedPriceSearchData);
      const updatedSavedSearchProducts = savedSearchProducts.map((savedSearchProduct: any)=>{
          if(savedSearchProduct?.id === savedSearchData?.id){
              return updatedSelectedPriceSearchData;
          }
          return savedSearchProduct;
      });
      setSavedSearchProducts(updatedSavedSearchProducts);
    }
}

export const getOrderSizeData = (bracketList: any[], orderSize: number) => {
    return bracketList.find((bracket: any, index: number) => {
        // For last bracket, only check min since it's unlimited max
        if (index === bracketList.length - 1) {
            return orderSize >= Number(bracket.min_weight);
        }
        // For other brackets check if order size falls between min and max
        return orderSize >= Number(bracket.min_weight) && orderSize < Number(bracketList[index + 1].min_weight);
    });
}

export const formatOrderSizeToDisplayText = (bracketList: any[], orderSize: number) => {
  const orderSizeData = getOrderSizeData(bracketList, orderSize);
  const formattedText = orderSizeData ? `Based Upon ${Number(orderSizeData?.min_weight) === Number(bracketList[bracketList.length - 1].min_weight) ? Number(orderSizeData?.min_weight).toLocaleString() + '+' : `${Number(orderSizeData?.min_weight).toLocaleString()} to ${Number(orderSizeData?.max_weight).toLocaleString()}`} LBS` : '-';
  return formattedText;
}
export const exportToExcel = async (data: RowData[], filename: string) => {
  const blob = await createExcelBlob(data);
  saveAs(blob, `${sanitizeFilename(filename)}.xlsx`);
}

export function sanitizeFilename(name:string) {
    // Replace disallowed characters: \ / : * ? " < > | and control characters (ASCII < 32)
    const illegalCharsRegex = /[\\\/:\*\?"<>\|\x00-\x1F]/g;

    // Replace disallowed Unicode characters for macOS (e.g., colon)
    const macUnsafe = /[:]/g;

    // Combine and replace with empty string, then replace spaces with underscores
    return name
        .replace(illegalCharsRegex, '')
        .replace(macUnsafe, '')
        .replace(/\s+/g, '_')
        .substring(0, 255); // Truncate to 255 characters (safe cross-platform length)
}

 export const handleSaveSearchProducts = async (priceSearchData: any, saveSearchProductsMutation: any) => {
  const setSelectedProductsData = useSearchStore.getState().setSelectedProductsData;
  const setSaveFeedbackMap = useSearchStore.getState().setSaveFeedbackMap;
  const setFocusSingleProduct = useSearchStore.getState().setFocusSingleProduct;
   try{
      const payload = {
          data: {
              id: priceSearchData?.id.includes(noIdGeneric) ? undefined : priceSearchData?.id,
              title: priceSearchData?.title,
              zipcode: priceSearchData?.zipcode.trim(),
              order_size: String(priceSearchData?.order_size),
              source: "search",
              products: priceSearchData?.products
          }
      }
      const response = await saveSearchProductsMutation(payload as any);
 
      if (response?.data?.data) {
        console.log("Pricing saved successfully");
        setSelectedProductsData([]);
        setSaveFeedbackMap({});
        setFocusSingleProduct({});
        clearLocal(localStorageKeys.instantPriceSearch);
      } else {
        console.error("Failed to save pricing");
      }
   } catch(error){
      console.error('Error saving pricing:', error);
   }
  }

export const convertDateFormat = (input: string) => {
  const targetFormat = 'YYYY-MM-DD HH:mm:ss';

  // Check if input is already in the correct format
  const isAlreadyFormatted = dayjs(input, targetFormat, true).isValid();
  if (isAlreadyFormatted) {
    return input;
  }

  // Clean and parse input like '7/29/2025 @ 7:19am'
  const cleanedInput = input?.replace('@', '').trim() || '';
  const parsedDate = dayjs(cleanedInput, 'M/D/YYYY h:mma');

  // If the date is invalid, return error or fallback
  if (!parsedDate.isValid()) {
    return 'Invalid date';
  }

  // Format to target
  return parsedDate.format(targetFormat);
}


type MatchType = 'contains' | 'startsWith' | 'endsWith' | 'exactMatch';

interface FilterResult<T> {
  filteredObj: T;
  field: string;
}

export function filterObjectsByString<T extends object>(
  data: T[],
  searchString: string,
  matchType: MatchType = 'contains'
): FilterResult<T>[] {
  const lowerSearch = searchString.toLowerCase();

  return data.flatMap((obj, index) => {
    for (const [key, value] of Object.entries(obj)) {
      if (key === 'id') continue;
      const val = String(value).toLowerCase();
      const isMatch =
        (matchType === 'contains' && val.includes(lowerSearch)) ||
        (matchType === 'startsWith' && val.startsWith(lowerSearch)) ||
        (matchType === 'endsWith' && val.endsWith(lowerSearch)) ||
        (matchType === 'exactMatch' && val === lowerSearch);

      if (isMatch) {
        //return [{ filteredObj: obj, field: key, index }];
        console.log(obj.created_date);
        return [{ id: index.toString(), label: obj[key], field: key, index, date: formatDateForGlobalSearch(obj.created_date.split(' ')[0].trim()) }];
      }
    }
    return [];
  });
}


export const formatDateForGlobalSearch = (dateStr: string) => {
  const date = new Date(dateStr);
  
  const weekday = date.toLocaleDateString('en-US', { weekday: 'short' });
  const month = date.getMonth() + 1;
  const day = date.getDate();
  
  const formatted = `${weekday} ${month}/${day}`;
  return formatted;
}

export function flattenObject(
  obj: any,
  prefix = '',
  ignorePatterns: RegExp[] = []
): Record<string, any> {
  let result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    const path = prefix ? `${prefix}.${key}` : key;

    // Check if this path matches any ignore pattern
    if (ignorePatterns.some((regex) => regex.test(path))) {
      continue; // skip this key entirely
    }

    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        const arrayKey = `${path}#${index}`;

        // Skip if arrayKey matches any ignore pattern
        if (ignorePatterns.some((regex) => regex.test(arrayKey))) {
          return; // skip this array element
        }

        if (item !== null && typeof item === 'object') {
          Object.assign(result, flattenObject(item, arrayKey, ignorePatterns));
        } else {
          result[arrayKey] = item;
        }
      });
    } else if (value !== null && typeof value === 'object') {
      Object.assign(result, flattenObject(value, path, ignorePatterns));
    } else {
      result[path] = value;
    }
  }

  return result;
}


export function transformOrderToSheetData(obj) {
  const formatDate = (rawDate) => {
      const date = new Date(rawDate);
      return date.toLocaleString('en-US', {
      weekday: undefined,
      year: 'numeric',
      month: 'long',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      });
  };

  const baseHeaderStyle = {
      b: true,
      backgroundColor: 'FFFFEB9C',
  };

  const baseRowStyle = {
      border: true,
  };

  const result = [];

  // Title
  result.push({
      row: [
      { cellText: 'Title', style: baseHeaderStyle },
      { cellText: obj.buyer_internal_po }
      ],
      style: baseRowStyle,
  });

  // Delivery Date
  result.push({
      row: [
      { cellText: 'Delivery Date', style: baseHeaderStyle },
      { cellText: formatDate(obj.delivery_date) }
      ],
      style: baseRowStyle,
  });

  // Address
  result.push({
      row: [{ cellText: 'Address', style: baseHeaderStyle }],
      style: baseRowStyle,
  });

  ['line1', 'line2', 'city', 'zip'].forEach((field) => {
      result.push({
      row: [
          { cellText: field.charAt(0).toUpperCase() + field.slice(1), style: baseHeaderStyle },
          { cellText: obj.shipping_details[field] }
      ],
      style: baseRowStyle,
      });
  });

  // State
  result.push({
      row: [
      { cellText: 'State', style: baseHeaderStyle },
      { cellText: obj.shipping_details.state_code }
      ],
      style: baseRowStyle,
  });

  // Empty row
  result.push({ row: [{ cellText: ' ' }] });

  // Table Header
  result.push({
      row: [
      { cellText: 'Line #' },
      { cellText: 'Domestic Material' },
      { cellText: 'Description' },
      { cellText: 'Quantity' },
      { cellText: 'Quantity Unit' },
      { cellText: 'Extended Price' },
      ],
      style: {
      b: true,
      backgroundColor: 'FFFFEB9C',
      border: true,
      },
  });

  // Line Items
  obj.cart_items.forEach((item) => {
      result.push({
      row: [
          { cellText: item.line_id },
          { cellText: item.domestic_material_only ? 'Yes' : 'No' },
          { cellText: item.description },
          { cellText: item.qty },
          { cellText: item.qty_unit.toUpperCase() },
          { cellText: item.buyer_line_total },
      ],
      style: baseRowStyle,
      });
  });

  // Empty row
  result.push({ row: [{ cellText: ' ' }] });

  // Total Weight
  result.push({
      row: [
      { cellText: 'Total Weight', style: baseHeaderStyle },
      { cellText: obj.total_weight + ' LBs' },
      ],
      style: baseRowStyle,
  });

  // Material Total
  result.push({
      row: [
      { cellText: 'Material Total', style: baseHeaderStyle },
      { cellText: '$ ' + obj.buyer_po_price },
      ],
      style: baseRowStyle,
  });

  // Sales Tax
  result.push({
      row: [
      { cellText: 'Sales Tax', style: baseHeaderStyle },
      { cellText: '$ ' + obj.sales_tax },
      ],
      style: baseRowStyle,
  });

  // Subscription
  result.push({
      row: [
      { cellText: 'Subscription', style: baseHeaderStyle },
      { cellText: '$ ' + obj.subscription },
      ],
      style: baseRowStyle,
  });

  // Total Purchase
  result.push({
      row: [
      { cellText: 'Total Purchase', style: baseHeaderStyle },
      {
          cellText:
          '$ ' +
          (
              Number(obj.subscription) +
              Number(obj.sales_tax) +
              Number(obj.buyer_po_price)
          ).toFixed(2),
      },
      ],
      style: baseRowStyle,
  });

  return result;
}
export const calculateSubscriptionAmount = (userCount: number = 0, pricingTiers: PricingTier[] | null | undefined): PricingTier | null => {
  if (!pricingTiers?.length) {
      return null;
  }

  try {
      const tier = pricingTiers.find(tier => {
          const isAboveMinimum = tier.min_user_count <= userCount;
          const isBelowMaximum = tier.max_user_count === null || userCount <= tier.max_user_count;
          return isAboveMinimum && isBelowMaximum;
      });

      if (!tier) {
          return null;
      }

      return tier;

  } catch (error) {
      console.error('Error calculating subscription amount:', error);
      return null;
  }
};

export const calculateLineWeight = (data) => {
  let lineWeight = 0;
  const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
  if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0){
      const qtyUnit = data.qty_unit ? data.qty_unit : data.descriptionObj.QUM_Dropdown_Options.split(",")[0];
      const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`]?.replace(/[\$,]/g, '')) || 0
      const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`]?.replace(/[\$,]/g, '')) || 0;
      const lbsPerFt = orderIncrementLb / orderIncrementFt
      const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
      const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${qtyUnit.toLowerCase()}`]?.replace(/[\$,]/g, '')) || 0;
      lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
  }
  return formatToTwoDecimalPlaces(lineWeight);
}
export const formatDraftFinalPayload = (localDraftData: any, draftId: string, initialData: any = null, isConvertingPo: boolean|undefined = undefined) => {
    const retValue = {
      "id": draftId,
      "bom_id": localDraftData.bom_id || undefined,
      "buyer_internal_po": localDraftData.buyer_internal_po || undefined,
      "shipping_details": {
        "zip": localDraftData.shipping_details.zip || undefined,
        "line1": localDraftData.shipping_details.line1 || undefined,
        "line2": localDraftData.shipping_details.line2 || null,
        "city": localDraftData.shipping_details.city || undefined,
        "state_id": localDraftData.shipping_details.state_id || undefined,
        "delivery_address_id": localDraftData.shipping_details.delivery_address_id || undefined
      },
      "buyer_po_price": localDraftData.buyer_po_price || undefined,
      "sales_tax": localDraftData.sales_tax || undefined,
      "freight_term": "Delivered",
      "order_type": localDraftData.order_type || ((location.pathname === routes.createPoPage) ? orderType.PO : orderType.QUOTE),  //change this to 'PO' if on purchasing screen 
      "source": localDraftData.source || null,
      "deposit_amount": localDraftData.deposit_amount || undefined,
      "delivery_date": localDraftData.delivery_date || undefined,
      "total_weight": localDraftData.total_weight || undefined,
      "cart_items": localDraftData.cart_items ? formattedCartItems(localDraftData.cart_items, draftId, initialData) : undefined,
      "payment_method": localDraftData.payment_method || undefined,
      "processing_fee": localDraftData.processing_fee || undefined,
      "is_converting_po": isConvertingPo || undefined
    }

    if (retValue?.cart_items?.length === 0) {
      retValue.cart_items = null;
    }

    return retValue;
}


export const formattedCartItems = (cartItems: any, draftId: boolean | string = false, initialData: any = null) => {
  return cartItems
    .filter((item: any) => (item?.id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED"))
    .map((item, index) => {
      if ((item?.id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED")) {
        const buyerPricingLbKey = `${newPricingPrefix}${priceUnits.lb}`;
        let formattedQtyUnit = null;
        let formattedPriceUnit = null;
        if (draftId && item?.id && !item?.qty_unit) {
          formattedQtyUnit = initialData?.cart_items[index]?.qty_unit
        } else {
          formattedQtyUnit = item?.qty_unit?.toUpperCase();
        }

        if (draftId && item?.id && !item?.price_unit) {
          formattedPriceUnit = initialData?.cart_items[index]?.price_unit
        } else {
          formattedPriceUnit = item?.price_unit?.toUpperCase();
        }
        let priceListObj: any = {};
        if (item?.descriptionObj) {
          Object.keys(item?.descriptionObj).forEach(key => {
            if (key.includes(newPricingPrefix)) {
              priceListObj[key.replace(newPricingPrefix, '').toLowerCase()] = item?.descriptionObj[key];
            }
          })
        }
        const priceList = item?.descriptionObj ? priceListObj : null;
        const cartItem = {
          "line_id": index + 1,
          "description": item?.descriptionObj?.UI_Description ?? null,
          "qty": (draftId && item?.id && !item?.qty && initialData?.cart_items[index]?.qty) ? initialData?.cart_items[index]?.qty : (item?.qty) ? item?.qty : null,
          "qty_unit": formattedQtyUnit || null,
          "product_tag": item?.product_tag ?? null,
          "product_tag_mapping_id": item?.descriptionObj?.product_tag ?? null,
          "buyer_price_per_unit": (draftId && item?.id && !item?.buyer_price_per_unit) ? initialData?.cart_items[index]?.price_per_unit : item?.buyer_price_per_unit ?? null,
          "price_unit": formattedPriceUnit || null,
          "buyer_line_total": item?.buyer_line_total ?? null,
          "product_id": (draftId && item?.id && !item?.descriptionObj?.Product_ID) ? initialData?.cart_items[index]?.product_id : item?.descriptionObj?.Product_ID ?? null,
          "reference_product_id": item?.descriptionObj?.id ?? null,
          "shape": item?.descriptionObj?.Key2 ?? null,
          "domestic_material_only": item?.domesticMaterialOnly ?? false,
          "buyer_calculation_price": item?.buyer_calculation_price ?? null,
          "line_status": item?.line_status ?? null,
          "id": draftId ? item?.id : undefined,
          "is_line_removed": draftId ? (!item?.descriptionObj?.Product_ID ? true : false) : undefined,
          "prices_list": priceList
        };
        return cartItem
      }
      return null
    });
}

export const expiredActivePricedProducts = async (expirePricingMutation: any) => {
    const savedSearchProducts = useSearchStore.getState().savedSearchProducts;
    const activePricedProducts = savedSearchProducts.filter((item: any) => 'pricing_expired' in item && (item.pricing_expired === false || item.pricing_expired === 0));
    if (activePricedProducts.length > 0) {
        const payload = {
            "data": activePricedProducts.map((item: any) => item.id)
        }
        try {
            const res = await expirePricingMutation(payload);
            console.log("res @>>>>>>>", res);
        } catch (error) {
            console.log("error @>>>>>>>", error);
        }
    }
}