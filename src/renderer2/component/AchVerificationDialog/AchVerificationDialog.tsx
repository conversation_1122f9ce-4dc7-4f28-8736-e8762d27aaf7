import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogTitle, TextField, Button, Typography, Box, Radio, RadioGroup, FormControlLabel, FormControl, FormLabel } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import styles from './AchVerificationDialog.module.scss';
import { ReactComponent as CloseIcon } from '../../assets/images/tnc-close.svg';

// Define validation schemas for both verification methods
const descriptorCodeSchema = yup.object().shape({
  descriptorCode: yup
    .string()
    .required('Descriptor code is required')
    .matches(/^[A-Za-z0-9]{6}$/, 'Code must be 6 characters (letters and numbers)')
});

const amountsSchema = yup.object().shape({
  amount1: yup
    .number()
    .required('First amount is required')
    .typeError('Amount must be a number')
    .min(0.01, 'Amount must be greater than 0'),
  amount2: yup
    .number()
    .required('Second amount is required')
    .typeError('Amount must be a number')
    .min(0.01, 'Amount must be greater than 0')
});

interface AchVerificationDialogProps {
  open: boolean;
  onClose: () => void;
  onVerify: (data: any) => void;
}

const AchVerificationDialog: React.FC<AchVerificationDialogProps> = ({ open, onClose, onVerify }) => {
  const [useAmounts, setUseAmounts] = useState(true);

  // Setup form for descriptor code
  const {
    control: descriptorControl,
    handleSubmit: handleDescriptorSubmit,
    formState: { errors: descriptorErrors },
    reset: resetDescriptorForm
  } = useForm({
    resolver: yupResolver(descriptorCodeSchema),
    mode: 'onChange'
  });

  // Setup form for amounts
  const {
    control: amountsControl,
    handleSubmit: handleAmountsSubmit,
    formState: { errors: amountsErrors },
    reset: resetAmountsForm
  } = useForm({
    resolver: yupResolver(amountsSchema),
    mode: 'onChange'
  });

  // Reset forms when dialog opens or closes
  useEffect(() => {
    if (!open) {
      // Reset forms when dialog closes
      resetDescriptorForm();
      resetAmountsForm();
      // Reset to default verification method
      setUseAmounts(true);
    }
  }, [open, resetDescriptorForm, resetAmountsForm]);

  // Handle form reset and dialog close
  const handleDialogClose = () => {
    // Call the parent onClose function
    onClose();
  };

  const handleToggleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUseAmounts(event.target.value === 'amounts');
  };

  const onSubmitDescriptor = (data: any) => {
    onVerify({ type: 'descriptor', ...data });
  };

  const onSubmitAmounts = (data: any) => {
    onVerify({ type: 'amounts', ...data });
  };

  return (
    <Dialog
      open={open}
      onClose={handleDialogClose}
      transitionDuration={200}
      hideBackdrop
      style={{ zIndex: 1000 }}
      sx={{
        '& .MuiBackdrop-root': { zIndex: 999 },
        '& .MuiDialog-paper': { zIndex: 1000 }
      }}
      classes={{
        root: styles.verificationDialog,
        paper: styles.dialogContent
      }}
    >
      <button className={styles.closeIcon} onClick={handleDialogClose}>
        <CloseIcon />
      </button>

      <DialogTitle className={styles.dialogTitle}>
        Verify Your Bank Account
      </DialogTitle>

      <DialogContent className={styles.dialogContentInner}>
        <FormControl component="fieldset" className={styles.toggleContainer}>
          <FormLabel className={styles.radioGroupLabel}>
            Verification Method
          </FormLabel>
          <RadioGroup
            row
            aria-label="verification-method"
            name="verification-method"
            value={useAmounts ? 'amounts' : 'descriptor'}
            onChange={handleToggleChange}
          >
            <FormControlLabel
              value="amounts"
              control={<Radio color="primary" />}
              label="Deposit Amounts"
              className={styles.radioLabel}
            />
            <FormControlLabel
              value="descriptor"
              control={<Radio color="primary" />}
              label="Descriptor Code"
              className={styles.radioLabel}
            />
          </RadioGroup>
        </FormControl>

        {!useAmounts ? (
          <>
            <Typography className={styles.infoText}>
              <span>To verify your bank account:</span>
              <ul>
                <li>Look for two small deposits from Stripe in your bank account.</li>
                <li>Check the "Transaction description" field in your online banking.</li>
                <li>You'll find a label “ACCTVERIFY” followed by a 6-character code (e.g., AB12CD) next to each deposit.</li>
                <li>Enter the code here to complete your verification.</li>
              </ul>
            </Typography>

            <form className={styles.formContainer} onSubmit={handleDescriptorSubmit(onSubmitDescriptor)}>
            <div className={styles.innerForm}>
              <Controller
                name="descriptorCode"
                control={descriptorControl}
                defaultValue=""
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Descriptor Code"
                    variant="outlined"
                    fullWidth
                    error={!!descriptorErrors.descriptorCode}
                    helperText={descriptorErrors.descriptorCode?.message}
                    className={styles.textField}
                    placeholder="Enter 6-character code (e.g., AB12CD)"
                  />
                )}
              />
            </div>
            <div className={styles.submitButton}>
            <Button
                type="submit"
                variant="contained"
                fullWidth
              >
               <span> Verify Account</span>
              </Button>
            </div>

            </form>
          </>
        ) : (
          <>
            <Typography className={styles.infoText}>
              To verify your bank account:
              <ul>
                <li>Look for two small deposits from Stripe in your bank account.</li>
                <li>Enter the amounts sent to your account (e.g., 0.32 & 0.45) to complete your verification.</li>
              </ul>
            </Typography>

            <form className={styles.formContainer} onSubmit={handleAmountsSubmit(onSubmitAmounts)}>
              <div className={styles.innerForm}>
              <div className={styles.amountsContainer}>
                <Controller
                  name="amount1"
                  control={amountsControl}
                  defaultValue={undefined}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="First Amount"
                      variant="outlined"
                      fullWidth
                      error={!!amountsErrors.amount1}
                      helperText={amountsErrors.amount1?.message}
                      className={styles.textField}
                      placeholder="e.g., 0.32"
                      InputProps={{
                        startAdornment: <span className={styles.lblInput}>$</span>,
                      }}
                    />
                  )}
                />

                <Controller
                  name="amount2"
                  control={amountsControl}
                  defaultValue={undefined}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Second Amount"
                      variant="outlined"
                      fullWidth
                      error={!!amountsErrors.amount2}
                      helperText={amountsErrors.amount2?.message}
                      className={styles.textField}
                      placeholder="e.g., 0.45"
                      InputProps={{
                        startAdornment: <span className={styles.lblInput}>$</span>,
                      }}
                    />
                  )}
                />
              </div>
              </div>

              <div className={styles.submitButton}>
              <Button
                type="submit"
                variant="contained"
                fullWidth
              >
                <span>Verify Account</span>
              </Button>
              </div>
            </form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AchVerificationDialog;
