.verificationDialog {
  z-index: 1000 !important; /* Ensure dialog appears above other elements */
  .dialogContent {
    max-width: 500px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    border-radius: 14px;
    box-shadow: 4px 4px 10.1px 1px rgba(0, 0, 0, 0.8);
    background: url(../../assets/New-images/Create-Account/dialogBG.svg) no-repeat;
    font-family: Inter, sans-serif;
    color: #fff;
    background-size: cover;
    position: relative;
    z-index: 1000 !important; /* Ensure dialog content appears above other elements */
  }

  .closeIcon {
    position: absolute;
    top: 10px;
    right: 12px;
    cursor: pointer;
    opacity: 0.5;
    background: transparent;
    border: none;
    padding: 0;

    &:hover,
    &:focus {
      opacity: unset;
    }

    svg {
      height: 20px;
      width: 20px;
      color: white;

      path {
        fill: #fff
      }
    }
  }

  .dialogTitle {
    font-family: Syncopate;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: 2.9px;
    text-transform: uppercase;
    text-align: center;
    color: #fff;
    text-align: center;
    color: #fff;
    width: 100%;
    flex-grow: 0;
    padding: 30px 12px;
    box-shadow: 4px 4px 10.1px 1px rgba(0, 0, 0, 0.8);
    background-image: linear-gradient(289deg, #42454f 122%, #0f0f14 -5%);
  }

  .dialogContentInner.dialogContentInner {
    width: 100%;
    padding: 0px;
  }

  .toggleContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    color: #fff;
    width: 100%;
    padding: 20px;

    .radioGroupLabel {
      font-family: Syncopate;
      color: #fff;
      font-size: 12px;
      margin-bottom: 8px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .radioLabel {
      margin: 0 10px 0px 0px;

      :global(.MuiFormControlLabel-label) {
        color: #fff;
      }

      :global(.MuiRadio-root) {
        color: rgba(255, 255, 255, 0.7);

        &:global(.Mui-checked) {
          color: #43f776;
        }
      }
    }
  }

  .infoText {
    padding: 0px 20px 20px 20px;

    color: #fff;

    span {
      font-family: Syncopate;
      color: #fff;
      font-size: 12px;
      margin-bottom: 8px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      line-height: 1.5;
    }

    ul {
      margin-top: 8px;
      padding-left: 24px;
      min-height: 130px;
    }

    li {
      margin-bottom: 6px;
      font-family: Inter;
      color: #fff;
      font-size: 14px;
      line-height: 1.5;
      letter-spacing: normal;
    }
  }

  .textField.textField {
    margin-bottom: 16px;
    min-height: 80px;

    .lblInput{
      margin-right: 2px;
    }

    :global(.MuiOutlinedInput-root) {
      background-color: rgba(255, 255, 255, 0.04);
      color: #fff;
      font-family: Inter;
    }

    :global(.MuiInputLabel-root) {
      color: rgba(255, 255, 255, 0.67);
    }

    :global(.MuiOutlinedInput-notchedOutline) {
      border-color: rgba(255, 255, 255, 0.23);
    }

    :global(.MuiFormHelperText-root) {
      color: #ff4859;
      margin-left: 0px;
    }
  }


  .formContainer {

    .amountsContainer {
      display: flex;
      gap: 16px;
    }
  }

  .innerForm {
    padding: 0px 20px 20px 20px;
  }


  .submitButton {
    box-shadow: 0 -2px 10.1px 1px rgba(0, 0, 0, 0.34);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;

    button {
      border-radius: 12px;
      background-color: rgba(255, 255, 255, 0.04);
      width: 100%;
      height: 50px;
      font-family: Syncopate;
      font-size: 18.5px;
      font-weight: bold;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: 1pxpx;
      text-align: center;
      span{
        color: rgba(255, 255, 255, 0.4);
        -webkit-text-fill-color:unset
      }
      &:hover{

          background: linear-gradient(236.5deg, rgba(255, 119, 89, 0.1) 0%, rgba(255, 83, 45, 0.1) 100%);
          span{
              background-image: linear-gradient(316deg, #ff7759 130%, #ff532d -17%);
              line-height: 1.3;
              text-align: left;
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
              text-transform: uppercase;
          }
      }

    }
  }

}