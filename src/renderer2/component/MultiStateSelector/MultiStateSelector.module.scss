.multiStateSelector {
  width: 100%;
  background-color: #1a1b1f;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding:10px 10px;
  transition: border-color 0.2s ease;

  &:focus-within {
    border-color: #459fff;
    outline: none;
  }

  &.error {
    border-color: #ff4444;
  }
}

.searchContainer {
  position: relative;
  margin-bottom: 8px;

  .searchInput {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 8px 12px 8px 12px;
    color: #fff;
    font-family: Inter;
    font-size: 14px;
    transition: border-color 0.2s ease;

    &::placeholder{
      text-transform: uppercase;
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }

    &:focus {
      outline: none;
      border-color: #459fff;
    }
  }

  .clearSearch {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.4);
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;

    &:hover {
      color: rgba(255, 255, 255, 0.6);
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.controlButtons {
  display: flex;
  gap: 3px;
  margin-bottom: 6px;
  flex-wrap: wrap;

  .controlButton {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding:8px 6px;
    color: rgba(255, 255, 255, 0.4);
    font-family: Inter;
    font-size: 10px;
    letter-spacing: 0.98px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 0;
    text-transform: uppercase;

    &:hover:not(:disabled) {
      background-color: #459fff;
      border-color: #459fff;
      color: #fff;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

  }
}

.statesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
  gap: 4px;
  max-height: 112px;
  overflow-y: auto;
  margin-bottom: 5px;
  padding: 4px;

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.2);
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

.stateItem {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  padding: 6px 3px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 29px;
  color: rgba(255, 255, 255, 0.4);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.6);
  }

  &.selected {
    background-color: #459fff;
    border-color: #459fff;
    color: #fff;

    &:hover {
      background-color: #3a89e6;
      border-color: #3a89e6;
    }
  }

  .stateCheckbox {
    margin: 0;
    pointer-events: none;
  }

  .stateCode {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    min-width: 20px;
    color: inherit;
  }
}

.selectedCount {
  color: rgba(255, 255, 255, 0.4);
  font-family: Syncopate;
  font-size: 10px;
  text-align: center;
  padding: 4px 0;
  text-transform: uppercase;
}

.noResults {
  color: rgba(255, 255, 255, 0.4);
  font-family: Syncopate;
  font-size: 10px;
  text-align: center;
  padding: 20px;
  font-style: italic;
  text-transform: uppercase;
}

.errorMessage {
  color: #ff4444;
  font-family: Syncopate;
  font-size: 10px;
  margin-top: 8px;
  text-align: center;
}
