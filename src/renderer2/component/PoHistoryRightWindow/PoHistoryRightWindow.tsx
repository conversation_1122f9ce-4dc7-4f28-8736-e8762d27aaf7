import React, { useEffect, useState } from 'react'
import styles from './PoHistoryRightWindow.module.scss'
import clsx from 'clsx'
import { commomKeys, fileType, formatToTwoDecimalPlaces, orderConfirmationConst, prefixUrl, useGlobalStore } from '@bryzos/giss-ui-library'
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore'
import ChatWithVendor from '../ChatWithVendor/ChatWithVendor'
import usePostStartChat from 'src/renderer2/hooks/usePostStartChat'
import useDialogStore from '../DialogPopup/DialogStore'
import { useChatWithVendorStore } from '@bryzos/giss-ui-library';
import { ReactComponent as CloseIcon } from '../../assets/New-images/close-icon.svg';
import usePostBuyerCancelOrder from 'src/renderer2/hooks/usePostBuyerCancelOrder'
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios'
import { downloadFiles } from 'src/renderer2/helper'


const PoHistoryRightWindow = () => {
    const { userData } = useGlobalStore();
    const { props, isAddLineBtnClicked } = useRightWindowStore();
    const { watch, setIsRemoveLineBtnClicked, isRemoveLineBtnClicked, showSuccessBox, setShowSuccessBox, showConfirmationBox, setShowConfirmationBox, saveAddedLine, handleSuccessBoxClose, handleConfirmationBoxClose, handleAddLineBtnClick } = props;
    const [cancelPoNumber, setCancelPoNumber] = useState(null);
    const [isChatWithVendor, setIsChatWithVendor] = useState(false);
    const [uploadPoProgress, setUploadPoProgress] = useState(null);
    const [disableUploadYourPo, setDisableUploadYourPo] = useState(false);
    const { mutateAsync: cancelOrderMutation } = usePostBuyerCancelOrder();

    const {setChannelName, setPoNumber, setCompanyName} = useChatWithVendorStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const [isPoClaimed, setIsPoClaimed] = useState(false);
    const cartItemsLength = watch('cart_items')?.length || 0;
    const isPoClosed = watch('is_closed') || watch('is_closed_buyer') || watch('is_closed_seller');

    useEffect(() => {
        const fullfilledBy = watch('fulfilled_by');
        if(!fullfilledBy || fullfilledBy.trim() === ''){
            setIsPoClaimed(false);
        }else{
            setIsPoClaimed(true);
        }
    }, [watch('fulfilled_by')])

    const cancelOrder = async () => {
        const payload = {
            po_number: watch('buyer_po_number'),
            type: orderConfirmationConst.buyerCancel
        }
        const response = await cancelOrderMutation(payload);
        if(response.error_message){
            console.log("error_message ", response.error_message);
            return;
        }
        setShowConfirmationBox(false);
        setShowSuccessBox(true);
    }

    const handleSuccessBoxCancel = () => {
        setCancelPoNumber(null);
        handleSuccessBoxClose()
    }

    const handleConfirmationBoxCancel = () => {
        setCancelPoNumber(null);
        setShowConfirmationBox(false);
        if(!cancelPoNumber) handleConfirmationBoxClose()
    }

    const handleChatWithVendor = async () => {
        if(!isPoClaimed) return;
        console.log(userData);
        if(userData.data.chat_data.is_ban===true){
            showCommonDialog(null, 'You are not allowed to chat. Please contact support.', null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            return;
        }
        const poNumber = watch('buyer_po_number');
        setCompanyName(watch('fulfilled_by'));
        
        setChannelName(poNumber);
        setPoNumber(poNumber);
        setIsChatWithVendor(true);
    }

    const closeChatWithVendor = () => {
        setIsChatWithVendor(false);
    }

    const handleCancelOrder = () => {
        setCancelPoNumber(watch('buyer_po_number'));
        setShowSuccessBox(false);
        setShowConfirmationBox(true);

    }

    const handleAddLine = () => {
        setShowSuccessBox(false);
        setShowConfirmationBox(false);
        setCancelPoNumber(null);
        handleAddLineBtnClick();
    }
    
    const uploadPoFile = async (event) => {
        const file = event.target.files[0];

        if (event.target.files.length !== 0) {
            setDisableUploadYourPo(true);
            setUploadPoProgress(true);
            let index = file.name.length - 1;
            for (; index >= 0; index--) {
                if (file.name.charAt(index) === '.') {
                    break;
                }
            }
            const ext = file.name.substring(index + 1, file.name.length);

            const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.buyerPo + '-' + uuidv4() + '.' + ext;
            const payload = {
                data: {
                    "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_PO_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 300

                }
            }
            let buyerPoUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;

            axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
                .then(response => {
                    const signedUrl = response.data.data;
                    axios.put(signedUrl, file)
                        .then(response => {
                            if (response.status === 200) {
                                const payload = {
                                    "data": {
                                        "po_number": watch('buyer_po_number'),
                                        "upload_po_s3_urls": [
                                            buyerPoUrl
                                        ]
                                    }
                                }
                                axios.post(import.meta.env.VITE_API_SERVICE + '/user/save/uploadPoS3Url', payload)
                                    .then(response => {
                                        showCommonDialog(commomKeys.uploadSuccessful,orderConfirmationConst.uploadPoDialogContent,commomKeys.actionStatus.success, resetDialogStore, [ {name: commomKeys.successBtnTitle, action: resetDialogStore}])
                                        setUploadPoProgress(false);
                                    })
                                    .catch(err => {
                                        setDisableUploadYourPo(false);
                                        setUploadPoProgress(null);
                                        showCommonDialog(null,commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
                                        console.error(err)
                                    })

                            }
                        })
                        .catch(error => {
                            setDisableUploadYourPo(false);
                            setUploadPoProgress(null);
                            showCommonDialog(null,commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
                            console.error(error)
                        })
                })
                .catch(error => {
                    setDisableUploadYourPo(false);
                    setUploadPoProgress(null);
                    showCommonDialog(null,commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
                    console.error(error)
                })

        }
    }

    const downloadCertificate = async()=>{
        try{
            // setShowLoader(true)
            const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/buyer/w9form')
            if(res.data.data){
                const url = res.data.data
                const fileName = url.split('/')
                await downloadFiles(url, fileName[fileName.length -1], fileType.pdf)
            }
        }catch(e) {
            showCommonDialog(null,commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
        } finally {
            // setShowLoader(false);
        }
    }

    return (
        <>
            <div className={styles.claimOrderRightWindow}>
                <div className={styles.claimOrderRightWindowHeader}>
                    <div className={styles.summarySection}>
                        <div className={styles.summaryRow}>
                            <div className={styles.summaryRowLbl}>Material Total</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('material_total'))}</div>
                        </div>
                        <div className={`${styles.summaryRow} ${styles.muted}`}>
                            <div className={styles.summaryRowLbl}>Sales Tax</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('sales_tax')) || 'Exempt'}</div>
                        </div>
                        <div className={`${styles.summaryRow} ${styles.muted}`}>
                            <div className={styles.summaryRowLbl}>Deposit</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('deposit')) || 'Exempt'}</div>
                        </div>
                        <div className={`${styles.summaryRow} ${styles.muted}`}>
                            <div className={styles.summaryRowLbl}>Subscription</div>
                            <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch('subscription')) || '0.00'}</div>
                        </div>
                    </div>
                    <div className={clsx(styles.summaryRow, styles.totalPurchase)}>
                        <div className={styles.totalPurchaseLbl}>Total Purchase</div>
                        <div className={styles.totalPurchaseNum}>$ {formatToTwoDecimalPlaces(watch('totalPurchase'))}</div>
                    </div>

                </div>
                <div className={styles.claimOrderNote}>
                    Some actions may be unavailable due to the
                    timing of order shipment.
                </div>
                <div className={styles.btnSection}>
                    <div>
                        <button disabled={isPoClosed} onClick={handleAddLine} className={isAddLineBtnClicked ? styles.active : ''}>ADD A LINE</button>
                    </div>
                    <div>
                        <button disabled={cartItemsLength <= 1 || isPoClosed} onClick={() => { setIsRemoveLineBtnClicked(!isRemoveLineBtnClicked) }} className={clsx(styles.removeLine, isRemoveLineBtnClicked ? styles.active : '')}>REMOVE A LINE</button>
                    </div>
                    <div>
                        <button disabled={isPoClosed} onClick={handleCancelOrder} >CANCEL ORDER</button>
                    </div>
                    <div>
                        <button onClick={handleChatWithVendor} disabled = {!isPoClaimed}>CHAT WITH VENDOR</button>
                    </div>
                </div>
            </div>
            {showSuccessBox && (
                <div className={clsx(styles.claimOrderRightWindow,styles.orderUpdatedSuccesWindow)}>
                    <div className={styles.closeIcon} onClick={handleSuccessBoxCancel}><CloseIcon/></div>
                    {cancelPoNumber ? (
                        <div className={styles.orderCancelationReqMain}>
                            <div className={styles.cancelationMessage}>
                                <span>YOUR CANCELATION REQUEST HAS BEEN SUBMITTED</span>
                                <span>We have notified {watch('fulfilled_by')} of your request. We will notify you when they respond.</span>
                            </div>
                            <div className={styles.cancelationActions}>
                                <button disabled className={styles.viewDisputesBtn}>VIEW DISPUTES</button>
                                <button className={styles.btnCancel} onClick={handleSuccessBoxCancel}>CANCEL</button>
                            </div>
                        </div>
                    ) : (
                        <div className={styles.orderUpdatedSuccesfullMain}>
                            <div className={styles.orderUpdatedSuccesBg}>
                                <span className={styles.title}>ORDER UPDATED SUCCESSFULLY</span>
                                <span className={styles.title1}>We have just emailed you the updated order confirmation to <span>{userData?.data?.email_id}</span></span>
                            </div>
                            <span className={styles.orderUpdatedSuccesBg1}>
                                    This order is still actively in the queue for supplier’s to claim for fulfillment. You will be able to chat directly with the supplier on this order once it has been claimed.<br/>
                                    Sit back & relax as your material arrives accurately & on time!
                                </span>
                            <div className={styles.btnSectionOrder}>
                                <label>
                                    <div className={clsx(styles.uploadYourPoDiv, (disableUploadYourPo) && styles.disabled)}>
                                        {uploadPoProgress === true ?
                                            <span className={styles.orderConfirmationButtonText}>Uploading </span> :
                                            uploadPoProgress === false ?
                                                <span className={styles.orderConfirmationButtonText}>PO Uploaded </span> :
                                                <>
                                                    <span className={styles.orderConfirmationButtonText}>Upload Your PO </span>
                                                    <span className={styles.emailIdInter}>or, <NAME_EMAIL></span>
                                                </>
                                        }
                                    </div>
                                    <input type='file' onChange={uploadPoFile} disabled={disableUploadYourPo} />
                                </label>
                                {/* <button className={styles.btn1}>
                                    <span>Upload Your PO</span>
                                    <span>or, <NAME_EMAIL></span>
                                </button> */}
                                <button className={styles.btn2}>
                                    <span>DOWNLOAD</span>
                                    <span>CONFIRMATION & <button onClick={downloadCertificate}>W-9</button></span>
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            )}
            {showConfirmationBox && (  
                <div className={clsx(styles.claimOrderRightWindow,styles.fulfillmentMainWindow)}>
                    {cancelPoNumber ? (
                         <div className={styles.fulfillmentMain}>
                            <span className={styles.fulfillmentNote}>
                                Our records indicate that {watch('fulfilled_by')} has already delivered this order.
                            </span>
                            <span className={styles.fulfillmentNote}>
                                Canceling this order will require a return of material and may incur a restocking fee.
                            </span>
                           <span className={clsx(styles.fulfillmentNote,styles.fulfillmentNote1)}>
                                Upon submission of this cancelation request, {watch('fulfilled_by')} will be notified and must “accept” this change and arrange their material return requirements.
                            </span>
                        </div>

                    ) : (
                        <div className={styles.fulfillmentMain}>
                            <span className={styles.fulfillmentNote}>
                                Your additional material will first be offered to {watch('fulfilled_by')} for fulfillment at ${formatToTwoDecimalPlaces(watch(`cart_items[${watch('cart_items').length - 1}].extended`))}.
                            </span>
                             <span className={styles.fulfillmentNote}>
                                If {watch('fulfilled_by')} is unwilling or unable to fulfill the added line, your material will be presented to the Bryzos supplier network as a standalone item at $150.00.
                            </span>
                             <span className={styles.fulfillmentNote}>
                                Either way, Bryzos is on it. Your order will be fulfilled.
                            </span>
                        </div>
                    )}
                    <div className={styles.btmSection}>
                        <span className={styles.proceedAddLine}>
                            Do you wish to proceed with this {cancelPoNumber ? 'cancelation' : 'added line'}?
                        </span>
                        <button className={styles.btnProceed} onClick={cancelPoNumber ? cancelOrder : saveAddedLine}>
                           <span>PROCEED</span> 
                        </button>
                        <button className={styles.btnCancel} onClick={handleConfirmationBoxCancel}>
                            CANCEL
                        </button>
                    </div>
                </div>
            )}
            {isChatWithVendor && (
                <div className={clsx(styles.claimOrderRightWindow,styles.chatWithVendor)}>
                    <ChatWithVendor
                        close={closeChatWithVendor}
                    />
                </div>
            )}
        </>
    )
}

export default PoHistoryRightWindow