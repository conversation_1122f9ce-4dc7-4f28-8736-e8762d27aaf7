import React, { useEffect } from 'react';
import { useGlobalClockStore } from '@bryzos/giss-ui-library';

interface CountdownTimerProps {
  expiresAt: string; // ISO string or Date string
  onExpire?: () => void;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ expiresAt, onExpire }) => {
  const now = useGlobalClockStore((s) => s.now);
  const expiresAtMs = new Date(expiresAt).getTime();
  const diffMs = expiresAtMs - now;

  useEffect(() => {
    if (diffMs <= 0 && onExpire) {
      onExpire();
    }
  }, [diffMs, onExpire]);

  if (diffMs <= 0) return <span>Expired</span>;

  const totalMinutes = Math.floor(diffMs / 1000 / 60);
  
  // Ensure we never show 0 minutes - show 1 minute minimum when time is still remaining
  const displayMinutes = Math.max(1, totalMinutes);

  let display = '';
  if (displayMinutes === 60) {
    display = '60 minutes';
  } else if (displayMinutes >= 60) {
    const hours = Math.floor(displayMinutes / 60);
    const minutes = displayMinutes % 60;
    display = `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  } else {
    display = `${displayMinutes} minute${displayMinutes !== 1 ? 's' : ''}`;
  }

  return <span>{display}</span>;
};

export default CountdownTimer;
