.container {
  position: absolute;
  width: calc(100% - 18px);
}

.weightDisplay {
  display: flex;
  padding: 2px;
  app-region: drag;
}

.weightValue {
  line-height: 1;
}

.weightValue {
  font-size: 28px;
  font-weight: normal;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  min-width: 59px;

  .weightInfoText {
    font-family: Syncopate;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -0.72px;
    text-align: left;
    color: #fff;
    margin-top: 3px;
  }

}
.weightValueBelowMinimum {
  font-weight: bold;
}

.weightInfo {
  display: flex;
  flex-direction: column;
}


.weightMinimum,
.weightOrder {
  font-family: Syncopate;
  font-size: 10.6px;
  font-weight: bold;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: -0.1px;
  text-align: left;
  color: #fff;
}


.showMininumWeightImg {
  // Recreate exact RedShape.svg using CSS
  position: relative;
  padding: 0px 5px;
  width: calc(100% - 20px);
  height: 56px;
  box-sizing: border-box;

  // // Create the exact curved shape using clip-path
  // clip-path: polygon(
  //   3.8% 0%,     /* Top-left rounded start */
  //   98.5% 0%,    /* Top-right straight */
  //   100% 7.1%,   /* Right side curve start */
  //   100% 8.9%,   /* Right side curve */
  //   57.5% 74.1%, /* Curve down to middle-right */
  //   42.5% 73.2%, /* Bottom curve middle */
  //   26.3% 96.4%, /* Bottom curve left */
  //   1.6% 99.1%,  /* Bottom-left curve */
  //   0% 92.9%,    /* Left side curve */
  //   0% 17.9%     /* Left side rounded corner */
  // );
    background: linear-gradient(164deg, #ea0000e3 2.5%, #F9052C 8%, rgba(249, 5, 5, 0.845) 47.5%, rgba(249, 5, 5, 0.44) 74.9%, #222329 100%);
    clip-path: polygon(-0.2% 0%, 141.5% 0%, 103% 4.1%, 100% 6.9%, 55.5% 56.1%, 40.5% 73.2%, 26.3% 90.4%, -1.4% 99.1%, 0% 91.9%, 0% 6.9%);
    box-shadow: inset 0.125rem 0.125rem 0.5375rem rgba(255, 255, 255, 0.49), 0 0.125rem 0.25rem rgba(249, 5, 5, 0.3);
    border-radius: 12px;
}
.content {
  width: 100%; // Use full width instead of fixed 270px
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: bold;
  position: relative;
  box-sizing: border-box; // Ensure proper width calculation

  // Enhanced styling when red warning is active
  &.showMininumWeightImg {
    z-index: 1; // Ensure content appears above pseudo-elements

    .weightValue,
    .weightInfo {
      position: relative;
      z-index: 2; // Ensure text appears above background effects
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); // Add text shadow for better readability
    }
  }

  .weight {
    font-size: 24px;
  }

  .min-order {
    font-size: 12px;
    text-transform: uppercase;
  }
}