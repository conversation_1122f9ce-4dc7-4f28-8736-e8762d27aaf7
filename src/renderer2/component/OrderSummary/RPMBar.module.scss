.rpmBarContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  // Allow flexible width for full width usage
  flex: 1;
  &:nth-child(1){
    .bar {
      height: 6px;
      clip-path: polygon(0% 7%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(2){
    .bar {
      height: 8px;
      clip-path: polygon(0% 14%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(3){
    .bar {
      height: 10px;
      clip-path: polygon(0% 16%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(4){
    .bar {
      height: 13px;
      clip-path: polygon(0% 18%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(5){
    .bar {
      height: 18px;
      clip-path: polygon(0% 20%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(6){
    .bar {
      height: 24px;
      clip-path: polygon(0% 19%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(7){
    .bar {
      height: 31px;
      clip-path: polygon(0% 16%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(8){
    .bar {
      height: 37px;
      clip-path: polygon(0% 13%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(9){
    .bar {
      height: 42px;
      clip-path: polygon(0% 10%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(10){
    .bar {
      height: 47px;
      clip-path: polygon(0% 8%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(11){
    .bar {
      height: 52px;
      clip-path: polygon(0% 7%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
  &:nth-child(12){
    .bar {
      height: 58px;
      clip-path: polygon(0% 7%, 107% 0%, 166% 100%, 0% 100%);
    }
  }
}

.bar {
  transition: all 0.2s;
  display: flex;
  width: 100%; // Use full available width instead of fixed 17px
  min-width: 17px; // Maintain minimum width for visual consistency
  flex-grow: 1; // Allow growth to fill available space
  border-radius: 1.5px;
  background-color: #191a20;
}

.barPart {
  height: 100%;
  width: 25%;
  transition: all 0.2s;
  background-color: #191a20;
  
  &:nth-child(1){
    border-radius:0px 0px 0px 2px;
  }
  &:last-child{
    border-radius:0px 2px 2px 0px;
  }
}

// Gradient classes
.gradientRed {
  background: linear-gradient(to top, #f00, #ef4444);
}

.gradientOrange {
  background: linear-gradient(to top, #ff8e1c, #fb923c);
}

.gradientYellow {
  background: linear-gradient(to top, #ffe019, #fde047);
}

.gradientGreen {
  background: linear-gradient(to top, #c1f035, #00ff48);
} 