import { create } from 'zustand';

interface HoverVideoState {
  VideoToolTipConfig:null;
  videoToolTipData: any;
  isHoverVideoEnabled: boolean;
  setVideoToolTipData: (videoToolTipData: any) => void;
  setVideoToolTipConfig: (VideoToolTipConfig: any) => void;
  toggleHoverVideo: () => void;
}

export const useHoverVideoStore = create<HoverVideoState>((set) => ({
  isHoverVideoEnabled: false,
  VideoToolTipConfig: null,
  videoToolTipData: null,
  setVideoToolTipData: (videoToolTipData: any) => set({ videoToolTipData }),
  setVideoToolTipConfig: (VideoToolTipConfig: any) => set({ VideoToolTipConfig }),
  toggleHoverVideo: () => set((state) => ({ isHoverVideoEnabled: !state.isHoverVideoEnabled })),
})); 