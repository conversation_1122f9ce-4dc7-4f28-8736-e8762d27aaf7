// @ts-nocheck
import React, { useState, useRef, useEffect } from 'react'
import styles from './SearchHistoryWindow.module.scss'
import { ReactComponent as SearchIcon } from '../../assets/New-images/Search.svg';
import { ReactComponent as EditIcon } from '../../assets/New-images/Edit.svg';
import { ReactComponent as DustbinIcon } from '../../assets/New-images/dustbin-icon.svg';
import { ReactComponent as CheckIcon } from '../../assets/New-images/check-icon.svg';
import { ReactComponent as CancelIcon } from '../../assets/New-images/cancel-icon.svg';
import { ReactComponent as BackIcon } from '../../assets/New-images/Create-Account/arrow-left.svg';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import clsx from 'clsx';
import useGetSaveSearchProducts from '../../hooks/useGetSaveSearchProducts';
import Loader from 'src/renderer2/Loader/Loader';
import usePostSaveSearchProducts from 'src/renderer2/hooks/usePostSaveSearchProducts';
import useDeleteSearchProducts from 'src/renderer2/hooks/useDeleteSearchProducts';
import { Tooltip, Fade } from '@mui/material';
import { formatNumericValue } from 'src/renderer2/common';
import { commomKeys, emojiRemoverRegex, formatDollarPerUnit, priceUnits } from '@bryzos/giss-ui-library';
import useDialogStore from '../DialogPopup/DialogStore';


interface HistoryItem {
  title: string;
  date: string;
  itemCount: number;
  zip: string;
  weight: string;
  id: string;
  items?: {
    product_id: number;
    product_description: string;
    price: number;
    price_unit: string;
  }[];
}

interface PriceItem {
  id: number;
  title: string;
  dimensions: string;
  itemNumber: string;
  weight: string;
  price: number;
}

const SearchHistoryWindow = () => {
  const { setIsPriceSearchHistory } = useRightWindowStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const [searchValue, setSearchValue] = useState<string>('');
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState<string>('');
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const editInputRef = useRef<HTMLInputElement>(null);
  
  // Initialize history items from data
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  
  // Use the updated useQuery hook
  const { 
    data: searchProductsData, 
    isLoading, 
    error,
    refetch: refetchHistory
  } = useGetSaveSearchProducts();

  const { mutateAsync: saveSearchProductsMutation } = usePostSaveSearchProducts();
  const { mutateAsync: deleteSearchProductsMutation } = useDeleteSearchProducts();

  useEffect(() => {
    setIsPriceSearchHistory(true);

    return () => {
      setIsPriceSearchHistory(false);
    }
  }, [])

  // Update historyItems whenever data changes
  useEffect(() => {
    if (searchProductsData?.data?.data && Array.isArray(searchProductsData.data.data)) {
      setHistoryItems(searchProductsData.data.data);
    } else {
      setHistoryItems([]);
    }
  }, [searchProductsData, isLoading]);

  // Focus the edit input when entering edit mode
  useEffect(() => {
    if (editingItemId && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editingItemId]);

  // Filter history items based on search
  const filteredItems = searchValue 
    ? historyItems.filter(item => {
        const title = item.title || '';
        const date = item.search_date_time || '';
        const zip = item.zipcode || '';
        const weight = item.order_size || 0;
        const count = item.item_count || 0;
        
        return title.toLowerCase().includes(searchValue.toLowerCase()) ||
          date.toLowerCase().includes(searchValue.toLowerCase()) ||
          zip.toLowerCase().includes(searchValue.toLowerCase()) ||
          weight.toString().toLowerCase().includes(searchValue.toLowerCase()) ||
          count.toString().includes(searchValue);
      })
    : historyItems;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleItemClick = (item: HistoryItem) => {
    // Don't do anything if we're in edit mode
    if (editingItemId) return;
    
    // Set the selected item to show detailed view
    setSelectedItem(item);
  };

  const handleBackClick = () => {
    setSelectedItem(null);
  };

  const handleEditClick = (e: React.MouseEvent, item: HistoryItem) => {
    e.stopPropagation(); // Prevent triggering the item click
    setEditingItemId(item.id);
    setEditTitle(item.title);
  };
  
  const handleEditTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.target.value = e.target.value.replace(emojiRemoverRegex, '');
    setEditTitle(e.target.value);
  };
  
  const handleCancelEdit = () => {
    setEditingItemId(null);
    setEditTitle('');
  };
  
  const handleSaveEdit = () => {
    if (editingItemId) {
      let updatedItem = {};
      // Update the history items with the new title
      const updatedItems = historyItems.map(item =>{
        if(item.id === editingItemId){
          item = { ...item, title: editTitle.trim() || 'Untitled' } 
          updatedItem = item;
        }
        return item;
      });
      setHistoryItems(updatedItems);
      console.log('updatedItem ', updatedItem);
      const payload = {
        data: updatedItem
      };
      saveSearchProductsMutation(payload);
      setEditingItemId(null);
      setEditTitle('');
    }
  };

  // Handle keypress in edit mode
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const handleDeleteClick = (e: React.MouseEvent, item: HistoryItem) => {
    e.stopPropagation(); // Prevent triggering the item click
    setShowDeleteConfirm(item.id);
  };

  const handleConfirmDelete = async () => {
    if (showDeleteConfirm) {
      try {
        const payload = {
          data: {
            id: showDeleteConfirm
          }
        };
        await deleteSearchProductsMutation(payload);
        // Remove the item from local state
        setHistoryItems(prev => prev.filter(item => item.id !== showDeleteConfirm));
        setShowDeleteConfirm(null);
        
        // If the deleted item was selected, go back to list view
        if (selectedItem?.id === showDeleteConfirm) {
          setSelectedItem(null);
        }
      } catch (error) {
        console.error('Failed to delete item:', error);
        showCommonDialog(null,commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])

        // You might want to show an error toast here
      }
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  const handleMouseLeave = () => {
    // Hide delete confirmation when mouse leaves the item
    if (showDeleteConfirm) {
      setShowDeleteConfirm(null);
    }
  };

  const renderHistoryList = () => (
    <>
     <div className={styles.headerMain}>
      <div className={styles.header}>PRICE SEARCH HISTORY</div>
      
      <div className={styles.searchBox}>
        <div className={styles.searchIcon}><SearchIcon/></div>
        <input type="text" className={styles.searchInput} placeholder="Search History" value={searchValue}onChange={handleSearchChange} disabled={!!selectedItem}/>
      </div>
      </div>
      <div className={styles.historyList}>
        <div className={styles.historyItemList}>
          {isLoading ? (
            <div className={styles.loading}><Loader/></div>
          ) : error ? (
            <div className={styles.error}>{'Failed to load history'}</div>
          ) : filteredItems.length > 0 ? (
            filteredItems.map((item) => (
              <div 
                key={item.id} 
                className={`${styles.historyItem} ${editingItemId === item.id ? styles.editMode : ''}`}
                onClick={() => handleItemClick(item)}
                onMouseLeave={handleMouseLeave}
              >
                {editingItemId === item.id ? (
                  <>
                    <div className={styles.editTitleRow}>
                      <input
                        ref={editInputRef}
                        type="text"
                        className={styles.editTitleInput}
                        value={editTitle}
                        onChange={handleEditTitleChange}
                        onKeyDown={handleKeyDown}
                        maxLength={30}
                      />
                      <div className={styles.editButtons}>
                        <button 
                          className={styles.editButton} 
                          onClick={handleCancelEdit}
                        >
                          Cancel
                        </button>
                        <button 
                          className={`${styles.editButton} ${styles.saveButton}`} 
                          onClick={handleSaveEdit}
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className={styles.historyItemTitle}>
                      <span className={styles.historyItemTitleText}>
                        <span>{item.title}</span>
                      </span>
                      <span
                        className={styles.editIcon}
                        onClick={(e) => handleEditClick(e, item)}
                      >
                        <EditIcon />
                      </span>
                    </div>
                  </>
                )}
                <div className={styles.historyItemDetails}>
                  <div className={styles.detailsRow}>
                    <span>{item.search_date_time}</span>
                  </div>
                  <div className={clsx(styles.detailsRow,styles.detailsRowGrid)}>
                    <span>{item.item_count} Items</span>
                    <span>Zip {item.zipcode}</span>
                  </div>
                  <div className={clsx(styles.detailsRow,styles.detailsRowGrid)}>
                    <span>Based Upon</span>
                    <span>{formatNumericValue(item.order_size, 4)} LBS</span>
                  </div>
                </div>
                
                {/* Single dustbin icon that works for both normal and edit modes */}
                <span 
                  className={styles.dustbinIcon} 
                  onClick={(e) => handleDeleteClick(e, item)}
                >
                  <DustbinIcon/>
                </span>
                {showDeleteConfirm === item.id && (
                  <div className={styles.deleteConfirmationIcons}>
                    <span 
                      className={styles.confirmDeleteIcon} 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleConfirmDelete();
                      }}
                    >
                      <CheckIcon/>
                    </span>
                    <span 
                      className={styles.cancelDeleteIcon} 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCancelDelete();
                      }}
                    >
                      <CancelIcon/>
                    </span>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className={styles.noResults}>No matching results</div>
          )}
        </div>
      </div>
    </>
   
  );

  const renderDetailView = () => {
    if (!selectedItem) return null;

    return (
      <>
        <div className={styles.headerMain}>
      <div className={styles.header}>PRICE SEARCH HISTORY</div>
      
      <div className={styles.searchBox}>
        <div className={styles.searchIcon}><SearchIcon/></div>
        <input type="text" className={styles.searchInput} placeholder="Search History" value={searchValue}onChange={handleSearchChange} disabled={!!selectedItem}/>
      </div>
      </div>
        <div className={styles.backButton} onClick={handleBackClick}>
          <BackIcon/> Back to Search History
        </div>

        <div className={styles.historyItemExpanded}>
          <div className={styles.expandedHistoryItemTitle}>
            <Tooltip
              title={selectedItem.title}
              placement="top-start"
              TransitionComponent={Fade}
              TransitionProps={{ timeout: 200 }}
              classes={{
                tooltip: styles.emailTooltip
              }}
            >
              <span> {selectedItem.title}</span>
            </Tooltip>
          </div>
          <div className={styles.historyItemDetails}>
          <div className={styles.detailsRow}>
              <span>{selectedItem.search_date_time}</span>
              <span>Zip {selectedItem.zipcode}</span>
            </div>
            <div className={styles.detailsRow}>
              <span>{selectedItem.item_count} Items</span>
              <span>Based Upon {formatNumericValue(selectedItem.order_size, 4)} LBS</span>
            </div>
          </div>
        </div>

        <div className={styles.priceList}>
          <div className={styles.priceItemList}>
            <div className={styles.priceItem}>
            {selectedItem.products && selectedItem.products.map((item, i) => (<div key={item.product_id} className={styles.priceItemContent}>
              <span className={clsx(styles.productDescription1,item.product_description.split('\n')[0]?.includes('Miscellaneous') && styles.miscDesc)}>{item.product_description.split('\n')[0]}</span>
                <div className={styles.priceRow}>
                  <div className={styles.priceItemTitle}>
                    {item.product_description.split('\n').slice(1).map((line, index) => (
                      <div key={index} className={styles.priceItemDetails}>
                        {line}
                      </div>
                    ))}
                  </div>
                  <div className={styles.priceValue}>
                    <div className={styles.price}><span className={styles.textStyle1}>$</span><span>{formatDollarPerUnit(item.price_unit.toLowerCase(),item.price.replace(/[\$,]/g, ""), i)}</span></div>
                    <div className={styles.priceUnit}>PER <span>{item.price_unit.toUpperCase()}</span></div>
                  </div>
                </div>
            </div>
                
            ))}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className={styles.priceSearchHistory}>
      {selectedItem ? renderDetailView() : renderHistoryList()}
    </div>
  );
}

export default SearchHistoryWindow;