import React, { useCallback, useMemo, useState } from 'react'
import { Select, MenuItem, FormControl, Typography, Box, Button, ClickAwayListener } from '@mui/material'
import styles from './SubscriptionRightSidePanel.module.scss'
import InputWrapper from '../InputWrapper'
import CustomTextField from '../CustomTextField'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import { subscriptionPaymentSchema } from './subscriptionPaymentSchema'
import clsx from 'clsx'
import { buyerSettingConst, commomKeys, formatCurrencyWithComma, formatEIN, formatToTwoDecimalPlaces, trueVaultRaygunError, useGlobalStore } from '@bryzos/giss-ui-library'
import { dispatchRaygunError, removeCommaFromCurrency } from 'src/renderer2/helper'
import axios from 'axios'
import TrueVaultClient from 'truevault';
import useDialogStore from '../DialogPopup/DialogStore'
import { CardCvcElement, CardExpiryElement, CardNumberElement, useElements, useStripe } from '@stripe/react-stripe-js'
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode'
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment'

const stripeElementsStyle = {
  base: {
    color: '#fff',
    fontFamily: 'Inter, sans-serif',
    fontSize: '18px',
    '::placeholder': {
      color: "rgba(255, 255, 255, 0.2)",
    },
    padding: '6px 16px',
  },
  invalid: {
    color: '#ff6b6b',
    iconColor: '#ff6b6b',
  },
};

const SubscriptionRightSidePanel = ({ closeRightSidePanel }: { closeRightSidePanel: () => void }) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid },
    getFieldState,
  } = useForm({
    resolver: yupResolver(subscriptionPaymentSchema),
    mode: 'onBlur',
  });
  const [paymentMethod, setPaymentMethod] = useState<string>('')
  const [editModeDesiredCreditLine, setEditModeDesiredCreditLine] = useState(false);
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();
  const { userData, setShowLoader }: any = useGlobalStore();
  const [isNet30TermsApplied, setIsNet30TermsApplied] = useState(false);
  const [showSalesScreen, setShowSalesScreen] = useState(false);
  const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();


  const [stripeError, setStripeError] = useState<{
    cardNumber: string | null,
    cardExpiry: string | null,
    cardCvv: string | null,
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvv: null,
  });
  const [cardBrand, setCardBrand] = useState<string | null>(null);

  const stripe = useStripe();
  const elements = useElements();
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false,
    cardExpiry: false,
    cardCvv: false
  });
  const [isCreditCardDirty, setIsCreditCardDirty] = useState<boolean>(false);

  const enableSaveCardInfo = useMemo(() => {
    return cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvv && !stripeError.cardNumber && !stripeError.cardExpiry && !stripeError.cardCvv && watch('billingZipCode') && !errors.billingZipCode && isCreditCardDirty;
  }, [cardComplete, stripeError, errors, watch('billingZipCode'), isCreditCardDirty]);

  const {
    mutateAsync: buyerSettingsPayment
  } = usePostBuyerSettingsPayment();

  const handlePaymentMethodChange = (event: any) => {
    setPaymentMethod(event.target.value)
  }

  const handleDoLater = () => {
    closeRightSidePanel()
  }

  const handleEinNoChange = (e: any) => {
    const { value } = e.target;
    setValue('einNumber', formatEIN(value));
  }

  const requestCreditLineChangeHandler = (e: any, fieldName: string) => {
    let value = sanitizeNumberInput(e.target.value);
    clearErrors(fieldName);

    if (!isNaN(removeCommaFromCurrency(value))) {
      setValue(fieldName, removeCommaFromCurrency(value));
    }
  }

  // Function to sanitize input - removes negative signs, spaces, and keeps only numbers
  const sanitizeNumberInput = (value: string): string => {
    // Remove negative signs, spaces, and keep only digits and decimal points
    return value.replace(/[-\s]/g, '').replace(/[^0-9.]/g, '');
  }

  const isBnplSubmitDisabled = watch('dnBNumber') && watch('creditLine') && watch('einNumber');

  const bnplSetupValidate = async () => {
    if (!watch('dnBNumber') || !watch('creditLine') || !watch('einNumber')) {
      const net30Fields = ['creditLine', 'dnBNumber', 'einNumber'];
      for (let trial in net30Fields) {
        if (watch(net30Fields[trial]) === '') {
          setError(net30Fields[trial], { message: `Net 30 Terms is not valid` });
        }
      }

    } else {
      const isValid = await trigger(['dnBNumber', 'creditLine', 'einNumber']);
      if (!isValid) {
        return;
      }
      if (Number(watch('creditLine')) <= 0) {
        setError('creditLine', { message: `Net 30 Terms is not valid` });
        return;
      }
      if (Number(watch('creditLine')) > buyerSettingConst.buyerCreditLineLimit) {
        setError('creditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
        return
      }
      clearErrors(['dnBNumber', 'creditLine', 'einNumber']);
      applyNetData();
    }
  }

  const applyNetData = () => {
    // setValidationInProgress({
    //   ...validationInProgress,
    //   submitBnpl: true
    // });
    getTruevaultData(getValues('parentCompanyName'), userData.data.id, null, null, null, getValues('einNumber'), getValues('dnBNumber')).then(documentIdFromTruevault => {
      const payload = {
        "data": {
          "ein_number": getValues('einNumber').slice(-2).padStart(getValues('einNumber').length, 'x'),
          "duns_number": getValues('dnBNumber'),
          "agreed_terms": true,
          "desired_credit_limit": getValues('creditLine'),
          "pgpm_mapping_id": 4,
          "reference_document_id": documentIdFromTruevault ? documentIdFromTruevault : ''
        }
      };

      axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveBuyNowPayLaterData', payload, {
        headers: {
          UserId: userData.data.id
        }
      })
        .then(res => {
          if (res.status === 200) {
            // setBnplStatus('PENDING');
            // setValidationInProgress({
            //   ...validationInProgress,
            //   submitBnpl: false
            // });
            // setSuccessAppSubmit(true)
            // setNet30ApplyStatus(true);
            setIsNet30TermsApplied(true)
          } else if (res?.data?.data.error_message) {
            showCommonDialog(
              null,
              commomKeys.errorContent,
              commomKeys.actionStatus.error,
              resetDialogStore,
              [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
            );
          }
        })
        .catch(err => {
          console.error(err);
          // setValidationInProgress({
          //   ...validationInProgress,
          //   submitBnpl: false
          // });
          showCommonDialog(
            null,
            commomKeys.errorContent,
            commomKeys.actionStatus.error,
            resetDialogStore,
            [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
          );
          // setApiFailureDialog(true);
        });
    })

  }

  const getTruevaultData = async (companyName, userData, bankName, routingNo, accountNo, einNumber, dnBNumber) => {
    try {
      const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken');
      const accessToken = res.data.data;
      let buyerPaymentData = {};
      buyerPaymentData = {
        "document": {
          "company_name": companyName,
          "user_id": userData,
          "dnb_number": dnBNumber,
          "net_30_ein": einNumber,
          "payment_method": paymentMethod
        }
      }
      const client = new TrueVaultClient({ accessToken });

      try {
        const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_BUYER_VAULT_ID, null, buyerPaymentData);
        const documentIdFromTruevault = response.id;
        if (!documentIdFromTruevault) {
          dispatchRaygunError(new Error('TrueVault error: TruevoltObject = ' + JSON.stringify(response)), [trueVaultRaygunError]);
        }
        return documentIdFromTruevault;

      } catch (error) {
        console.error("Error creating document:", error);
        dispatchRaygunError(error, [trueVaultRaygunError]);
        // setValidationInProgress({
        //   ...validationInProgress,
        //   submitBnpl: false
        // });
      }

    } catch (err) {
      console.error(err)
    }

  }

  const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
    // setIsCardDetailsRequired(true);
    setCardComplete(prev => ({
      ...prev,
      [fieldName]: event.complete
    }));
    setIsCreditCardDirty(true);

    // Capture card brand when it's a card number element
    if (fieldName === 'cardNumber' && event.brand && event.brand !== 'unknown') {
      setCardBrand(event.brand);
      setValue('cardType', event.brand.toUpperCase());
    }

    // Set or clear error based on the event
    if (event.error || (!event.empty && !event.complete)) {
      setStripeError(prev => ({
        ...prev,
        [fieldName]: event.error?.message ?? 'Incomplete card details'
      }));
    } else {
      setStripeError(prev => ({
        ...prev,
        [fieldName]: null
      }));
    }
  }, [stripeError, setValue]);

  const handleZipValidation = async (): Promise<any> => {
    if (watch('billingZipCode') && watch('billingZipCode').trim() !== '' && watch('billingZipCode').trim().length > 4) {
      try {
        const res = await verifyZipCode({ zip_code: watch('billingZipCode') });
        if (res) {
          return true;
        } else {
          setError('billingZipCode', { message: 'Invalid zipcode' });
          return false;
        }
      } catch (error) {
        console.log('error', error);
        setError('billingZipCode', { message: 'Invalid zipcode' });
        return false;
      }
    }
  };


  const handleCardSubmit = async () => {
    try {
      const isZipValid = await handleZipValidation();
      if (!stripeError.cardNumber && !stripeError.cardExpiry && !stripeError.cardCvv && isZipValid) {
        setShowLoader(true);
        if (!stripe || !elements) {
          // Stripe.js hasn't loaded yet
          setStripeError("Stripe hasn't loaded yet. Please try again.");
          setShowLoader(false);
          return;
        }
        let _paymentMethod;
        const cardElement = elements?.getElement(CardNumberElement);
        if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvv && watch('billingZipCode')) {
          const { paymentMethod, error: paymentMethodError }: any = await stripe?.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
              email: watch('cardEmailId'),
              name: `${watch('cardFirstName')} ${watch('cardLastName')}`,
              address: {
                country: 'US',
                postal_code: watch('billingZipCode'),
              }
            }
          });
          if (paymentMethodError) {
            setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
            setShowLoader(false);
            return;
          }
          else {
            _paymentMethod = paymentMethod;
          }
        }

        const payload = {
          "data": {
            "payment_method": "card",
            "payment_details": {
              "zipcode": watch('billingZipCode'),
              "payment_method_id": _paymentMethod?.id ? _paymentMethod.id : undefined
            }
          }
        }
        await buyerSettingsPayment(payload);
        setIsCreditCardDirty(false);
      }

      let response;
      // if (userSubscription.active_customer || isUpdatePaymentModule) {
      //   response = await updateUserPayment(payload);
      //   setSubscriptionStatus(true);
      // } else {
      //   response = await saveUserSubscription(payload);
      //   if (!response || !response?.client_secret) {
      //     setStripeError('No client secret received from server');
      //     setShowLoader(false);
      //     return;
      //   }

      //   const { client_secret: clientSecret } = response;
      //   const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(clientSecret);
      //   if (confirmError) {
      //     setStripeError(confirmError.message || 'An error occurred during payment processing');
      //   } else if (paymentIntent.status === 'succeeded') {

      //   }
      // }
    } catch (error: unknown) {
      const err = error as Error;
      setStripeError(prev => ({
        ...prev,
        cardNumber: err.message || 'An error occurred during payment processing'
      }));
      showCommonDialog(
        null,
        err.message || 'An error occurred during payment processing',
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
    } finally {
      setShowLoader(false);
    }
  }



  console.log("errors", errors);
  console.log("watch", watch());


  return (
    <div className={styles.subscriptionRightSidePanel}>
      <div className={styles.header}>
        <Typography variant="h5" className={styles.headerText}>
          {
            showSalesScreen ? (
              'Sales'
            ) : (
              'BUYING METAL'
            )
          }
        </Typography>
      </div>

      {
        showSalesScreen ? (
          <>
            <div className={styles.content}>
              <div className={styles.textContent}>
                <Typography variant="body1" className={styles.paragraph}>
                  If your company has sales tax exemption certificates or “certs,” you can upload them now:                </Typography>
              </div>
            </div>
          </>
        ) : (
          <div className={styles.content}>
            <div className={styles.textContent}>
              <Typography variant="body1" className={styles.paragraph}>
                If you intend to purchase metal on Bryzos, versus using it for estimating only, you will need to setup a method for those purchases.
              </Typography>

              <Typography variant="body1" className={styles.paragraph}>
                You do not have to do this right now, but it will need to be done.
              </Typography>

              <Typography variant="body1" className={styles.paragraph}>
                If you'd like to setup your method of payment now, complete the information below:
              </Typography>
            </div>

            <div className={styles.dropdownSection}>
              <FormControl fullWidth className={styles.formControl}>
                <Select
                  value={paymentMethod}
                  onChange={handlePaymentMethodChange}
                  displayEmpty
                  className={styles.select}
                  MenuProps={{
                    PaperProps: {
                      className: styles.menuPaper
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    <Typography className={styles.placeholderText}>
                      METHOD OF PAYMENT
                    </Typography>
                  </MenuItem>
                  <MenuItem value="net30">
                    <Typography className={styles.menuItemText}>
                      Net 30
                    </Typography>
                  </MenuItem>
                  <MenuItem value="creditCard">
                    <Typography className={styles.menuItemText}>
                      Credit / Card
                    </Typography>
                  </MenuItem>
                </Select>
              </FormControl>
            </div>

            <div className={styles.actionSection}>
              <Button
                onClick={handleDoLater}
                className={styles.doLaterButton}
              >
                <Typography className={styles.doLaterText}>
                  I'll do this later
                </Typography>
              </Button>
            </div>
            {
              paymentMethod ? (
                <>
                  {
                    paymentMethod === 'net30' ? (
                      isNet30TermsApplied ? (
                        <div>
                          <div className={styles.paymentMethodContain}>
                            We have received your request for Net-30 Payment terms.  We’re working on it!

                            We will notify you when a credit limit has been determined.  In the unlikely event we need additional information, we will reach out.
                          </div>
                          <button onClick={() => setShowSalesScreen(true)}>Got it</button>
                        </div>
                      ) : (
                        <>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="dnBNumber"> D&B NUMBER</div>
                            <div className={styles.paymethodContainValue}>
                              <InputWrapper>
                                <CustomTextField
                                  className={clsx(styles.inputCreateAccount, errors?.dnBNumber && styles.error)}
                                  type='text'
                                  mode="wholeNumber"
                                  register={register("dnBNumber")}
                                  placeholder=''
                                  maxLength={9}
                                  onChange={(e: any) => {
                                    register("dnBNumber").onChange(e)
                                    const dnb = e.target.value.replace(/\D/g, '');
                                    setValue('dnBNumber', dnb);
                                  }}
                                />
                              </InputWrapper>
                            </div>
                          </div>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="einNumber"> COMPANY EIN</div>
                            <div className={styles.paymethodContainValue}>
                              <InputWrapper>
                                <CustomTextField
                                  className={clsx(styles.inputCreateAccount, errors?.einNumber && styles.error)}
                                  type='text'
                                  register={register("einNumber")}
                                  placeholder=''
                                  maxLength={10}
                                  onChange={(e) => {
                                    register("einNumber").onChange(e)
                                    handleEinNoChange(e)
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Tab' && !e.shiftKey) {
                                      e.preventDefault();
                                      setTimeout(() => {
                                        setEditModeDesiredCreditLine(true)
                                        const creditLineInput = document.querySelector(`input[id="desired-credit-line"]`);
                                        if (creditLineInput instanceof HTMLElement) {
                                          creditLineInput.focus();
                                        }
                                      }, 100);
                                    }
                                  }}
                                />
                              </InputWrapper>
                            </div>
                          </div>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="creditLine"> D&B NUMBER</div>
                            <div className={styles.paymethodContainValue}>
                              {editModeDesiredCreditLine ? (
                                <ClickAwayListener
                                  onClickAway={() => setEditModeDesiredCreditLine(false)}
                                >
                                  <input
                                    id='desired-credit-line'
                                    type="string"
                                    className={clsx(styles.inputCreateAccount, errors?.creditLine && styles.error)}
                                    autoFocus={true}
                                    value={
                                      (+watch("creditLine")) ?
                                        (formatCurrencyWithComma(watch("creditLine")) ?? "") :
                                        (watch("creditLine") ?? "")
                                    }
                                    onChange={(e) => {
                                      requestCreditLineChangeHandler(e, "creditLine")
                                    }}
                                    onBlur={() => {
                                      setEditModeDesiredCreditLine(false)
                                    }}
                                  />

                                </ClickAwayListener>
                              ) : (
                                <span className={clsx(styles.inputCreateAccount, errors?.creditLine && styles.error)} onClick={() => setEditModeDesiredCreditLine(true)}>
                                  $ {watch("creditLine") ? formatToTwoDecimalPlaces(watch("creditLine")) : ''}
                                </span>
                              )}
                            </div>
                          </div>
                          <button className={styles.headerBtn1} disabled={!isBnplSubmitDisabled} onClick={() => bnplSetupValidate()}><span>Apply for Terms</span></button>
                        </>
                      )
                    ) : (
                      <>
                        <div className={styles.paymentMethodContent}>
                          <div className={styles.paymentMethodItemHeader}>
                            <div className={styles.headerItem}>
                              CREDIT/DEBIT CARD
                            </div>
                            <div className={styles.headerItemBtn}>
                              <button className={clsx(styles.headerBtn1, !enableSaveCardInfo && styles.disabled)} htmlFor="bankName" disabled={!enableSaveCardInfo} onClick={handleCardSubmit}>
                                <span>Save Card Info</span>
                              </button>
                            </div>
                          </div>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="bankName"> CARD TYPE</div>
                            <div className={styles.paymethodContainValue}>
                              <span className={styles.col1}>
                                <span className={clsx(styles.inputCreateAccount)}>{watch('cardType') || ''}</span>
                              </span>
                            </div>
                          </div>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="accountName"> CARD NUMBER</div>
                            <div className={styles.paymethodContainValue}>
                              <div className={styles.stripeElement}>

                                <CardNumberElement options={{
                                  style: stripeElementsStyle,
                                  placeholder: watch('cardNumberLast4Digits') ? `**** **** **** ${watch('cardNumberLast4Digits')}` : '0000 0000 0000 0000'

                                }}
                                  onChange={(e) => handleCardChange(e, 'cardNumber')}
                                // onBlur={() => handleCardSubmit()}
                                />
                              </div>
                            </div>
                          </div>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="bankRoutingNumber"> EXPIRATION / CVV</div>
                            <div className={styles.stripePaymentGrid}>
                              <div className={styles.stripeElement}>
                                <CardExpiryElement options={{
                                  style: stripeElementsStyle,
                                  placeholder: watch('cardExpiry') ? `${watch('cardExpiry')}` : 'MM / YY'
                                }}
                                  onChange={(e) => handleCardChange(e, 'cardExpiry')}
                                // onBlur={() => handleCardSubmit()}
                                />
                              </div>
                              <div className={styles.stripeElement}>
                                <CardCvcElement options={{
                                  style: stripeElementsStyle,
                                  placeholder: 'CVV'
                                }}
                                  onChange={(e) => handleCardChange(e, 'cardCvv')}
                                // onBlur={() => handleCardSubmit()}
                                />
                              </div>
                            </div>
                          </div>
                          <div className={styles.paymentMethodContain}>
                            <div className={styles.paymethodContainTitle} htmlFor="billingZipCode"> BILLING ZIP CODE</div>
                            <div className={styles.paymethodContainValue}>
                              <InputWrapper>
                                <CustomTextField
                                  className={clsx(styles.inputCreateAccount, errors?.billingZipCode && styles.error)}
                                  type='text'
                                  register={register("billingZipCode")}
                                  placeholder='XXXXX'
                                  maxLength={5}
                                  mode="wholeNumber"
                                // onBlur={() => handleZipOnBlur()}
                                />
                              </InputWrapper>
                            </div>
                          </div>
                        </div>
                        <button className={clsx(styles.headerBtn1, !enableSaveCardInfo && styles.disabled)} htmlFor="bankName" disabled={!enableSaveCardInfo} onClick={handleCardSubmit}>
                          <span>Save Card Info</span>
                        </button>
                      </>
                    )
                  }
                </>
              ) : (
                <>

                </>
              )
            }

          </div>
        )
      }

    </div>
  )
}

export default SubscriptionRightSidePanel
