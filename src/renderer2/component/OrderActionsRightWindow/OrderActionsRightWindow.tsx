import styles from './OrderActionsRightWindow.module.scss';
import { useSellerOrderStore } from '@bryzos/giss-ui-library';
import { purchaseOrder } from 'src/renderer2/common';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';

const OrderActionsRightWindow = () => {
  const {filterPoStoreBy, setFilterPoStoreBy} = useSellerOrderStore();
  const { props } = useRightWindowStore();
  const { selectedOrder, selectedOrderIndex, navigateToAcceptOrder } = props;

  const changePoOrderFilterBy = (filter) => {
    if (filter === filterPoStoreBy) setFilterPoStoreBy('');
    else setFilterPoStoreBy(filter);
  }

  return (
    <div className={styles.OrderActionsMain}>
      <div className={styles.actionTitle}>ORDER<br />ACTIONS</div>

      <button onClick={(e) => navigateToAcceptOrder(e,selectedOrder, selectedOrderIndex, true)} disabled={selectedOrderIndex === null || !selectedOrder.is_order_view || selectedOrder.claimed_by === purchaseOrder.pending}>ACCEPT ORDER</button>
      <button disabled>HIDE ORDER</button>
      <button onClick={($event) => navigateToAcceptOrder($event, selectedOrder, selectedOrderIndex, false)} disabled={selectedOrderIndex === null}>VIEW ORDER DETAILS</button>
      <button className={filterPoStoreBy === purchaseOrder.readyToClaim ? styles.activeBtn : ''} onClick={(e) => { changePoOrderFilterBy(purchaseOrder.readyToClaim) }}>VIEW AVAILABLE ONLY</button>
      <button className={filterPoStoreBy === purchaseOrder.pending ? styles.activeBtn : ''} onClick={(e) => { changePoOrderFilterBy(purchaseOrder.pending) }}>VIEW UPCOMING ONLY</button>
      <button className={filterPoStoreBy === purchaseOrder.hidden ? styles.activeBtn : ''} onClick={(e) => { changePoOrderFilterBy(purchaseOrder.hidden) }} disabled>VIEW HIDDEN ORDERS</button>

    </div>
  )
}

export default OrderActionsRightWindow
