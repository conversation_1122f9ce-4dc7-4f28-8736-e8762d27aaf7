.container {
  width: 322px;
  height: 520px;
  padding: 20px;
  border-radius: 20px;
  background-origin: border-box;
  position: relative;
  overflow: hidden;
  background: url(../../assets/New-images/review.svg) no-repeat transparent;
}

.titleContainer {
  text-align: center;

  .mainTitle {
    font-family: Inter;
    font-size: 18px;
    font-weight: 600;
    line-height: normal;
    text-align: center;
    color: #fff;
  }


  .subtitle {
    font-family: Inter;
    font-size: 12px;
    font-weight: 200;
    line-height: normal;
    text-align: center;
    color: #fff;
    margin: 0;
  }
}

.summaryContainer {
  width: 100%;
  height: 282px;
  margin: 12px 0 12px 0px;
  padding: 1px;
  background-origin: border-box;
  background-clip: content-box, border-box;
}

// Tabs styling for header
.headerTabs {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.tabHeader {
  display: flex;
  justify-content: space-between;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  font-family: Syncopate;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.2;
  letter-spacing: 1.68px;
  text-align: center;
  color: #fff;
  text-transform: uppercase;
  cursor: pointer;
  top:3px;

  &:first-child {
    flex: 0 100px;
    position: relative;

  }

  &:last-child {
    position: relative;
    text-align: left;
  }
}

.activeTab {
  color: #fff;
}

.sectionsLayout {
  display: flex;
  width: 100%;
}

.leftColumn {
  width: 100px;
  padding-right: 0px;
  height: 100%;
  position: relative;

}

.rightColumn {
  flex: 1;
}

.dropdownArrow {
  display: flex;
  align-items: center;
  margin-left: 4.5px;
  svg {
    transform: rotate(180deg);
    width: 13px;
    height: 12px;

  }
}

.filterSelect {
  margin-bottom: 12px;
  width: 100%;
  opacity: 0;
  height: 0;
  overflow: hidden;

}

.filterHeader {
  width: 100%;
  height: 20px;
  flex-grow: 0;
  padding: 3px 2px;
  font-family: Inter;
  font-size: 12px;
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: -0.12px;
  text-align: left;
  color: #fff;
  z-index: 2;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.verticalNumbers {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-left: 10px;
  padding-bottom: 10px;
  height: 223px;
  overflow-y: auto;
  padding-top: 10px;

  /* Styling the scrollbar */
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(25, 26, 32, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #8b91a6;
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.verticalNumbersContent {
  display: flex;
  column-gap: 4px;
  cursor: pointer;

  .numberItem {
    display: flex;
    flex-direction: column;
    font-family: Syncopate;
    font-size: 10px;
    font-weight: bold;
    line-height: 1.2;
    letter-spacing: 0.7px;
    text-align: center;
    color: #9b9eac;
    position: relative;
    flex: 0 20px;

    span {
      display: flex;
    }

    .redDot,
    .greenDot {
      margin-right: 0px;
    }
  }

  .leftColumnBox1 {
    width: 40px;
    height: 20px;
    flex-grow: 0;
    border-radius: 1.8px;
    background-color: rgba(255, 255, 255, 0.04);
  }

  .leftColumnBox2 {
    width: 8px;
    height: 6px;
    flex-grow: 0;
    border-radius: 0.4px;
    background-color: rgba(255, 255, 255, 0.04);
  }
}


.redDot,
.greenDot,
.yellowDot,
.blueDot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 4px;
}

.redDot {
  background-color: #fc3030;
}

.greenDot {
  background-color: #43f776;
}

.yellowDot {
  background-color: #ff9e20;
}

.blueDot {
  background-color: #459fff;
}

// Information panel styling
.infoPanel {
  display: flex;
  flex-direction: column;
}

.infoTop {
  display: flex;
  justify-content: space-between;
  padding: 8px 8px 10px 8px;
  position: relative;
}

.infoStats {
  flex: 1;
  display: flex;
  flex-direction: column;
  row-gap: 5px;
}

.statItem {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;

  .statLabel {
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: -0.12px;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      margin-left: 5px;
    }
  }

  .statValue {
    font-family: Inter;
    font-size: 12px;
    font-weight: 200;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 0.48px;
    text-align: left;
    color: #fff;
  }
}

.readyPercentage {
  width: 64px;
  height: 54px;
  flex-grow: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.1);

  .percentageValue {
    font-family: Inter;
    font-size: 24px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    margin: 0;

    span {
      font-size: 10px;
      vertical-align: super;
    }
  }

  .percentageLabel {
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 0.96px;
    text-align: center;
    color: #fff;
    margin: 0;
  }
}

// Progress bars styling
.progressBarsSection {
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.statusProgressBar {
  width: 100%;
  margin-bottom: 5px;
}

.progressBarLabel {
  display: flex;
  justify-content: space-between;

  .progressLabel {
    font-family: Inter;
    font-size: 10px;
    font-weight: 200;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 2px;
    text-align: left;
    color: #fff;
    text-transform: uppercase;
  }

  .progressCount {
    font-family: Inter;
    font-size: 12px;
    font-weight: 200;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 0.48px;
    text-align: left;
    color: #fff;
  }
}

.progressBarContainer {
  position: relative;
  width: 100%;
  height: 10px;
  overflow: visible;
}

.progressBar {
  width: 100%;
  height: 10px;
  border-radius: 2px;
  overflow: visible;
  position: relative;
  margin-bottom: 0;
}

.progressBarFill {
  position: absolute;
  top: 0;
  left: 0;
  height: 10px;
  border-radius: 2px;
  flex-grow: 0;
  margin: 0;
  transform: none;
  transform-origin: unset;
  box-shadow: inset 3px 2px 30.8px -8px rgba(110, 207, 255, 0.91);
  transition: all 0.3s ease-in-out;
}

// Custom progress bar fill styles as provided
.approvedFill {
  background: linear-gradient(92deg, #4ade80 0%, #86efac 100%);
}

.pendingFill {
  background: linear-gradient(92deg, #f59e0b 0%, #fbbf24 100%);
}

.skippedFill {
  background: linear-gradient(92deg, #6366f1 0%, #818cf8 100%);
}

.deletedFill {
  background: linear-gradient(92deg, #ef4444 0%, #f87171 100%);
}

.instructionText {
  margin-bottom: 25px;

  p {
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: -0.36px;
    text-align: center;
    color: rgba(255, 255, 255, 0.4);
  }
}

.orderButton {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0 0;
  border-radius: 10px;
  text-transform: uppercase;
  transition: all 0.1s;
  opacity: unset;
  background-color: #fff;
  font-family: Syncopate;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.72px;
  text-align: center;
  color: #191a20;
  cursor: pointer;

  &:disabled {
    cursor: not-allowed;
    color: rgba(255, 255, 255, 0.4);
    background-color: #222329;
  }

  &:hover:not(:disabled) {
    background-color: #f0f0f0;
  }
}

// Animation classes
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.slideUp {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menuSlideUp {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownList {
  animation: slideUp 250ms ease-out forwards;
  transform-origin: top center;
}

.dropdownDataMain.dropdownDataMain {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .paymentMethod {
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.3;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
  }

  .paymentValue {
    display: flex;
    justify-content: center;
    align-self: stretch;
    flex-grow: 0;
    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    line-height: 1.3;
    text-align: center;
    color: #32ff6c;
  }
}

.dropdownList.dropdownList {
  border-radius: 10px;
  position: relative;
  background-color: rgba(128, 130, 140, 0.28);
  padding: 4px;
  max-width: 280px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  top: 308px !important;

  .muiMenuList {
    padding: 0px;
    z-index: 2;

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1;
      margin-bottom: 3px;
      padding: 10px 8px;
      text-align: center;
      height: 41px;
      width: 100%;
      color: rgba(255, 255, 255, 0.6);
      justify-content: center;

      div {
        display: flex;
        flex-direction: column;
        row-gap: 3px;

        i {
          font-weight: 300;
          font-size: 10px;
        }
      }

      &:hover {
        border-radius: 10px;
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        font-weight: bold;
      }

      // &[aria-selected="true"] {
      //   border-radius: 10px;
      //   // background-color: rgba(255, 255, 255, 0.2);
      //   color: #fff;
      //   font-weight: bold;
      // }
      &:focus {
        border-radius: 10px;
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

.creditLimitTooltip.creditLimitTooltip {
  padding: 10px 25px;
  max-width: 282px;
  height: 60px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.15);
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.7px;
  text-align: center;
  color: #fff;
  margin-bottom: 16px !important;

  .tooltipArrow {
    color: rgba(255, 255, 255, 0.15);
  }
}

.summaryContainer {
  color: #fff;
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .titleContainer {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
  }

}

.sectionsLayout {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: flex-start;
}


// Custom dropdown for filter
.customDropdown {
  position: relative;
  width: 100%;
  margin-bottom: 12px;
}

.dropdownTrigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 10px;
  background-color: rgba(25, 26, 32, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  color: white;
  font-size: 13px;

  &:hover {
    background-color: rgba(25, 26, 32, 0.6);
  }
}

.dropdownArrow {
  font-size: 10px;
  opacity: 0.7;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 2px;
  background-color: #212121;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  z-index: 10;
  overflow: hidden;
}

.dropdownItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.activeItem {
  background-color: rgba(255, 255, 255, 0.05);
  color: white;
}

.menuItem {
  &:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}

.menuItemContent {
  display: flex;
  align-items: center;
  gap: 8px;

  span {
    color: white;
  }
}



.dropDownBG.dropDownBG {
  width: 100px;
  height: 98px;
  padding: 4px;
  object-fit: contain;
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
  background: url(../../assets/New-images/drop-down.svg) no-repeat;
  z-index: 999;
  background-size: cover;
  background-position: bottom center;
  border-radius: 0px 0px 4px 4px;

  ul {
    padding: 0px;

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      padding: 5px 12px;

      &:hover {
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
      }
    }
  }
}
