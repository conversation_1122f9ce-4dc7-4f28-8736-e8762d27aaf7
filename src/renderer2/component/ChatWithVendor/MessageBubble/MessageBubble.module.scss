.messageBubble {
    padding: 8px 8px 8px 8px;
    border-radius: 6px;
    background-color: #000;
    color: #ffffff;
    max-width: 70%;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.24;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    word-wrap: break-word;
    min-width: 30px;

    a {
        color: inherit;
        text-decoration: none;
        cursor: pointer;
        
        &:hover {
            text-decoration: underline;
        }
    }
}

.othersMessage {
    background-color: #d4d4d4;
    color: #000;
}

:global(.MuiTooltip-popper) {
    .linkTooltip {
        background: #181e2b;
        color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 24px rgba(0,0,0,0.22);
        padding: 10px;
        max-width: 200px;
        min-width: 200px;
    }
}