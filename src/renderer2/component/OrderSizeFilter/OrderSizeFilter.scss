.vertical-ruler-container {
  position: relative;
  width: 100%; // Width for the ruler container
  height: 141px; // Ensure the height fits within the box
  display: flex;
  margin-top: 15px;


  .ruler-marks {
    position: absolute;
    width: 100%; // Make sure the marks fill the width
    height: 100%;
    display: flex;
    flex-direction: column-reverse; // Start from bottom to top
    justify-content: space-between;
    left: 11px;

    .ruler-mark {
      position: relative;
      display: flex;
      align-items: center;
      height: 1px; 

      &:first-child{
          .mark-line{
            position: relative;
            top:0.7px
          }
      }
      
      .mark-line {
        background-color: #fff;
        opacity: 0.5;

        &.major-line {
          width: 14.9px; // Major line length
          height: 1px;
        }

        &.minor-line {
          width: 8.9px; // Minor line length (shorter)
          height: 0.7px;
          opacity: 0.2;
        }

        &.middle-minor-line {
          width: 12.9px;
          height: 0.7px;
          opacity: 0.2;
        }

        &.active-mark {
          background-color: #ffc44f;
          opacity: unset;
        }
      }

      .mark-label {
        font-family: Syncopate;
        font-size: 10px;
        color: #fff;
        position: relative;
        left: 11px;
        top: 1px;
        &.active-mark-label{
          color: #ffc44f;
        }
      }
    }
  }

}

.isMacDevice{
  .vertical-ruler-container {
  
    .ruler-marks {
   
      .ruler-mark {
    
        &:first-child{
            .mark-line{
              position: relative;
              top:0.7px
            }
        }

                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4),
                &:nth-child(5),
                &:nth-child(6),
                &:nth-child(7),
                &:nth-child(8),
                &:nth-child(9),
                &:nth-child(10),
                &:nth-child(11) {
                  .mark-line {
                    position: relative;
                    top: 0.5px
                  }
                }
        
       
      }
    }
  
  }
}