.snackbarContainer {
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    width: 570px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 24px;
    z-index: 11000;
    display: flex;
    padding: 9px 10px 9px 10px;
    align-items: center;
    border-radius: 4px;
    transition: opacity 0.3s ease-in-out; 
    &.hide{
        opacity: 0;
    }
    &.show{
        opacity: 1;
    }

    .content {
        flex: 1;
        font-size: 14px;
        color: #fff;
    }

    .actionBtn {
        background: #fff;
        color: #000;
        line-height: normal;
        height: 24px;
        width: 72px;
        border-radius: 3px;
        font-size: 12px;
        margin-left: 16px;
    }
    .closeBtn{
        display: flex;
        align-items: center;
        border-radius: 50%;
        position: relative;
        left: 5px;
        transition: all 0.1s;
        svg{
            width: 20px;
            height: 20px;
        }
        &:hover{
            background-color: #fff;
            svg{
                path{
                    fill:#ff0000
                }
            }
        }
    }
}