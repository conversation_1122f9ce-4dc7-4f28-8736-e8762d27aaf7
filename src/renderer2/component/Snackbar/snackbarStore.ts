import { create } from 'zustand';

interface SnackbarStore {
    // State properties
    openSnackbar: boolean;
    snackbarMessage: string;
    snackbarSeverity: string;
    snackbarActions: any;
    snackbarCloseHandler: any;
    snackbarTimer: number | null;
    
    // Methods
    setSnackbarOpen: (isOpen: boolean) => void;
    setSnackbarMessage: (msg: string) => void;
    setSnackbarSeverity: (sev: string) => void;
    setSnackbarActions: (action: any) => void;
    showToastSnackbar: (msg: string, sev: string, actions: any, closeHandler: any, timer: number | null) => void;
    resetSnackbarStore: () => void;
}

const snackbarStoreInit = {
    openSnackbar: false,
    snackbarMessage: '',
    snackbarSeverity: 'alert',
    snackbarActions: null,
    snackbarCloseHandler: null,
    snackbarTimer: null,
}

const useSnackbarStore = create<SnackbarStore>((set) => ({
    ...snackbarStoreInit,
    setSnackbarOpen: (isOpen: boolean) => set({ openSnackbar: isOpen }),
    setSnackbarMessage: (msg: string) => set({ snackbarMessage: msg }),
    setSnackbarSeverity: (sev: string) => set({ snackbarSeverity: sev }),
    setSnackbarActions: (action: any) => set({ snackbarActions: action }),
    showToastSnackbar: (msg: string, sev: string, actions:any, closeHandler:any, timer: number|null) => set({openSnackbar: true,snackbarMessage: msg, snackbarSeverity: sev, snackbarActions: actions, snackbarCloseHandler:closeHandler, snackbarTimer:timer}),
    resetSnackbarStore: () => set((state:any) => ({
        ...snackbarStoreInit
    }))
}));

export default useSnackbarStore;