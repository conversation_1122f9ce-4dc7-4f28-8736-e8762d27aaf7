.progressItem {
  display: flex;
  flex-direction: column;
  gap: 6px;
  position: relative;
}

.progressLabel {
  font-family: Inter;
  font-size: 12px;
  font-weight: 200;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 4.2px;
  text-align: left;
  color: #fff;
  text-transform: uppercase;
}

.progressBarContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progressBar {
  flex: 1;
  height: 9px;
  background-color: #191a20;
  border-radius: 1px;
  overflow: hidden;
  position: relative;
}

.progressBarFill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(92deg, rgba(97, 203, 255, 0.96) 0%, rgba(136, 215, 255, 0.96) 100%);
  background: url(../../assets/New-images/Progress-Bar.svg) no-repeat transparent;
  border-radius: 1px;
  transition: width 0.3s ease-in-out;
}

.progressPercentage {
  font-family: Inter;
  font-size: 12px;
  font-weight: 200;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 4.2px;
  text-align: right;
  color: #fff;
  position: absolute;
  right: 0;
  top: 0;
} 
.segmentMarker {
  position: absolute;
  z-index: 3;
  display: flex;

} 
.segmentMarkerFill {
  width: 24px;
  height: 9px;
  border-radius: 1px;
} 
.gap {
  width: 2px;
  height: 9px;
  background-color: #212227;
}

