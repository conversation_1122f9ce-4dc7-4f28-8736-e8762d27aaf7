import React from 'react';
import styles from './HoverVideoPlayer.module.scss';
import { ReactComponent as CloseXIcon } from '../../assets/New-images/Close-x-icon.svg';
import { useRightWindowStore } from '../../pages/RightWindow/RightWindowStore';
import { useHoverVideoStore } from '../LeftPanel/HoverVideoStore';

interface HoverVideoPlayerProps {
  videoUrl: string;
  title?: string;
  description?: string;
}

const HoverVideoPlayer: React.FC<HoverVideoPlayerProps> = () => {
  const { setLoadComponent } = useRightWindowStore();
  const { videoUrl, title, description } = useHoverVideoStore.getState().videoToolTipData;

  return (
    <div className={styles.videoPlayerRightWindowContainer}>
      <div className={styles.videoPlayerSection}>
        <video 
          className={styles.videoPlayer}
          src={videoUrl}
          autoPlay

        >
          Your browser does not support the video tag.
        </video>
      </div>
      <div className={styles.informationContainer}>
        {title && <span className={styles.title}>{title}</span>}
        {description && <span className={styles.description}>{description}</span>}
      </div>
      <p className={styles.closeButton} onClick={() => setLoadComponent(null)}><CloseXIcon /></p>
    </div>
  );
};

export default HoverVideoPlayer; 