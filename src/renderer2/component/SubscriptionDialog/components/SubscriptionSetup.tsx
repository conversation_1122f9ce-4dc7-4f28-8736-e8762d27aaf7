import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useStripe, useElements, CardNumberElement } from '@stripe/react-stripe-js';
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore';
import PaymentForm from './PaymentForm';
import { getPaymentSchema } from '../schemas/paymentSchemas';
import styles from '../SubscriptionDialog.module.scss';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { commomKeys, PAYMENT_METHODS, reactQueryKeys, SUBSCRIPTION_STATUS } from 'src/renderer2/common';
import useDialogStore from '../../DialogPopup/DialogStore';
import usePostUserSubscription from 'src/renderer2/hooks/usePostUserSubscription';
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment';
import { useQueryClient } from '@tanstack/react-query';
import usePostUpdateSubscribePayment from 'src/renderer2/hooks/usePostUpdateSubscribePayment';
import usePostUpdateSubscribeAccount from 'src/renderer2/hooks/usePostUpdateSuscribeAccount';
import { calculateSubscriptionAmount } from 'src/renderer2/helper';
import InputWrapper from '../../InputWrapper';
import CustomTextField from '../../CustomTextField';

const SubscriptionSetup = ({ currentMode }: { currentMode: string | undefined }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { userSubscription, isFromSetting , subscriptionsPricing , closeSubscriptionDialog } = useSubscriptionStore();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    mutateAsync: updateUserPayment
  } = usePostUpdateSubscribePayment();
  const {
    mutateAsync: updateUserAccount
  } = usePostUpdateSubscribeAccount();

  // Card completion state
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false,
    cardExpiry: false,
    cardCvc: false
  });

  // Card error state
  const [cardError, setCardError] = useState<{
    cardNumber: string | null;
    cardExpiry: string | null;
    cardCvc: string | null;
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvc: null
  });

  const [stripeError, setStripeError] = useState<string | null>(null);
  const [isCardDetailsRequired, setIsCardDetailsRequired] = useState(false);
  const { userData, setShowLoader } = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const queryClient = useQueryClient();
  const isUpdatePaymentModule = currentMode === 'EDIT_LICENSES' || currentMode === 'EDIT_PAYMENT';
  const isEditPaymentModule = currentMode === 'EDIT_PAYMENT';

  // Create form with dynamic schema
  const methods = useForm<any>({
    resolver: yupResolver(getPaymentSchema(selectedPaymentMethod)),
    defaultValues: {
      numberOfLicenses: 1,
      paymentMethod: '',
    },
    mode: 'onBlur',
  });

  const { handleSubmit, watch, setValue, setError, register, formState: { errors, isValid, isDirty, dirtyFields } } = methods;
  const numberOfLicenses = watch('numberOfLicenses');
  const [monthlyPrice, setMonthlyPrice] = useState(0);

  const {
    mutateAsync: saveUserSubscription
  } = usePostUserSubscription();

  const {
    mutateAsync: buyerSettingsPayment
  } = usePostBuyerSettingsPayment();


  useEffect(() => {
    if (userSubscription?.error_message) {
      // setShowUpdateAccount(false);
    } else if (userSubscription) {
      const pricingData = calculateSubscriptionAmount(Number(userSubscription?.total_license), subscriptionsPricing);
      const nextPaymentCyclePrice = Number(pricingData?.subscription_amount)* Number(userSubscription?.total_license);
      // const isAccountPending = (userSubscription?.status !== SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING);
      if (userSubscription?.id) {
        // setShowUpdateAccount(isAccountPending && userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED);
        // setIsUpdatePaymentModule(!isAccountPending);
        setValue('numberOfLicenses', userSubscription?.total_license);
        setValue('existingNumberOfLicenses', userSubscription?.total_license);
        setValue('nextPaymentCyclePrice', nextPaymentCyclePrice);
        if (userSubscription?.subscribed_emails?.length > 0) {
          setValue('emailAddress', userSubscription?.subscribed_emails);
        }
        setValue('agreeToTerms', !!userSubscription?.id);
        setValue('nextPaymentCycleDay', userSubscription?.next_payment_cycle_day);
        setValue('bankNameOrCardBrand', userSubscription?.payment_details?.card_display_brand ?? userSubscription?.payment_details?.bank_name);
      } else {
        // setShowUpdateAccount(false);
        // setIsUpdatePaymentModule(false);
        setValue('paymentMethod', '');
        setValue('numberOfLicenses', 1);
        setValue('existingNumberOfLicenses', 1);
        setValue('nextPaymentCyclePrice', 0);
        setValue('nextPaymentCycleDay', 0);
      }
      // setSubscriptionStatus(null);
    }
  }, [userSubscription]);

  useEffect(() => {
    if (subscriptionsPricing) {
      const pricingData = calculateSubscriptionAmount(Number(numberOfLicenses), subscriptionsPricing);
      if (pricingData) {
        setMonthlyPrice(Number(pricingData.subscription_amount));
      }
    }
  }, [numberOfLicenses, subscriptionsPricing]);

  useEffect(() => {
    const price = (numberOfLicenses * monthlyPrice).toFixed(2);
    setValue('perUserPrice', price ?? '0.00');
  }, [numberOfLicenses, monthlyPrice])


  console.log("watch", watch())

  const isCardFormComplete = useMemo(() => {
    if (watch('paymentMethod') === PAYMENT_METHODS.CARD) {
      let commonValidation = false
      const isValidCardDetails = !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc
      console.log('isValidCardDetails', isValidCardDetails)
      if (!cardComplete.cardNumber &&
        !cardComplete.cardExpiry &&
        !cardComplete.cardCvc && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!userSubscription?.id && !!userSubscription?.payment_details) || (userSubscription?.status === SUBSCRIPTION_STATUS.CANCELLED))) {
        console.log('true at 114')
        return true;
      }
      if (isUpdatePaymentModule) {
        const isUserProfileUpdated = (
          userSubscription?.payment_details?.first_name !== watch('cardholderFirstName') ||
          userSubscription?.payment_details?.last_name !== watch('cardholderLastName') ||
          userSubscription?.payment_details?.zipcode !== watch('billingZipCode') || userSubscription?.total_license !== watch('numberOfLicenses')
        )
        commonValidation = (isCardDetailsRequired ?
          (!stripeError && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!cardComplete.cardCvc && !cardComplete.cardExpiry && !cardComplete.cardNumber && isUserProfileUpdated) || (cardComplete.cardCvc && cardComplete.cardExpiry && cardComplete.cardNumber)))
          :
          isUserProfileUpdated
        )
      }
      else {
        commonValidation = (isValidCardDetails &&
          !stripeError)
      }
      return (commonValidation &&
        !!watch('cardholderFirstName') &&
        !!watch('cardholderLastName') &&
        !!watch('billingZipCode') &&
        /^\d{5}$/.test(watch('billingZipCode') || '')
      )
    }
  }, [
    watch('paymentMethod'),
    cardComplete.cardNumber,
    cardComplete.cardExpiry,
    cardComplete.cardCvc,
    stripeError,
    watch('cardholderFirstName'),
    watch('cardholderLastName'),
    watch('billingZipCode'),
    isCardDetailsRequired,
    userSubscription,
    cardError,
    cardError.cardNumber,
    cardError.cardExpiry,
    cardError.cardCvc,
    cardComplete.cardNumber,
    cardComplete.cardExpiry,
    cardComplete.cardCvc,
    isDirty,
    stripeError
  ]);

  const isSubmitEnabled = useMemo(() => {
    if (watch('paymentMethod') === PAYMENT_METHODS.CARD) {
      return isCardFormComplete;
    } else if (currentMode === 'EDIT_LICENSES') {
      return userSubscription?.total_license !== watch('numberOfLicenses')
    }
  }, [isCardFormComplete, watch('paymentMethod'), userSubscription?.total_license, watch('numberOfLicenses')]);

  const handlePaymentMethodChange = (method: string) => {
    setSelectedPaymentMethod(method);
    // Reset form when payment method changes

    if (method === PAYMENT_METHODS.CARD) {
      initializeCardForm();
    } else {
      initializeAchForm();
    }
    // Reset card states when payment method changes
    setCardComplete({
      cardNumber: false,
      cardExpiry: false,
      cardCvc: false
    });
    setCardError({
      cardNumber: null,
      cardExpiry: null,
      cardCvc: null
    });
    setStripeError(null);
    setIsCardDetailsRequired(false);
  };

  const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
    setIsCardDetailsRequired(true);
    setCardComplete(prev => ({
      ...prev,
      [fieldName]: event.complete
    }));

    // Set or clear error based on the event
    if (event.error || (!event.empty && !event.complete)) {
      setCardError(prev => ({
        ...prev,
        [fieldName]: event.error?.message ?? 'Incomplete card details'
      }));
    } else {
      setCardError(prev => ({
        ...prev,
        [fieldName]: null
      }));
      setStripeError(null);
    }
  }, []);

  const initializeCardForm = () => {
    setValue('cardholderFirstName', userSubscription?.payment_details?.first_name);
    setValue('cardholderLastName', userSubscription?.payment_details?.last_name);
    setValue('billingZipCode', userSubscription?.payment_details?.zipcode);
    setValue('cardNumberLast4Digits', userSubscription?.payment_details?.card_number_last_four_digits);
    setValue('cardExpiry', userSubscription?.payment_details?.expiration_date);
    setValue('email_id', userSubscription?.payment_details?.email_id || userData?.data?.email_id);
  }

  const initializeAchForm = () => {
    setValue('accountName', userSubscription?.payment_details?.account_name);
    setValue('routingNumber', userSubscription?.payment_details?.routing_number);
    // setValue('accountNumber', userSubscription?.account_number);
    setValue('accountType', userSubscription?.payment_details?.account_type?.toLowerCase());
    setValue('bankName', userSubscription?.payment_details?.bank_name);
    setValue('last4AccountNumber', userSubscription?.payment_details?.account_number);
    // setValue('reEnterAccountNumber', userSubscription?.account_number);
  }


  const handleUpdateAccount = async () => {
    const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    const emailAddresses = watch('emailAddress')?.filter(Boolean) || [];
    let hasEmailError = false;
    const emailList: string[] = [];
    emailAddresses.forEach((emailAddress: any, index: number) => {
      const email = emailAddress.email_id;
      const emailTrimmed = email.trim();
      if (emailTrimmed.length === 0) {
        return;
      }
      if (!emailRegex.test(emailTrimmed)) {
        setError(`emailAddress.${index}`, { message: 'Please enter a valid email address' });
        hasEmailError = true;
      }
      emailList.push(emailTrimmed);
    });
    if (hasEmailError) {
      return;
    }
    const payload = {
      "data": {
        "total_license": Number(watch('numberOfLicenses')),
        "subscribed_emails": emailList
      }
    }
    try {
      setShowLoader(true);
      const response = await updateUserAccount(payload);
      if (
        typeof response === "object" &&
        "error_message" in response
      ) {
        const { reason, email: emails } = response.error_message;
        if (reason === 'invalid_domain')

          emails.forEach((email: any, index: number) => {
            const _i = watch('emailAddress').findIndex(({ email_id }) => email === email_id);
            setError(`emailAddressDomain.${_i}`, { message: 'User must have same company domain' });
          });
        else
          showCommonDialog(null, response.error_message || 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
        setShowLoader(false);
      } else {
        queryClient.invalidateQueries([reactQueryKeys.getUserSubscription]);
        showCommonDialog(null, 'Account updated successfully', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
      }
    } catch (err) {
      showCommonDialog(null, 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
      setShowLoader(false);
    } finally {
      setShowLoader(false);
    }
  }

  const onSubmit = async (data: any) => {
    setShowLoader(true);

    if (isUpdatePaymentModule && userSubscription?.total_license !== data.numberOfLicenses) {
      handleUpdateAccount();
    }

    if (data.paymentMethod === PAYMENT_METHODS.CARD && isCardFormComplete) {
      if (!stripe || !elements) {
        setStripeError("Stripe hasn't loaded yet. Please try again.");
        setShowLoader(false);
        return;
      }

      // Check if card details are complete
      if ((!userSubscription?.payment_details?.card_number_last_four_digits) && (!cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc)) {
        setStripeError("Please complete all card details.");
        setShowLoader(false);
        return;
      }

      try {
        let _paymentMethod;
        const cardElement = elements.getElement(CardNumberElement);
        if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc) {
          const { paymentMethod, error: paymentMethodError } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
              email: data.email_id,
              name: `${data.cardholderFirstName} ${data.cardholderLastName}`,
              address: {
                country: 'US',
                postal_code: data.billingZipCode,
              }
            }
          });
          if (paymentMethodError) {
            setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
            setShowLoader(false);
            return;
          }
          else {
            _paymentMethod = paymentMethod;
          }
        }

        const payload = {
          "data": {
            "total_license": !isUpdatePaymentModule ? Number(data.numberOfLicenses) : undefined,
            "payment_method": PAYMENT_METHODS.CARD,
            "payment_details": {
              "first_name": data.cardholderFirstName,
              "last_name": data.cardholderLastName,
              "zipcode": data.billingZipCode
            },
            "payment_method_id": (_paymentMethod && (userSubscription?.active_customer || isUpdatePaymentModule)) ? _paymentMethod.id : undefined
          }
        }
        const paymentSettingPayload = {
          "data": {
            "payment_method": PAYMENT_METHODS.CARD,
            "payment_details": {
              "zipcode": data.billingZipCode,
              "payment_method_id": _paymentMethod ? _paymentMethod.id : undefined,
              "first_name": data.cardholderFirstName,
              "last_name": data.cardholderLastName
            }
          }
        }
        let response;
        if (userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED && (userSubscription?.active_customer || isUpdatePaymentModule)) {
          response = await updateUserPayment(payload);
        } else {
          if ((cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc) || dirtyFields.billingZipCode) {
            console.log("checkkkkkk34343443444434")
            console.log("paymentSettingPayload", paymentSettingPayload)
            const buyerSettingsPaymentResponse = await buyerSettingsPayment(paymentSettingPayload);
            if (buyerSettingsPaymentResponse.error_message) {
              setStripeError(buyerSettingsPaymentResponse.error_message);
              showCommonDialog(null, buyerSettingsPaymentResponse.error_message, null, resetDialogStore, [
                { name: commomKeys.errorBtnTitle, action: resetDialogStore }
              ]);
              setShowLoader(false);
              return;
            }
          }
          console.log("payload", payload)
          console.log('79222222222')
          response = await saveUserSubscription(payload);
          if (!response || !response?.client_secret) {
            setStripeError('No client secret received from server');
            setShowLoader(false);
            return;
          }

          console.log("response", response)
          const { client_secret: clientSecret } = response;
          const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(clientSecret);
          console.log("paymentIntent", paymentIntent)
          console.log("confirmError", confirmError)
          if (confirmError) {
            showCommonDialog(
              null,
              confirmError.message || 'An error occurred during payment processing',
              commomKeys.actionStatus.error,
              resetDialogStore,
              [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
            );
            setStripeError(confirmError.message || 'An error occurred during payment processing');
          } else if (paymentIntent.status === 'succeeded') {

          }
        
        }
        if (isFromSetting) {
          closeSubscriptionDialog();
        }
        queryClient.invalidateQueries([reactQueryKeys.getUserSubscription])

      } catch (err) {
        showCommonDialog(
          null,
          err?.message || 'An error occurred during payment processing',
          commomKeys.actionStatus.error,
          resetDialogStore,
          [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
        );
        setStripeError(err?.message || 'An error occurred during payment processing');
        setShowLoader(false);
      }
      finally {
        setShowLoader(false);
      }
    }
  };
  console.log('watch', watch())
  console.log('isCardFormComplete', isCardFormComplete)
  return (
    <div className={styles.subscriptionSetup}>
      {
        !isEditPaymentModule && (
      <div className={styles.header}>
        <div className={styles.stepTitle}>STEP 1: BUY LICENSES</div>
        <div className={styles.licenseCount}>YOU HAVE 0 PAID LICENSES</div>
      </div>

        )
      }

      {/* Main Content */}
      <div className={styles.content}>
        {/* Left Panel - License Quantity */}
        {
          !isEditPaymentModule && (  
        <div className={styles.panel}>
          <div className={styles.panelTitle}>
            Enter the number of licenses you would like to purchase.
          </div>
          <div className={styles.quantityInput}>
                <InputWrapper>
                  <CustomTextField
                    className={styles.numberOfUsersInput}
                    type='text'
                    mode='wholeNumber'
                    register={register("numberOfLicenses")}
                    placeholder='No. of Users'
                    disabled={isUpdatePaymentModule && userSubscription?.active_customer}
                  />
                </InputWrapper>
            {errors.numberOfLicenses && (
              <span className="error">{String(errors.numberOfLicenses.message)}</span>
            )}
          </div>
          <div className={styles.priceInfo}>
            Each license is $50 per month
          </div>
        </div>

          )
        }

        {/* Middle Panel - Payment Method */}

        <div className={styles.panel}>
          <FormProvider {...methods}>
            <PaymentForm
              onPaymentMethodChange={handlePaymentMethodChange}
              selectedPaymentMethod={selectedPaymentMethod}
              cardComplete={cardComplete}
              cardError={cardError}
              stripeError={stripeError}
              onCardChange={handleCardChange}
            />
          </FormProvider>
        </div>

        {/* Right Panel - Order Summary */}

        <div className={styles.panel}>
        {
          !isEditPaymentModule && ( 
          <div className={styles.orderSummary}>
            <div className={styles.summaryRow}>
              <span>Number of Licenses</span>
              <span>{numberOfLicenses}</span>
            </div>
            <div className={styles.summaryRow}>
              <span>Price per License</span>
              <span>$ {monthlyPrice}</span>
            </div>
            <div className={styles.summaryRow}>
              <span className={styles.monthlyTotal}>Monthly Total</span>
              <span className={styles.monthlyTotal}>$ {watch('perUserPrice')}</span>
            </div>
          </div>
          )
        }

          <div className={styles.disclaimer}>
            By clicking "Purchase," you agree to the terms of the{' '}
            <span className={styles.link}>Debit Authorization Agreement</span>.
          </div>

          <button
            className={styles.purchaseButton}
            onClick={() => onSubmit(watch())}
            disabled={!stripe || isProcessing || !isSubmitEnabled}
          >
            {isProcessing ? 'PROCESSING...' : 'PURCHASE'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionSetup;
