import React from 'react';
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore';
import styles from '../SubscriptionDialog.module.scss';

const SubscriptionUserMgmt = () => {
  const { userSubscription } = useSubscriptionStore();
  
  // Mock data for demonstration - replace with actual data from userSubscription
  const mockData = {
    licenseCount: 1,
    nextPaymentAmount: '$50.00',
    nextPaymentDate: 'Sep 1, 2025',
    paymentMethod: 'ACH Debit from Bank of America account ending in 1000'
  };

  const handleGoToSettings = () => {
    // TODO: Implement navigation to settings
    console.log('Navigate to settings');
  };

  return (
    <div className={styles.subscriptionSetup}>
      {/* Header Section */}
      <div className={styles.header}>
        <div className={styles.stepTitle}>
          STEP 1: BUY LICENSES
        </div>
        <div className={styles.licenseCount}>
          YOU HAVE {mockData.licenseCount} PAID LICENSES
        </div>
      </div>

      {/* Content Cards */}
      <div className={styles.content}>
        {/* Left Card - Need More Licenses */}
        <div className={styles.panel}>
          <div className={styles.panelTitle}>
            NEED MORE LICENSES?
          </div>
          <div style={{ color: '#b0b0b0', fontSize: '14px', marginBottom: '20px' }}>
            You can manage your licenses in settings (top right).
          </div>
          <button 
            className={styles.purchaseButton}
            onClick={handleGoToSettings}
            style={{ marginTop: 'auto' }}
          >
            GO TO SETTINGS
          </button>
        </div>

        {/* Middle Card - Success */}
        <div className={styles.panel}>
          <div className={styles.panelTitle} style={{ fontSize: '20px', textAlign: 'center', marginBottom: '20px' }}>
            SUCCESS!
          </div>
          <div style={{ color: '#b0b0b0', fontSize: '14px', marginBottom: '20px' }}>
            You have successfully purchased {mockData.licenseCount} license.
          </div>
          <div style={{ color: '#b0b0b0', fontSize: '14px' }}>
            Use the table below to assign the license to a User.
          </div>
          {/* TODO: Add user assignment table here */}
          <div style={{ 
            marginTop: '20px', 
            padding: '16px', 
            background: '#404040', 
            borderRadius: '8px',
            color: '#b0b0b0',
            fontSize: '14px',
            textAlign: 'center'
          }}>
            User Assignment Table (Coming Soon)
          </div>
        </div>

        {/* Right Card - Payment Details */}
        <div className={styles.panel}>
          <div className={styles.panelTitle}>
            PAYMENT DETAILS
          </div>
          <div style={{ color: '#b0b0b0', fontSize: '14px', lineHeight: '1.6' }}>
            <div style={{ marginBottom: '12px' }}>
              Your next automatic payment is for <strong style={{ color: 'white' }}>{mockData.nextPaymentAmount}</strong>
            </div>
            <div style={{ marginBottom: '12px' }}>
              on <strong style={{ color: 'white' }}>{mockData.nextPaymentDate}.</strong>
            </div>
            <div style={{ marginTop: '20px' }}>
              {mockData.paymentMethod}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionUserMgmt;
