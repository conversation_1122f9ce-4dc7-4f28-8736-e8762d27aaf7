import React from 'react';
import { useFormContext } from 'react-hook-form';

const BankTransferForm: React.FC = () => {
  const { register, formState: { errors } } = useFormContext();

  return (
    <div className="bank-transfer-form">
      <div className="form-row">
        <div className="form-field">
          <input
            {...register('accountHolderName')}
            placeholder="ACCOUNT HOLDER NAME"
            className="form-input"
          />
          {errors.accountHolderName && <span className="error">{String(errors.accountHolderName.message)}</span>}
        </div>
      </div>

      <div className="form-row">
        <div className="form-field">
          <input
            {...register('accountNumber')}
            placeholder="ACCOUNT NUMBER"
            className="form-input"
          />
          {errors.accountNumber && <span className="error">{String(errors.accountNumber.message)}</span>}
        </div>
        <div className="form-field">
          <input
            {...register('routingNumber')}
            placeholder="ROUTING NUMBER"
            className="form-input"
          />
          {errors.routingNumber && <span className="error">{String(errors.routingNumber.message)}</span>}
        </div>
      </div>

      <div className="form-row">
        <div className="form-field">
          <input
            {...register('bankName')}
            placeholder="BANK NAME"
            className="form-input"
          />
          {errors.bankName && <span className="error">{String(errors.bankName.message)}</span>}
        </div>
      </div>
    </div>
  );
};

export default BankTransferForm; 