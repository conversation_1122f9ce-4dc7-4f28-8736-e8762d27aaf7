import React, { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { CardCvcElement, CardElement, CardExpiryElement, CardNumberElement } from '@stripe/react-stripe-js';
import styles from '../SubscriptionDialog.module.scss';
import InputWrapper from '../../InputWrapper';
import CustomTextField from '../../CustomTextField';
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode';

interface CreditCardFormProps {
    cardComplete: {
        cardNumber: boolean;
        cardExpiry: boolean;
        cardCvc: boolean;
    };
    cardError: {
        cardNumber: string | null;
        cardExpiry: string | null;
        cardCvc: string | null;
    };
    stripeError: string | null;
    onCardChange: (event: any, fieldName: 'cardNumber' | 'cardExpiry' | 'cardCvc') => void;
}

const CreditCardForm: React.FC<CreditCardFormProps> = ({
    cardComplete,
    cardError,
    stripeError,
    onCardChange
}) => {
    const { register, watch,  setError , formState: { errors }} = useFormContext();
  const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();  

    const stripeElementsStyle = {
        base: {
            color: '#fff',
            fontFamily: 'Inter, sans-serif',
            fontSize: '16px',
            '::placeholder': {
                color: "rgba(255, 255, 255, 0.67)",
            },
            backgroundColor: '#3f4753',
            padding: '6px 16px',
        },
        invalid: {
            color: '#ff6b6b',
            iconColor: '#ff6b6b',
        },
    };

    const handleZipValidation = async (): Promise<any> => {
        if (watch('billingZipCode') && watch('billingZipCode').trim() !== '' && watch('billingZipCode').trim().length > 4) {
            try {
                const res = await verifyZipCode({ zip_code: watch('billingZipCode') });
                if (res) {
                    return true;
                } else {
                    setError('billingZipCode', { message: 'Invalid zipcode' });
                    return false;
                }
            } catch (error) {
                console.log('error', error);
                setError('billingZipCode', { message: 'Invalid zipcode' });
                return false;
            }
        }
    };
    console.log(errors, "errors");

    console.log("helloooo")

    return (
        <div className={styles.paymentDetailsContainer}>
            <div className={styles.paymentDetails}>
                <InputWrapper>
                    <CustomTextField
                        type="text"
                        placeholder="Cardholder First Name"
                        className={styles.formField}
                        register={register('cardholderFirstName')}
                        errorInput={errors.cardholderFirstName?.message}
                        aria-label="Cardholder First Name"
                    />
                </InputWrapper>
                <InputWrapper>
                    <CustomTextField
                        type="text"
                        placeholder="Cardholder Last Name"
                        className={styles.formField}
                        register={register('cardholderLastName')}
                        errorInput={errors.cardholderLastName?.message}
                        aria-label="Cardholder Last Name"
                    />
                </InputWrapper>
            </div>
            <div className={styles.paymentDetails}>
                <div className={styles.stripeElement}>
                    <CardNumberElement options={{
                        style: stripeElementsStyle,
                        placeholder: watch('cardNumberLast4Digits') ? `**** **** **** ${watch('cardNumberLast4Digits')}` : 'Card Number'

                    }}
                        onChange={(e) => onCardChange(e, 'cardNumber')}
                    />
                </div>
                <div className={styles.stripeElement}>
                    <CardExpiryElement options={{
                        style: stripeElementsStyle,
                        placeholder: watch('cardExpiry') ? `${watch('cardExpiry')}` : 'Expiration ( MM / YY )'
                    }}
                        onChange={(e) => onCardChange(e, 'cardExpiry')}
                    />
                </div>
            </div>
            <div className={styles.paymentDetails}>
                <div className={styles.stripeElement}>
                    <CardCvcElement options={{
                        style: stripeElementsStyle,
                        placeholder: 'CVV'
                    }}
                        onChange={(e) => onCardChange(e, 'cardCvc')}
                    />
                </div>
                <InputWrapper>
                    <CustomTextField
                        mode="wholeNumber"
                        placeholder="Billing Zip Code"
                        className={styles.formField}
                        register={register('billingZipCode')}
                        errorInput={errors.billingZipCode?.message}
                        aria-label="Billing Zip Code"
                        maxLength={5}
                        onBlur={handleZipValidation}
                    />
                </InputWrapper>
            </div>
        </div>
    );
};

export default CreditCardForm; 