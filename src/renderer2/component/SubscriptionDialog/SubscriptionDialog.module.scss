.subscriptionDialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
}

.dialogContent {
    background: #000000;
    border-radius: 12px;
    padding: 24px;
    min-width: 90%;
    // max-width: 600px;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index:  101;
    display: flex;
}

.closeIcon {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 1;
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
        color: #333;
    }

    svg {
        width: 20px;
        height: 20px;
    }
}

.dialogTitle {
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
}

.dialogText {
    color: #666;
    line-height: 1.5;
    text-align: center;
} 

.subscriptionSetup {
    color: white;
    font-family: 'Inter', sans-serif;
    padding: 20px;
    max-width: 90%;
    margin: 0 auto;
  
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding: 0 20px;
  
      .stepTitle {
        font-size: 24px;
        font-weight: 600;
        color: white;
      }
  
      .licenseCount {
        font-size: 16px;
        color: #007bff;
        font-weight: 500;
      }
    }
  
    .content {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
  
      .panel {
        background: #2a2a2a;
        border-radius: 12px;
        padding: 24px;
        min-height: 300px;
        display: flex;
        flex-direction: column;
  
        .panelTitle {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 20px;
          color: #e0e0e0;
        }
  
        .quantityInput {
          margin-bottom: 20px;
  
          .quantityField {
            width: 100%;
            height: 60px;
            background: #404040;
            border: none;
            border-radius: 8px;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            color: white;
            padding: 0 16px;
  
            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px #007bff;
            }
          }
        }
  
        .priceInfo {
          font-size: 14px;
          color: #b0b0b0;
          margin-top: auto;
        }
  
        .paymentMethod {
          margin-bottom: 20px;
  
          .paymentSelect {
            width: 100%;
            height: 50px;
            background: #404040;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            color: white;
            padding: 0 16px;
            cursor: pointer;
  
            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px #007bff;
            }
  
            option {
              background: #404040;
              color: white;
            }
          }
        }
  
        .cardElement {
          margin-top: 20px;
          padding: 16px;
          background: #404040;
          border-radius: 8px;
          border: 1px solid #555;
        }
  
        .orderSummary {
          margin-bottom: 20px;
  
          .summaryRow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #404040;
  
            &:last-child {
              border-bottom: none;
            }
  
            .monthlyTotal {
              font-weight: 700;
              color: white;
            }
          }
        }
  
        .disclaimer {
          font-size: 12px;
          color: #b0b0b0;
          margin-bottom: 20px;
          line-height: 1.4;
  
          .link {
            color: #ff6b35;
            cursor: pointer;
            text-decoration: underline;
  
            &:hover {
              color: #ff8c5a;
            }
          }
        }
  
        .purchaseButton {
          width: 100%;
          height: 50px;
          background: #404040;
          border: none;
          border-radius: 8px;
          color: white;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: background-color 0.2s;
  
          &:hover:not(:disabled) {
            background: #505050;
          }
  
          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  
  // Responsive design
  @media (max-width: 1024px) {
    .subscriptionSetup {
      .content {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  }
  
  @media (max-width: 768px) {
    .subscriptionSetup {
      padding: 16px;
  
      .header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
  
        .stepTitle {
          font-size: 20px;
        }
  
        .licenseCount {
          font-size: 14px;
        }
      }
    }
  } 