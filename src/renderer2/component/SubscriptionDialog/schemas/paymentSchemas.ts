import { PAYMENT_METHODS } from 'src/renderer2/common';
import * as yup from 'yup';

// Base schema for common fields
const baseSchema = yup.object({
  numberOfLicenses: yup
    .number()
    .min(1, 'Minimum 1 license required')
    .max(100, 'Maximum 100 licenses allowed'),
  paymentMethod: yup
    .string()
    .oneOf([PAYMENT_METHODS.CARD, PAYMENT_METHODS.ACH], 'Invalid payment method'),
});

// Credit Card schema
export const creditCardSchema = baseSchema.concat(
  yup.object({
    cardholderFirstName: yup
      .string(),
    cardholderLastName: yup
      .string(),
    billingZipCode: yup
      .string(),
  })
);

// Bank Transfer schema
export const bankTransferSchema = baseSchema.concat(
  yup.object({
    accountHolderName: yup
      .string(),
    accountNumber: yup
      .string()
      .matches(/^\d{8,17}$/, 'Account number must be 8-17 digits'),
    routingNumber: yup
      .string()
      .required('Routing number is required')
      .matches(/^\d{9}$/, 'Routing number must be 9 digits'),
    bankName: yup
      .string()
      .required('Bank name is required')
      .min(2, 'Bank name must be at least 2 characters'),
  })
);

// Dynamic schema based on payment method
export const getPaymentSchema = (paymentMethod: string) => {
  switch (paymentMethod) {
    case PAYMENT_METHODS.CARD:
      return creditCardSchema;
    case PAYMENT_METHODS.ACH:
      return bankTransferSchema;
    default:
      return baseSchema;
  }
};

export type CreditCardFormData = yup.InferType<typeof creditCardSchema>;
export type BankTransferFormData = yup.InferType<typeof bankTransferSchema>;
export type PaymentFormData = CreditCardFormData | BankTransferFormData; 