import { useRef, useState, useEffect } from "react";
import { ReactComponent as ShowPassIcon } from '../../assets/New-images/icon-eye-show.svg';
import { ReactComponent as ShowPassIconHover } from '../../assets/New-images/icon-eye-show-hover.svg';
import { ReactComponent as HidePassIcon } from '../../assets/New-images/icon-eye-hide.svg';
import { ReactComponent as HidePassIconHover } from '../../assets/New-images/icon-eye-hide-hover.svg';
import styles from './CustomPasswordField.module.scss'
import clsx from "clsx";
import { ClickAwayListener } from "@mui/material";
import CustomTextField from "../CustomTextField";
import { ClassNames } from "@emotion/react";


type CustomPasswordFieldProps = {
    register: any,
    onFocus?: Function,
    onChange?: Function,
    placeholder: string,
    onBlur?: Function,
    isConfirmPassword?: boolean,
    targetText?: string,
    currentText?: string,
    errorInput?:any,
    className?:any,
    classNameWrapper?:any,
    customPasswordFieldRef?:any,
    tabIndex?:number,
    showPassAtInitial:boolean,
}


const CustomPasswordField: React.FC<CustomPasswordFieldProps>  = ({ register = {}, placeholder, onChange, isConfirmPassword = false, targetText = '', currentText = '', onBlur, onFocus , className, errorInput, classNameWrapper, customPasswordFieldRef , tabIndex , showPassAtInitial = false}) => {
    const [passwordVisibility, setPasswordVisibility] = useState(true);
    const [showPasswordMatch, setShowPasswordMatch] = useState(false);
    const [passwordValue, setPasswordValue] = useState(currentText || '');
    const [lastCharVisible, setLastCharVisible] = useState(false);
    const [lastCharTimer, setLastCharTimer] = useState<NodeJS.Timeout | null>(null);
    const [isFocused, setIsFocused] = useState(false);
    const inputPasswordRef = useRef<HTMLInputElement | null>(null);

    // Update passwordValue when currentText changes (for external updates)
    useEffect(() => {
        if (currentText !== passwordValue) {
            setPasswordValue(currentText);
        }
    }, [currentText]);

    const togglePasswordVisibility = () => {
        setPasswordVisibility(!passwordVisibility);
    };

    const handlePasswordMatchClick = () => {
        setShowPasswordMatch(false)
        setTimeout(() => {
            if (inputPasswordRef.current) {
                (inputPasswordRef.current as any).focus();
            }
        }, 100);
    }

    const handlePasswordMatchVisibility = () => {
        if (isConfirmPassword && currentText.length) {
            setShowPasswordMatch(true);
        }
    }

    const handleOnFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        setShowPasswordMatch(false);
        setIsFocused(true);
        if(onFocus) onFocus(e);
    }

    const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        const isCharacterAdded = newValue.length > passwordValue.length;

        setPasswordValue(newValue);

        // Clear any existing timer
        if (lastCharTimer) {
            clearTimeout(lastCharTimer);
        }

        // Only make the last character visible if a character was added (not deleted)
        if (isCharacterAdded) {
            setLastCharVisible(true);

            // Set a timer to hide the last character after 1 second
            const timer = setTimeout(() => {
                setLastCharVisible(false);
            }, 1000);

            setLastCharTimer(timer);
        } else {
            // For deletions, immediately hide the last character
            setLastCharVisible(false);
        }

        if(onChange) onChange(e);
    }

    // Clean up timer on unmount
    useEffect(() => {
        return () => {
            if (lastCharTimer) {
                clearTimeout(lastCharTimer);
            }
        };
    }, [lastCharTimer]);

    // Since CustomTextField doesn't accept a value prop directly,
    // we need to update the value through the register object
    useEffect(() => {
        if (inputPasswordRef.current) {
            // Using any type to bypass TypeScript checking for the input value
            (inputPasswordRef.current as any).value = passwordValue;
        }
    }, [passwordValue]);

    const handleOnBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        if(onBlur) onBlur(e);
        handlePasswordMatchVisibility();
    }

    const handleInputRef = (e: HTMLInputElement) => {
        inputPasswordRef.current = e; register.ref(e);
        if(customPasswordFieldRef) customPasswordFieldRef.current = e;
    }



    return (
        <ClickAwayListener onClickAway={handlePasswordMatchVisibility}>
            <div className={clsx(styles.togglePassWrapper, styles.togglePassWrapperMain, errorInput && styles.inputErrorPass, classNameWrapper)}>
                {(!showPasswordMatch || !passwordVisibility) ? (
                    <div className={styles.passwordInputContainer}>
                        {/* Actual input field (visually hidden but functional) */}
                        <CustomTextField
                            type={passwordVisibility ? "password" : "text"} // Use password type for better browser handling
                            register={register}
                            placeholder={placeholder}
                            onChange={handleOnChange}
                            onBlur={handleOnBlur}
                            onFocus={handleOnFocus}
                            inputRef={handleInputRef}
                            className={clsx(styles.inputPass, styles.hiddenInput, className)} 
                            tabIndex={tabIndex}
                        />

                        {/* Custom overlay to show masked password or plain text */}
                        <div
                            className={clsx(
                                styles.passwordOverlay,
                                isFocused && styles.focused,
                                errorInput && styles.maskedInputErrorPass
                            )}
                        >
                            {!passwordValue && placeholder && (
                                <span className={styles.placeholder}>{placeholder}</span>
                            )}
                            {passwordValue && passwordVisibility && (
                                <span className={styles.maskedPassword}>
                                    {/* Show dots for all characters except possibly the last one */}
                                    {Array(passwordValue.length - (lastCharVisible ? 1 : 0))
                                        .fill('•')
                                        .join('')}

                                    {/* Show the last character only if it's recently typed */}
                                    {passwordValue.length > 0 && lastCharVisible && (
                                        <span className={styles.lastChar}>
                                            {passwordValue.charAt(passwordValue.length - 1)}
                                        </span>
                                    )}
                                </span>
                            )}
                            {passwordValue && !passwordVisibility && (
                                <span className={styles.plainPassword}>
                                    {passwordValue}
                                </span>
                            )}
                        </div>
                    </div>
                ) : (
                    <button
                        className={styles.passwordMatchContainer}
                        onClick={handlePasswordMatchClick}
                        onFocus={handlePasswordMatchClick}
                        tabIndex={tabIndex}
                    >
                        {(currentText.split(''))?.map((char, i) => (
                            <div
                                key={i}
                                className={clsx(
                                    styles.passwordMatch,
                                    (!targetText || i > targetText.length - 1)
                                        ? styles.extraChar
                                        : char === targetText[i]
                                            ? styles.match
                                            : styles.mismatch
                                )}
                            />
                        ))}
                    </button>
                )}

                {(passwordValue.length > 0 || showPassAtInitial)&& (
                    <button className={styles.showPassBtn} onClick={togglePasswordVisibility} tabIndex={-1}>
                        {passwordVisibility ? (
                            <span className={styles.eyeIcon}>
                                <ShowPassIcon className={styles.img1} />
                                <ShowPassIconHover className={styles.img2} />
                            </span>
                        ) : (
                            <span className={styles.eyeIcon}>
                                <HidePassIcon className={styles.img1} />
                                <HidePassIconHover className={styles.img2} />
                            </span>
                        )}
                    </button>
                )}
            </div>
        </ClickAwayListener>
    )
}

export default CustomPasswordField;