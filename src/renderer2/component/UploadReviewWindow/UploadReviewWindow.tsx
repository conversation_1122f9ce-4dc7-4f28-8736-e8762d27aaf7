import VideoPlayerRightWindow from "../../pages/RightWindow/VideoPlayerRightWindow/VideoPlayerRightWindow"
import OrderSummary from "../OrderSummary/OrderSummary"
import { useRightWindowStore } from "../../pages/RightWindow/RightWindowStore";
import BomProcessingWindow from "../BomProcessingWindow/BomProcessingWindow";
import { routes, shareEmailTypes } from "src/renderer2/common";
import ShareEmailWindow from "../ShareEmailWindow/ShareEmailWindow";

const UploadReviewWindow = ({orderSummaryProps}:any) => {
  const {showVideo, showBomProcessing} = useRightWindowStore();
  const { shareEmailType } = useRightWindowStore();
  return (
    <>
      <OrderSummary {...orderSummaryProps} />
      {/* {(showVideo && location.pathname !== routes.savedBom) && <VideoPlayerRightWindow />} */}
      {showBomProcessing && <BomProcessingWindow hideScore={true} />}
      {shareEmailType === shareEmailTypes.shareQuote && <ShareEmailWindow />}

    </>
  )
}

export default UploadReviewWindow
