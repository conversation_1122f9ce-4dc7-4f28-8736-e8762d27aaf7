// @ts-nocheck
import React, {  useEffect, useRef, useState } from 'react';
import { Routes, Route, useNavigate, useLocation} from 'react-router-dom';
import Login from './pages';
import Home from './pages/search/home';
import Tnc from './pages/tnc';
import Success from './pages/success';
import CreatePo from './pages/buyer/CreatePo/CreatePo';
import BuyerSetting from './pages/buyer/buyerSetting';
import SellerSetting from './pages/seller/sellerSetting';
import OrderConfirmation from './pages/buyer/orderConfirmation';
import AcceptOrder from './pages/seller/acceptOrder';
import Dispute from './pages/dispute/dispute';
import Order from './pages/dispute/order';
import { expiredActivePricedProducts, getHomeRoute, getLocal } from './helper';
import { localStorageKeys } from './common';
import {  
  CUSTOM_NOTIFICATION_ACTION,
  CUSTOM_NOTIFICATION_PRIORTY,
    MAX_APP_HEIGHT,
    MAX_APP_WIDTH,
    chatUserRole,
    commomKeys,
    purchaseOrder,
    raygunKeys,
    routes,
    snackbarMessageContent,
    snackbarSeverityType,
    systemVersionWebConst,
    userRole
} from './common';
import { Amplify, Auth } from 'aws-amplify';
import axios from 'axios';
import useCognitoUser from './hooks/useCognitoUser';
import { MENU_ANIMATION_DURATION, VERSION_NUMBER, dispatchRaygunError, getProductMapping, mainConfig, navigatePage, setNavigate, updatedAllProductsData } from './helper';
import Header from './pages/header';
import Loader from './Loader/Loader';
import rg4js from "raygun4js";
import ForgotPassword from './pages/forgotPassword';
import { useWindowEvent } from '@mantine/hooks';
import UpdatePopup from './component/UpdatePopup/updatePopup';
import MatPopup from './component/MatPopup/MatPopup';
import OnboardingWelcome from './pages/Onboarding/onboardingWelcome';
import OnboardingTnc from './pages/Onboarding/onboardingTnC';
import OnboardingDetails from './pages/Onboarding/onboardingDetails';
import OnboardingThankYou from './pages/Onboarding/onboardingThankyou';
import ErrorBoundary from './component/Error/ErrorBoundary';
import ToastSnackbar from './component/Snackbar/ToastSnackbar';
import useSnackbarStore from './component/Snackbar/snackbarStore';
import { ReactComponent as CloseIcon } from '../../public/asset/Icon_Close.svg';
import useGetUserDiscountData from './hooks/useGetUserDiscountData';
import { ReferenceDataProduct } from './types/ReferenceDataProduct';
import { useSellerOrderStore, useGlobalStore, createSocket, addAxiosInterceptor, initializeAxiosResponseInterceptor, useGetSecurityData, generateHashFromEncryptedData, excludeSecurityHashApiList, CustomSocketProp, setEnvironment, getChannelWindow, setChannelWindow, steg, RequestInterceptorParams, refereshImpersonatedUserToken, useBuyerSettingStore, ueGetBuyingPreference, useCreatePoStore, usePostFetchProductPrices, compareVersions, useSearchStore , useSellerSettingStore, useGetReferenceNotificationSettings, useGetUserNotification , useChatWithVendorStore } from '@bryzos/giss-ui-library';
import useDialogStore from './component/DialogPopup/DialogStore';
import DialogBox from './component/DialogPopup/Dialog';
import { Chat } from './component/chat/Chat';
import { useImmer } from 'use-immer';
import Impersonate from './component/Impersonate/Impersonate';
import addErrorInterceptors from './utility/ErrorInterceptor';
import NoInternet from './component/NoInternet/NoInternet';
import VideoLibrary from './pages/VideoLibrary/videoLibrary';
import ChangePassword from './component/changePassword/changePassword';
import useGetReferenceData from './hooks/useGetReferenceData';
import useGetAllProducts from './hooks/useGetAllProducts';
import AuthenticationWrapper from './component/AuthenticationWrapper/AuthenticationWrapper';
// import { useHeightListener } from './hooks/useHeightListener';
import HeaderTop from './component/Header/Header';
import Search2 from './pages/Search-2';
import BomUpload from './pages/buyer/bom-upload/BomUpload';
import SubscribePage from './pages/Subscribe/Subscribe';
import AppUpdateNotification from './component/AppUpdateNotification';
import SubscriptionDialog from './component/SubscriptionDialog';
import { useSubscriptionStore } from './store/SubscriptionStore';
import AppStripeProvider from './component/SubscriptionDialog/AppStripeProvider';

import multipliers from './multipliers.json';
import EventLogger from './component/EventLogger/EventLogger';
import useGetSignedUrl from './hooks/useGetSignedUrl';
import packageConfig from '../../package.json';
import LeftPanel from './component/LeftPanel/LeftPanel';
import { useLeftPanelStore } from './component/LeftPanel/LeftPanelStore';
import RightWindow from './pages/RightWindow/RightWindow';
import clsx from 'clsx';
import { useRightWindowStore } from './pages/RightWindow/RightWindowStore';
import useGetBomSavedData from './hooks/useGetBOMSavedData';
import { set } from 'react-hook-form';
import BomProcessingWindow from './component/BomProcessingWindow/BomProcessingWindow';
import SavedBom from './pages/buyer/SavedBom/SavedBom';
import NewSetting from './pages/buyer/newSettings/NewSetting';
import BomPdfExtractor from './pages/buyer/BomPdfExtractor/BomPdfExtractor';
import useGetUserSubscription from './hooks/useGetUserSubscription';
import BomReview from './pages/buyer/BomReview/BomReview';
import GlobalMouseListeners from './utility/GlobalMouseListeners';
import SearchHeader from './pages/SearchHeader';
import useGetSaveSearchProducts from './hooks/useGetSaveSearchProducts';
import ViewPoHistory from './pages/buyer/ViewPoHistory/ViewPoHistory';
import useGetViewPoHistoryData from './hooks/useGetViewPoHistoryData';
import useGetChats from './hooks/useGetChats';
import SellerSettings from './pages/seller/Settings/SellerSettings';
import {ProductSearcher} from '@bryzos/steel-search-lib';
import { useBomPdfExtractorStore } from './pages/buyer/BomPdfExtractor/BomPdfExtractorStore';
import SellerOrderViewingPane from './pages/buyer/sellerOrderViewing/SellerOrderViewingPane';
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community';
import { useAppStore } from './store/AppStore';
import useGetQuoteList from './hooks/useGetQuoteList';
import useGetPurchasingList from './hooks/useGetPurchasingList';
import RouteTab from './component/LeftPanel/RouteTab/RouteTab';
import usePostExpirePricing from './hooks/usePostExpirePricing';
import useGetMutateSubscriptionPricing from './hooks/useGetMutateSubscriptionPricing';
import OrderManagement from './pages/OrderManagement';
import { useOrderManagementStore } from './store/OrderManagementStore';

rg4js("apiKey", import.meta.env.VITE_RAYGUN_KEY);
rg4js("enableCrashReporting", true);
rg4js('setVersion', VERSION_NUMBER);
rg4js('ignore', 'web_request_timing');
rg4js('options', {
  ignore3rdPartyErrors: true
});

// Save original console methods
// const originalConsoleMethods = { ...console };

if (!window.electron) {
    window.electron = {
        send: () => { },
        sendSync: () => { },
        receive: ()=>{},
        handleZoom: ()=>{},
        isWeb:true
    }
}

Amplify.configure({
  Auth: {
    region: import.meta.env.VITE_AWS_COGNITO_REGION,
    userPoolId: import.meta.env.VITE_AWS_COGNITO_USER_POOL_ID,
    userPoolWebClientId: import.meta.env
      .VITE_AWS_COGNITO_USER_POOL_WEB_CLIENT_ID,
    cookieStorage: {
      domain: import.meta.env.VITE_AWS_COGNITO_DOMAIN,
      path: import.meta.env.VITE_AWS_COGNITO_PATH,
      expires: Number(import.meta.env.VITE_AWS_COGNITO_EXPIRES),
      sameSite: import.meta.env.VITE_AWS_COGNITO_SAME_SITE,
      secure: Boolean(import.meta.env.VITE_AWS_COGNITO_SECURE),
    }
  },
  Analytics: { disabled: Boolean(import.meta.env.VITE_AWS_COGNITO_ANALYTICS_DISABLED) },
});

const listChannelWindow = window.electron.sendSync({ channel: 'setChannelWindows' });
setChannelWindow(listChannelWindow);
let _appVersion = null;
if(listChannelWindow?.electronVersion){
  _appVersion = window.electron.sendSync({ channel: listChannelWindow.electronVersion,})
}

let clearTimeoutScreenMove;

function App2() {
    const navigate = useNavigate();
    const location = useLocation();
    const { setSubscriptionDialogOpen } = useSubscriptionStore();
    
    if(location.pathname === routes.newUpdate){
      return <UpdatePopup />
    }
    const [user, setUser] = useImmer({'data': null, "zip": null });
    const [openMatPopup, setOpenMatPopup] = useState(false);
    const [disableInitialSecurityApiCall, setDisableInitialSecurityApiCall] = useState(false);
    const foregroundBackgroundActivityPayloadRef = useRef<CustomSocketProp>();
    const setIsRequiredSellerSettingsFilled = useGlobalStore(state => state.setIsRequiredSellerSettingsFilled);
    const { userData, setUserData, setSecurityHash, showErrorPopup, setShowErrorPopup, sdk, setSdk, setShowChatIcon, noInternetAccessibility, decryptionEntity, setDecryptionEntity, currentAccessToken, originalLoggedInUserData,  isImpersonatedUserLoggedIn,hasLoginProcessCompleted, setStartLoginProcess, apiFailureDueToNoInternet , bryzosPayApprovalStatus , removePoFromViewPoHistory , setRemovePoFromViewPoHistory} = useGlobalStore();
    const {viewedOrdersListForBadgeCount,backdropOverlay, setBackdropOverlay, setSellerCompanyName, isUserLoggedIn, discountData, forceLogout, setForceLogout , appVersion,setAppVersion,setSystemVersion , currentUser,setCurrentUser,productData, setProductData,setProductMapping,setReferenceData, onlineStatus, securityHash, setDeviceId, isSubscribeClickedDuringSignup, setIsSubscribeClickedDuringSignup, setReferenceDataUpdated, referenceDataUpdated, setFetchProductPricesMutateAsync, subscriptionStatus} = useGlobalStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setSellerSettings,  sellerSettings } = useSellerSettingStore();
    const {isRequiredSellerSettingsFilled} = useGlobalStore();



    const {
      data: cognitoUser,
    } = useCognitoUser(!window.electron?.isWeb ? isUserLoggedIn : true, securityHash);

    const getUserDiscountData = useGetUserDiscountData();
    const getReferenceData = useGetReferenceData();
    const getAllProductsHook = useGetAllProducts();
    const routerContentRef = useRef(null);

    const {
      refetch: refetchGetSecurity
    } = useGetSecurityData(cognitoUser);
    
    const [socket, setSocket] = useState(null);
    const resetHeaderConfig = useGlobalStore(state => state.resetHeaderConfig);
    const showLoader = useGlobalStore(state => state.showLoader);
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const disableBryzosNavigation = useGlobalStore(state => state.disableBryzosNavigation);
    const setDisableBryzosNavigation = useGlobalStore(state => state.setDisableBryzosNavigation);
    const channelWindow =  getChannelWindow()  ;
    const setNavigationStateForNotification = useGlobalStore(state => state.setNavigationStateForNotification);
    const purchaseOrdersList = useSellerOrderStore(state => state.ordersCart);
    const {showToastSnackbar, resetSnackbarStore, setSnackbarOpen, openSnackbar} = useSnackbarStore();
    const [pusherId, setPusherId] = useState(0);
    const [resumeApp, setResumeApp] = useState(false);
    const deviceId = useRef<string | null>(null);
    // const ref = useHeightListener(_appVersion, resumeApp);
    const getSignedUrl = useGetSignedUrl();
    const {openLeftPanel, closeWithoutAnimation , setLeftPanelData, leftPanelData ,  setLeftPanelViewPoHistoryData, leftPanelViewPoHistoryData} = useLeftPanelStore();
    const setSavedSearchProducts = useSearchStore(state => state.setSavedSearchProducts);
    const setQuoteList = useCreatePoStore(state => state.setQuoteList);
    const setPurchasingList = useCreatePoStore(state => state.setPurchasingList);
    const setOrderManagementData = useOrderManagementStore(state => state.setOrderManagementData);
    const routerContainerRef = useRef<HTMLDivElement>(null);
    const rightContentRef = useRef<HTMLDivElement>(null);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [wrapperContentWidth, setWrapperContentWidth] = useState(0);
    const {showVideo, loadComponent} = useRightWindowStore();
    const rightWindowRef = useRef<HTMLDivElement>(null);
    const [areRightWindowHeightsEqual, setAreRightWindowHeightsEqual] = useState(true);
    const [isMacDevice, setIsMacDevice] = useState(false);
    const mainWrapperRef = useRef<HTMLDivElement>(null);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const getBuyingPreference = ueGetBuyingPreference();
    const { setBuyerSettingInfo, buyerSetting  , userNotificationData , referenceNotificationSettings} = useBuyerSettingStore();
    const {setBomProcessingWindowProps, setLoadComponent} = useRightWindowStore();
    const {setBomProgressSocketData, savedBomSocketData, viewPoHistorySocketData, setViewPoHistorySocketData} = useCreatePoStore();
    const {mutateAsync: postFetchProductPrices} = usePostFetchProductPrices(true);
    const [updateBackdropOverlay, setUpdateBackdropOverlay] = useState(false);
    const {setProductSearcher} = useBomPdfExtractorStore();
    useGetUserSubscription();
    const mainWindowWidth = useAppStore(state => state.mainWindowWidth);
    const screenWidth = useAppStore(state => state.screenWidth);
    const screenHeight = useAppStore(state => state.screenHeight);
    const setScreenWidth = useAppStore(state => state.setScreenWidth);
    const setScreenHeight = useAppStore(state => state.setScreenHeight);
    const setIsFullScreen = useAppStore(state => state.setIsFullScreen);
    const isFullScreen = useAppStore(state => state.isFullScreen);
    const {mutateAsync: getReferenceNotificationSettings} = useGetReferenceNotificationSettings();
    const {mutateAsync: getUserNotification} = useGetUserNotification();
    const { notificationPriceChanges, notificationProductChanges, notificationCapgoUpdate, userSpreadPriceChange, setNotificationPriceChanges, setNotificationProductChanges } = useGlobalStore();
    const { mutateAsync: expirePricingMutation } = usePostExpirePricing();
    const updatedSavedSearchDataFromSocket = useSearchStore(state => state.updatedSavedSearchDataFromSocket);
    const {mutateAsync: getSubscriptionsPricing} = useGetMutateSubscriptionPricing();
    
    useWindowEvent('plugin_web_update_notice',  (e) => {
      const { version } = e.detail;
    });
    if(!window.electron.isWeb && compareVersions(_appVersion, '2.0.0') < 0) return <AppUpdateNotification 
      // ref={ref} 
      updateUrl="https://bryzos.com/download" 
      channelWindow={listChannelWindow}
    />;

    useEffect(()=>{
      if(postFetchProductPrices){
        setFetchProductPricesMutateAsync(postFetchProductPrices);
      }
    },[postFetchProductPrices])

  useEffect(() => {
    if (userData?.data && discountData) {
      userData.data.disc_is_discounted = discountData.isDiscounted;
      userData.data.disc_discount_rate = discountData.discountRate;
      userData.data.disc_discount_pricing_column = discountData.discountPricingColumn;
      setUserData({...userData});
      setProductMapping(getProductMapping(productData, userData.data));
    }
  }, [discountData, referenceDataUpdated]);

  useEffect(() => {
    setEnvironment(import.meta.env);
    getDeviceId();
    setDeviceId(deviceId.current);
    const steg_fun = steg(document);
    ModuleRegistry.registerModules([AllCommunityModule]);
    steg_fun.decode("/asset/space_5.png", (_data) => {
      setDecryptionEntity(JSON.parse(_data));
    })
    addErrorInterceptors();
    if(!disableInitialSecurityApiCall){
      addSecurityHashInInterceptor(true);
    }
    if(window.electron?.isWeb) setStartLoginProcess(true);
    // if(channelWindow?.resizeScreen){
    //   window.electron.send({ channel: channelWindow.resizeScreen, data: { width: MAX_APP_WIDTH, height: MAX_APP_HEIGHT }});
    // }
    if(channelWindow?.electronVersion){
      setAppVersion(window.electron.sendSync({ channel: channelWindow.electronVersion }));
    }else{
      setAppVersion(packageConfig.version);
    }
    initializeAxiosResponseInterceptor(setForceLogout, async(response)=>{
      await handleAxiosResponseData(response);
    });
    if(channelWindow?.systemVersion){
      const os = window.electron.sendSync({ channel: channelWindow.systemVersion, data: null});
      setIsMacDevice(os.includes('Mac'))
      setSystemVersion(os);
    }else{
      setSystemVersion(systemVersionWebConst)
    }
    if(window.electron.handleZoom)
    window.electron.handleZoom();

    if(channelWindow?.setCofigFromRenderer){
      window.electron.send({ channel: channelWindow.setCofigFromRenderer, data:mainConfig})
    }

    if(channelWindow?.logData){
      window.electron.receive(channelWindow.logData, (data)=>{
      })
    }

    if(channelWindow?.handleURI){
      window.electron.receive(channelWindow.handleURI,(uri) => {
        const [protocol, path] = uri.split('//');      
        console.log("uri",path)
        if(path){
            const isMail = path.split("/")[0] === "mail";
            const isChat = path.split("/")[0] === "chat";
          if(isMail){
            openAppUsingLinkHandler(path);
          }else if(isChat){
            console.log("isChat",path)
          }else{
            notificationUriHandler(path);
          }
        }
      })
    }
    if(channelWindow?.markAsReadNotification){
      window.electron.receive(channelWindow.markAsReadNotification,(notificationList) => {
        notificationList = JSON.parse(notificationList);
        markNotificationAsRead(notificationList);
      });
    }
    if(channelWindow?.productReferenceChanged){
      window.electron.receive(channelWindow.productReferenceChanged,() => {
        showOverlayToaster(snackbarMessageContent.productReferenceDataChanged, snackbarSeverityType.warning, [{name:commomKeys.tryAgain, handler: handleSnackbarClose}], null, true);
      });
    }
    if (channelWindow?.customNotification) {
      window.electron.receive(channelWindow.customNotification, (data) => {
        if (data?.notification) {
          showCustomNotification(data, true);
        }
      });
    }
    if(channelWindow?.getAccessToken){
      window.electron.receive(channelWindow.getAccessToken,(channelId) => {
        setPusherId(channelId);
      })
    }
    if(channelWindow?.fetchOnRendererMount){
      const electronData = window.electron.sendSync({ channel: channelWindow.fetchOnRendererMount });
      const isFullScreen = electronData.isFullScreen; 
      setIsFullScreen(isFullScreen);
      const bodyHeight = document.body.clientHeight;
      const bodyWidth = document.body.clientWidth;
      setScreenWidth(bodyWidth);
      setScreenHeight(bodyHeight);
    }
    if(channelWindow?.resumeApp){
      window.electron.receive(channelWindow.resumeApp,()=>{
        setResumeApp(true);
      })
    }
    if(channelWindow?.getSignedUrl){
      window.electron.receive(channelWindow.getSignedUrl,(data)=>{
        getSignedUrlForLogger(data)
      })
    }

    if(channelWindow?.toggleFullScreen){
      window.electron.receive(channelWindow.toggleFullScreen, (data)=>{
        const bodyHeight = document.body.clientHeight;
        const bodyWidth = document.body.clientWidth;
        setScreenWidth(bodyWidth);
        setScreenHeight(bodyHeight);
        setIsFullScreen(data);
      })
    }

    if(channelWindow?.handleOverlay){
      window.electron.receive(channelWindow.handleOverlay, (data)=>{
        setUpdateBackdropOverlay(data);
      })
    }
    if(channelWindow?.moveWindow){
      window.electron.receive(channelWindow.moveWindow, (data)=>{
        // setScreenWidth(data.width);
        // setScreenHeight(data.height);
        if(clearTimeoutScreenMove) clearTimeout(clearTimeoutScreenMove);
        clearTimeoutScreenMove = setTimeout(()=>{
          const bodyHeight = document.body.clientHeight;
          const bodyWidth = document.body.clientWidth;
          setScreenWidth(bodyWidth);
          setScreenHeight(bodyHeight);
        },1000)
      })
    }
  
    setNavigate(navigate);
    return () => {
      socket?.disconnect();
      setSocket(null);
    };
  }, []);

  useEffect(()=>{
    if(resumeApp && currentUser){
      checkDiscountPricing();
      setResumeApp(false);
    }
  },[resumeApp, currentUser])

  useEffect(() => {
    const handleNotifications = async () => {
      if (notificationPriceChanges || notificationProductChanges){
        const _userData = useGlobalStore.getState().userData;
        if (_userData.data.type === userRole.buyerUser) {
          if (notificationPriceChanges || notificationProductChanges) {
            await getAllProductsData();
          }
          setReferenceDataUpdated(Math.random());
          setNotificationPriceChanges(false);
          setNotificationProductChanges(false);
        }
      }
    };

    handleNotifications();
  }, [notificationPriceChanges, notificationProductChanges])

  useEffect(() => {
    let badgeCount = 0;
    if(viewedOrdersListForBadgeCount && purchaseOrdersList){
      const viewedOrderSet = new Set([...viewedOrdersListForBadgeCount]);
      badgeCount = purchaseOrdersList.reduce((count, order) => {
        if(!viewedOrderSet.has(order.buyer_po_number)) count++;
        return count;
      }, 0);
    }
    if(channelWindow?.badgeCountHandler)
    window.electron.send({ channel: channelWindow.badgeCountHandler, data:{type: 'set', count:badgeCount }});
  }, [purchaseOrdersList, viewedOrdersListForBadgeCount]);
  
  useEffect(() => {
    if(pusherId && channelWindow?.refreshPrivateChannel && cognitoUser){
      reconnectPusher()
    }
  },[pusherId])

  useEffect(()=>{
    if(showErrorPopup){
      showCommonDialog(null, commomKeys.errorContent, null, handleCloseErrorPopup, [{name: commomKeys.errorBtnTitle, action: handleCloseErrorPopup}])
    }
  },[showErrorPopup])

  useEffect(()=>{
    if(bryzosPayApprovalStatus){
      let buyerSettings = {...buyerSetting, bnpl_settings: bryzosPayApprovalStatus};
      setBuyerSettingInfo(buyerSettings);
    }
   },[bryzosPayApprovalStatus])

  useEffect(()=>{
    if(removePoFromViewPoHistory){
      const _viewPoHistoryData = leftPanelViewPoHistoryData.filter((item: any) => item.buyer_po_number !== removePoFromViewPoHistory?.po_number);
      setLeftPanelViewPoHistoryData(_viewPoHistoryData);
      setRemovePoFromViewPoHistory(null);
    }
  },[removePoFromViewPoHistory])

  useEffect(()=>{
    if(location.pathname !== routes.homePage) {
      expiredActivePricedProducts(expirePricingMutation);
    }
  },[location.pathname, updatedSavedSearchDataFromSocket])

  const handleAxiosResponseData = async(response) => {
    const urlSplit = response.config.url.split("/");
      const pathName = urlSplit[urlSplit.length - 1];
      if((!excludeSecurityHashApiList.find((excludeSecurityHashApi) => pathName.startsWith(excludeSecurityHashApi)) && response?.data?.data === 'Success' && response.status === 200)){
        showCommonDialog(null, commomKeys.errorContent, null, handlePopupClose, [{name: commomKeys.errorBtnTitle, action: handlePopupClose}])
      }
  }

  const getSignedUrlForLogger = (data) => {
    const payload = { data, deviceId: getDeviceId()}
    getSignedUrl.mutateAsync(payload);
    getSignedUrl.mutateAsync({...payload, data: { ...data, isOld : true}});
  }

  const handlePopupClose = () => {
    if(channelWindow?.refreshApp)
    window.electron.send({ channel: channelWindow.refreshApp });
    resetDialogStore();
  };

  const handleCloseErrorPopup = () => {
    setShowErrorPopup(false)
    addSecurityHashInInterceptor(true);
    setShowLoader(false)
    resetDialogStore();
  }

  const checkDiscountPricing = async () => {
    try {
      const userDiscount = await getUserDiscountData.mutateAsync();

      if (!(currentUser.disc_is_discounted === userDiscount.is_discount &&  currentUser.disc_discount_rate === userDiscount.discount_rate && currentUser.disc_discount_pricing_column === userDiscount.discount_pricing_column)) {
        showOverlayToaster(snackbarMessageContent.discountPriceChanged, snackbarSeverityType.warning, [{name:commomKeys.refresh, handler: handleSnackbarClose}], null, true);
      }
      const prevUser = {...currentUser,  disc_is_discounted: userDiscount.is_discount, disc_discount_rate: userDiscount.discount_rate, disc_discount_pricing_column: userDiscount.discount_pricing_column}
      setCurrentUser(prevUser);
    } catch (error) {
      console.log(error)
    }

  }

  const reconnectPusher = async () => {
    try{
      let accessToken;
      if (isImpersonatedUserLoggedIn) accessToken = await refereshImpersonatedUserToken(originalLoggedInUserData)
      else {
        const user = await Auth.currentAuthenticatedUser({ bypassCache: true });
        accessToken = user.signInUserSession.accessToken.jwtToken;
      }
      window.electron.send({ channel: channelWindow.refreshPrivateChannel, data: { channelId: pusherId, accessToken } });
    } catch(error) {
      console.log(error);
    } finally {
      setPusherId(null);
    }
  }

  const getLogData = ()=>{
    if(channelWindow?.logData){
      window.electron.send({ channel:channelWindow.logData, data:null}); // Send request to the main process
    }
  };
  
  const notificationUriHandler = (path) => {   
      let action = null;
      let cleanPath = path;
      if (path && path.includes(';action=')) {
        const actionParts = path.split(';action=');
        cleanPath = actionParts[0]; // Remove action part from path
        action = actionParts[1]; // Extract the action value
      }
      let [userId, notificationId, routePath, stateUrl] = cleanPath.split('/');
      
      const globalStore = useGlobalStore.getState();
      if((+userId) === (+globalStore?.currentUser?.id)){
        if(routePath)
        routePath = `/${routePath}`;
        const state = {};
        if(stateUrl){
          const stateUrlArray = stateUrl.split(',');
          for(const element of stateUrlArray){
            const [key, value] = element.split('=');
            state[key] = value;
          };
        }
        if(action){
          state.action = action;
        }
        let isValidRoutePath = false;
        for(const routeKey in routes){
          if(!isValidRoutePath && routes[routeKey] === routePath) isValidRoutePath=true;
        }
        if(isValidRoutePath){
          setNavigationStateForNotification(state);
          if(globalStore?.userData?.data?.type === userRole.sellerUser && routePath === routes.homePage){
            navigatePage(location.pathname, {path: getHomeRoute()})
          }else{
            navigatePage(location.pathname, {path:routePath})
          }
        }
      }
      else{
        console.log(`Notication for User: ${userId} but found currentUser:${currentUser.id}`);
      }
  }


  const markNotificationAsRead = (notificationList) => {
    const deviceId = getDeviceId();
    const payload = {data:{notification_id: [], device_id: deviceId } };
    for(const notification of notificationList){
      payload.data.notification_id.push(notification.notificationId);
    }
    axios.post(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/markAsRead', payload)
    .then(res => {
      if(channelWindow?.isMarkAsReadNotification)
      window.electron.send({ channel: channelWindow?.isMarkAsReadNotification, data:true })
    })
    .catch(err => {
      if(channelWindow?.isMarkAsReadNotification)
      window.electron.send({ channel: channelWindow?.isMarkAsReadNotification, data:false })
    });
  }

  const openAppUsingLinkHandler = (path) => {
    if(path.indexOf("mail/create-po") >= 0){
      if(currentUser?.type === userRole.buyerUser)
      navigate(routes.createPoPage);

    }else if(path.indexOf("mail/buyer-setting") >= 0){
      if(currentUser?.type === userRole.buyerUser)
      navigate(routes.buyerSettingPage);
    }else{

    }
  }

  const handleSnackbarClose = async (event, reason) => {
    setSnackbarOpen(false);
    try {
        await axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data/homepage');
        resetSnackbarStore();
        if(channelWindow?.refreshApp)
        window.electron.send({ channel: channelWindow.refreshApp });
    } catch (error) {
        setSnackbarOpen(true);
    }
  };

  const onlyCloseSnackbar = async () => {
    setSnackbarOpen(false);
    resetSnackbarStore();
    setBackdropOverlay(false);
  };

  const showOverlayToaster = (message, severity, buttons, closeHandler, showOverlay)=>{
    showToastSnackbar(message, severity, buttons, closeHandler);
    if(showOverlay)
    setBackdropOverlay(true);
  }

  const socketConnectionErrorHandler = (message) => {
    showOverlayToaster(message, snackbarSeverityType.alert, [{name:commomKeys.tryAgain, handler: handleSnackbarClose}], null, true);
  }
  const removeSocketDisconnectToaster = ()=>{
    resetSnackbarStore();
    setBackdropOverlay(false);
  }

  const onSocketConnectionError = (errorMsg)=>{
    dispatchRaygunError(
      new Error(errorMsg),
      [raygunKeys.socketInvalidToken.tag]
    );
    if(channelWindow?.refreshApp)
    window.electron.send({ channel: channelWindow.refreshApp });
    else
    setForceLogout(true);
  }

  async function createSocketConnection(userData, accessToken){
    const extraHeaders= {
      "gissToken": import.meta.env.VITE_WEB_SOCKET_GISS_TOKEN,
      "email": userData.email_id,
      "accessToken":  accessToken
    };
    if (!deviceId.current) {
      deviceId.current = await getDeviceId();
    }
    const data = await getOsAndLastLoginAppVersion();
    const object = {
      email_id: userData.email_id,
      os_version: data.os_version,
      last_login_app_version: data.last_login_app_version,
      device_id: deviceId.current,
      ui_version: import.meta.env.VITE_RENDERER_DEPLOY_VERSION,
    }
    foregroundBackgroundActivityPayloadRef.current = object;

    const socketProps = {
      userRole: userData.type,
      socketConnectionErrorHandler: socketConnectionErrorHandler,
      removeSocketDisconnectToaster:  removeSocketDisconnectToaster,
      onSocketConnectionError: onSocketConnectionError,
      ...object
    }
    const newSocket = await createSocket(import.meta.env.VITE_WEB_SOCKET_SERVER,extraHeaders, socketProps);
    setSocket(newSocket);
  }


  const { mutateAsync: getBomData, isPending } = useGetBomSavedData();
  const { mutateAsync: getSaveSearchProducts } = useGetSaveSearchProducts();
  const { mutateAsync: getQuoteList } = useGetQuoteList();
  const { mutateAsync: getPurchasingList } = useGetPurchasingList();
  const { mutateAsync: getViewPoHistoryData } = useGetViewPoHistoryData();
  const {mutateAsync: getChats} = useGetChats();
  const {setAllChatPOs} = useChatWithVendorStore()

  const getRefenceData = async (user, isImpersonated = false) => {
    const response = await getReferenceData.mutateAsync();
    const referenceData = response.data;
    let poHistoryData = [];
    const isBuyer = user.type === userRole.buyerUser;
    if(isBuyer){
      poHistoryData = await initBuyerSpecificData();
    }
    await initChatData(poHistoryData, isBuyer);
    await initialiseReferenceDataAndNavigate(user, referenceData, isImpersonated);
  }

  useEffect(() => {
    if (savedBomSocketData) {
        const idToFind = savedBomSocketData?.id;
        const index = leftPanelData.findIndex((item: any) => item.id === idToFind);
        let updatedData = [...leftPanelData];
        if (index !== -1) {
            // Replace the object if id matches
            updatedData[index] = savedBomSocketData;
        } else {
            // Add the new object to the top if no match is found
            updatedData = [savedBomSocketData, ...updatedData];
        }
        setLeftPanelData(updatedData);
    }
  }, [savedBomSocketData]);

  // useEffect(() => {
  //   if (viewPoHistorySocketData) {
  //       const poToFind = viewPoHistorySocketData?.buyer_po_number;
  //       let updatedData = [...leftPanelViewPoHistoryData];

  //       // if (viewPoHistorySocketData.type === 'BUYER_ORDER_CANCELLED') {
  //       //     // Remove the PO if it's cancelled
  //       //     updatedData = updatedData.filter((item: any) => item.buyer_po_number !== poToFind);
  //       // } else {
  //           const index = leftPanelViewPoHistoryData.findIndex((item: any) => item.buyer_po_number === poToFind);
  //           if (index !== -1) {
  //               // Replace the object if id matches
  //               updatedData[index] = viewPoHistorySocketData;
  //           } else {
  //               // Add the new object to the top if no match is found
  //               updatedData = [viewPoHistorySocketData, ...updatedData];
  //           }
  //       // }
  //       setLeftPanelViewPoHistoryData(updatedData);
  //       setViewPoHistorySocketData(null)
  //   }
  // }, [viewPoHistorySocketData]);

  const initChatData = async (poHistoryData, isBuyer)=>{
    try{
      // const chatData = await getChats();
      // console.log('>>>>>>>>>>>>>>>>>>>>>>>chatData',chatData);
      // setAllChatPOs(chatData || []);
       setAllChatPOs([])
      // if(isBuyer && poHistoryData?.length > 0){
      //   const allPos = [];
      //   const allPoMap = {};
      //   poHistoryData.forEach(item=>{
      //     allPos.push(item.buyer_po_number);
      //     allPoMap[item.buyer_po_number] = {
      //       buyerPoNumber: item.buyer_po_number,
      //       buyerInternalPo: item.buyer_internal_po,
      //       sellerCompany: item.fulfilled_by,
      //       sellerEmail: item.claimed_by,
      //     };
      //   })
      //   setAllPos(allPos);
      //   setAllPoMap(allPoMap);
      // }
    }catch(error){
      console.log(error);
    }
  }

  const initBuyerSpecificData = async ()=>{
    try{
      const bomDataLocal = getLocal(localStorageKeys.bomData, null);
      let breakPoint = false;
      // const viewPoHistoryData = {};
      const viewPoHistoryData = await getViewPoHistoryData();
      console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>',viewPoHistoryData.data);
      setOrderManagementData(viewPoHistoryData.data ?? []);
      // setLeftPanelViewPoHistoryData(viewPoHistoryData.data ?? []);
      // const savedBoms = await getBomData();
      // setLeftPanelData(savedBoms.data ?? []);
      const savedSearchProductsData = await getSaveSearchProducts();
      setSavedSearchProducts(savedSearchProductsData.data ?? []);
      const quoteListData = await getQuoteList();
      setQuoteList(quoteListData.data ?? []);
      const purchasingListData = await getPurchasingList();
      setPurchasingList(purchasingListData.data ?? []);
      if(bomDataLocal){
        savedBoms.data.forEach((item:any)=>{
        if(item.status === "INPROGRESS"&& !breakPoint){
          if(deviceId.current === item.device_id && bomDataLocal.bomProcessingWindowProps?.bomUploadId === item.id){
              breakPoint = true;
              //load bom processing window
              setBomProcessingWindowProps(bomDataLocal.bomProcessingWindowProps);
              setBomProgressSocketData(bomDataLocal.bomProgressSocketData);
              setLoadComponent(<BomProcessingWindow/>);
            }
          }
        })
      }
      return viewPoHistoryData?.data ?? [];
    }catch(error){
      console.log(error);
    }
  }

  const getAllProductsData = async (user) => {
    setFetchProductPricesMutateAsync(postFetchProductPrices);
    const userData = useGlobalStore.getState().userData;
    const productSearcher = useBomPdfExtractorStore.getState().productSearcher;
    const response: ReferenceDataProduct[] = await getAllProductsHook.mutateAsync();
    setProductMapping(getProductMapping(response.data, user ?? userData.data));
    setProductData(response.data);
    if(productSearcher){
      productSearcher.init(response.data);
    }else{
      const productSearcher = new ProductSearcher(response.data);
      setProductSearcher(productSearcher);
    }
  }

  const initialiseReferenceDataAndNavigate = async (user, referenceData, isImpersonated) => {
    await getAllProductsData(user);
    const currentTandC = user.current_tnc_version;
    const acceptedTandC = user.accepted_terms_and_condition;
    setUserData({ "data": user, multipliers});
    setReferenceData(referenceData)
    if (location.pathname !== routes.newUpdate) {
      if (!isImpersonated && (currentTandC !== acceptedTandC || currentTandC === null || acceptedTandC === null)) {
        navigate(routes.TnCPage, {state : { isViewMode: false, navigateTo: getHomeRoute() }});
      }
      else if(channelWindow?.getLoginCredential && !isImpersonated && user.is_migrated_to_password <= 1){
        setShowLoader(false);
        navigate(routes.changePassword);
      } else if(isSubscribeClickedDuringSignup && user?.type === userRole.buyerUser){
          navigate(routes.subscribe);
      }
      else {
        setDisableBryzosNavigation(false);  
        if (!discountData) {
          navigate(getHomeRoute());
        }
      }
      setIsSubscribeClickedDuringSignup(false);
    };
  }

  const addSecurityHashInInterceptor = async (noAccessTokenRequire, currentAccessToken, userData) => {
    try{
      const res = await refetchGetSecurity();
      if(res.data){
        const securityHash = await generateHashFromEncryptedData(res.data, import.meta.env.VITE_SECRET_KEY);
        setSecurityHash(securityHash);
        setDisableInitialSecurityApiCall(true);
        axios.interceptors.request.clear();
        const params:RequestInterceptorParams = {
          impersonatedToken: currentAccessToken
        }
        addAxiosInterceptor(() => setForceLogout(true), (request) => {
          request.headers.security = securityHash;
          if(userData) request.headers["super-admin"] = userData.data.id
        }, noAccessTokenRequire, params);
      }
    } catch (error) {
      console.log(error);
    }
    
  }

  const getOsAndLastLoginAppVersion = async () => {
    type responseType = { os_version: null | string; last_login_app_version: null | string; };
    let systemVersion = null;
    if (channelWindow?.systemVersion) {
    systemVersion = window.electron.sendSync({ channel: channelWindow.systemVersion });
    }
    const response: responseType = { os_version: systemVersion, last_login_app_version: appVersion };
    return response;
  }

  const getDeviceId = () => {
    if (!deviceId.current && channelWindow?.getDeviceId) {
      try {
        const id = window.electron.sendSync({ channel: channelWindow.getDeviceId });
        if (id && typeof id === 'string') {
          deviceId.current = id;
          return id;
        } else {
          deviceId.current = null;
          return null;
        }
      } catch (e) {
        deviceId.current = null;
        return null;
      }
    }
    return deviceId.current;
  };

  const showCustomNotification = (data: any) => {
    if (data) {
      let action = data.notification.action;

      const message = data.notification.body;
      const severity = data.notification.priority;
      let buttons;
      if (action === CUSTOM_NOTIFICATION_ACTION[0]) {
        buttons = [{ name: CUSTOM_NOTIFICATION_ACTION[0], handler: handleSnackbarClose }];
      } else if (action === CUSTOM_NOTIFICATION_ACTION[1]) {
        buttons = [{ name: "", handler: onlyCloseSnackbar, icon: <CloseIcon /> }];
      }
      showOverlayToaster(message, severity, buttons, null, true);
    }
  }

useEffect(() => {
  if (!userData?.data && sdk) {
    sdk.logout();
  }
  if (userData?.data?.id && userRole.sellerUser === userData?.data?.type) {
    axios
      .get(import.meta.env.VITE_API_SERVICE + "/user/sellingPreference", {
        headers: {
          UserId: userData.data.id,
        },
      })
      .then((response) => {
        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            setIsRequiredSellerSettingsFilled(false);
          } else {
            const sellerSettingData = response.data.data;
            setSellerCompanyName(sellerSettingData?.company_name ?? "");
            setSellerSettings(sellerSettingData);
            if(sellerSettingData?.company_name &&
              sellerSettingData?.company_address?.line1 && 
              sellerSettingData?.company_address?.city && 
              sellerSettingData?.company_address?.state_id && 
              sellerSettingData?.company_address?.zip && 
              sellerSettingData?.first_name && 
              sellerSettingData?.last_name && 
              sellerSettingData?.email_id && 
              sellerSettingData?.phone ){
              setIsRequiredSellerSettingsFilled(true);
            }else{
              setIsRequiredSellerSettingsFilled(false)
            }
          }
        } else {
          setIsRequiredSellerSettingsFilled(false);
        }
      })
      .catch(() => {
        setIsRequiredSellerSettingsFilled(false);
      });
  }
  if(userData?.data?.type === userRole.buyerUser){
    const getBuyerPreferences = async () => {
      try {
        const buyingPreference = await getBuyingPreference.mutateAsync();
        if (buyingPreference?.data?.data !== 'SUCCESS') {
          const buyerPreferenceData = buyingPreference.data.data;
          setBuyerSettingInfo(buyerPreferenceData);
        }
      } catch (error) {
        console.error('Error fetching buyer preferences:', error);
      }
    };
    const fetchReferenceNotificationSettings = async () => {
      await getReferenceNotificationSettings();
      await getUserNotification();
    }
    getSubscriptionsPricing();
    getBuyerPreferences();
    fetchReferenceNotificationSettings();
  }

}, [userData?.data]);

useEffect(()=>{
  getLogData()
  if(window.electron?.isWeb){
    navigate(routes.loginPage);
  }
},[])


const openUpdateLink = ()=>{
  window.open(import.meta.env.bryzosWebUrl);
  setTimeout(()=>{
    if(channelWindow?.close)
    window.electron.send({ channel: channelWindow.close })
  },0);
}

  const initiateDeadSimpleChat = async (data: any) => {
      try {
        const sdk = new DSChatSDK(import.meta.env.VITE_DEAD_SIMPLE_CHAT_ROOM_ID, 'chat-frame', import.meta.env.VITE_DEAD_SIMPLE_CHAT_PUBLIC_KEY);
        await sdk.connect();
        if (data.is_moderator) {
          await sdk.joinRoom({ accessToken: data.access_token });
        } else {
          await sdk.joinRoom({ uniqueUserIdentifier: data.unique_user_identifier });
        }
        setSdk(sdk);
        setShowChatIcon(true);
      } catch (e) {
        setShowChatIcon(false);
        console.log(e);
      }
  };

  const createDeadSimpleChatUser = async () => {
    try {
      const response = (await axios.post(import.meta.env.VITE_API_SERVICE + '/user/createUser', null)).data?.data;
      return { unique_user_identifier: response };
    } catch (error) {
      return null;
    }
  }

  // Default theme set to 'dark'
  const [theme, setTheme] = useState('dark');

  useEffect(() => {
      // Set the initial theme on mount
      document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  const handleToggle = () => {
      setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  const [hideCloseAndMinimizeBtn, setHideCloseAndMinimizeBtn] = useState(false);

  useEffect(()=>{
    // if(!isMacDevice){
      setHideCloseAndMinimizeBtn(true);
      setTimeout(()=>setHideCloseAndMinimizeBtn(false), MENU_ANIMATION_DURATION)
    // }
    if(openLeftPanel)
      setIsMenuOpen(true);
    else
      setTimeout(()=>{
        setIsMenuOpen(false);
      }, MENU_ANIMATION_DURATION);
  },[openLeftPanel])

  useEffect(() => {
    const calculateWidth = () => {
      const routerContainerWidth = routerContainerRef.current?.offsetWidth || 0;
      const rightContentWidth = rightContentRef.current?.offsetWidth || 0;
      const totalWidth = routerContainerWidth + rightContentWidth + 48 + 351;
      setWrapperContentWidth(totalWidth > 800 ? totalWidth : 848);
    };

    // Create a ResizeObserver instance
    const resizeObserver = new ResizeObserver(() => {
      calculateWidth();
    });

    // Observe both elements if they exist
    if (routerContainerRef.current) {
      resizeObserver.observe(routerContainerRef.current);
    }
    if (rightContentRef.current) {
      resizeObserver.observe(rightContentRef.current);
    }

    // Initial calculation
    calculateWidth();

    // Cleanup
    return () => {
      resizeObserver.disconnect();
    };
  }, [location.pathname, isMenuOpen, showLoader]);

  useEffect(() => {
    const checkDivHeights = () => {
      if (routerContainerRef.current && rightWindowRef.current) {
        const height1 = routerContainerRef.current.clientHeight;
        const height2 = rightWindowRef.current.clientHeight;
        setAreRightWindowHeightsEqual(height1 === height2);
      }
    };
  
    // Create a separate ResizeObserver for height comparison
    const heightObserver = new ResizeObserver(() => {
      checkDivHeights();
    });
  
    // Observe the divs we want to compare
    if (routerContainerRef.current) {
      heightObserver.observe(routerContainerRef.current);
    }
    if (rightWindowRef.current) {
      heightObserver.observe(rightWindowRef.current);
    }
  
    // Initial check
    checkDivHeights();
  
    // Cleanup
    return () => {
      heightObserver.disconnect();
    };
  }, [loadComponent]);
  const { setEmitAppCloseEvent} = useGlobalStore();

  const closeBtnClick = () => {
    if(channelWindow?.close){
        setEmitAppCloseEvent(true);
        window.electron.send({ channel: channelWindow.close })
    }
  }

  const minimizeBtnClick = () => {
      if(channelWindow?.minimize){
          window.electron.send({ channel: channelWindow.minimize })
      }
  }


 const showSideBar = !(location.pathname === routes.loginPage ||
  location.pathname === routes.forgotPassword
  || location.pathname === routes.onboardingWelcome ||
  location.pathname === routes.changePassword
  || location.pathname === routes.onboardingDetails || location.pathname === routes.onboardingTnc || location.pathname === routes.onboardingThankYou);

const handleDoubleClick = () => {
  if(channelWindow?.adjustWindowView){
    window.electron.send({ channel: channelWindow.adjustWindowView })
  }
}
const { subscriptionDialogOpen } = useSubscriptionStore();

return ( 
    <div  className={clsx(isMacDevice && 'isMacDevice', 'appFullScreen')} style={{minWidth: screenWidth, minHeight: screenHeight, overflow: 'hidden'}}>
      <EventLogger />
      <GlobalMouseListeners />
    
      {(isMacDevice && !isFullScreen) && <div className="macTitleBar" onDoubleClick={handleDoubleClick}>
      {/* <div className="macTitleBarLeft">
        <div className="btn close-btn" onClick={closeBtnClick}></div>
        <div className="btn min-btn" onClick={minimizeBtnClick}></div>
      </div> */}
      </div>}
      <div className={`widgetCanvas  ${location.pathname === routes.loginPage || location.pathname === routes.forgotPassword ? 'appBg' : 'noBg'}   ${window.electron.isWeb ? 'webBackground' : ''}  ${(noInternetAccessibility || apiFailureDueToNoInternet || !onlineStatus)  ? 'noInternetWindow' : ''}` }>
      {/* {isUserLoggedIn && <div className={clsx('closeAndMinimizeBtn', isMacDevice && 'macCloseAndMinimizeBtn', hideCloseAndMinimizeBtn && 'hideCloseAndMinimizeBtn', (isMacDevice && !isMenuOpen) && 'positionCloseAndMinimizeBtn')}>
        <div className="btn close-btn" onClick={closeBtnClick}></div>
        <div className="btn min-btn" onClick={minimizeBtnClick}></div>
      </div>} */}
      
        <div className='mainContainer'>
         <SearchHeader isMacDevice={isMacDevice} handleDoubleClick={handleDoubleClick}/>
         <ErrorBoundary>
        <AuthenticationWrapper cognitoUser={cognitoUser} addSecurityHashInInterceptor={addSecurityHashInInterceptor} getDeviceId={getDeviceId} removeSocketDisconnectToaster={removeSocketDisconnectToaster} getRefenceData={getRefenceData} createSocketConnection={createSocketConnection} showCustomNotification={showCustomNotification} markNotificationAsRead={markNotificationAsRead} foregroundBackgroundActivityPayloadRef={foregroundBackgroundActivityPayloadRef} showOverlayToaster={showOverlayToaster} getSignedUrlForLogger={getSignedUrlForLogger} isLoggingOut={isLoggingOut} setIsLoggingOut={setIsLoggingOut}>
        {
          showLoader &&
          <div className={`loaderContent referenceDataLoader ${isUserLoggedIn ? 'loaderMain' : 'loginLoader'}`}>
            <Loader />
          </div>
        }
        {/* 
        {(location.pathname !== routes.onboardingWelcome && location.pathname !== routes.loginPage && location.pathname !== routes.forgotPassword ) &&  
          <HeaderTop/>
        } */}
        {/* {!openLeftPanel && <div className={clsx('ghostContainer')}></div>} */}
        <div className="mainViewWindow">
        <NoInternet />
          {showSideBar && <div className={'routeTab'} style={{width: `${screenWidth*0.0389}px`}}>
              <RouteTab />
          </div>}
          <div className={clsx('WrapperContent')}>
            <div id="wrapperContentOverlay" className="wrapperContentOverlay"></div>
          
        { showSideBar &&
            <>
              <LeftPanel routerContentRef={routerContentRef} isMenuOpen={isMenuOpen} updateBackdropOverlay={updateBackdropOverlay}/>
            </>
        }
            <div className={clsx('bgImg')} ref={mainWrapperRef} style={{width: mainWindowWidth ? `${mainWindowWidth}%` : 'auto', flex: mainWindowWidth===null ? '1' : 'none'}}>
            {(backdropOverlay || updateBackdropOverlay) && 
                    <div className='backdropOverlay' />
                }
                {location.pathname !== routes.newUpdate && (
                <div className={openSnackbar ? 'snackBarMarginTop' : ''}>
                  <ToastSnackbar />
                </div>
              )}
              <DialogBox parentRef={mainWrapperRef}/>
                  {
                    subscriptionDialogOpen &&
                    <AppStripeProvider>
                      <SubscriptionDialog />
                    </AppStripeProvider>

                  }
              {(location.pathname !== routes.loginPage && location.pathname !==  routes.forgotPassword && location.pathname !== routes.onboardingWelcome) && <div className={clsx('wrapperOverlay1', areRightWindowHeightsEqual && 'fillBorderRightBottom', !!loadComponent && 'fillBorderRightTop', isMenuOpen && 'wrapperMenuBorder')}></div>}
              <div className={clsx('routerContainer')}>
                <div className={clsx('routerContent')} ref={routerContainerRef}>
                
                <div className={` ${location.pathname !== routes.loginPage ? 'headerPanel commonHeader' : `loginBody`} ${showLoader && 'loaderRunning'}`} ref={routerContentRef}>
                  <Routes>
                      <Route exact path={routes.onboardingWelcome} element={<OnboardingWelcome />} />
                      <Route path={routes.onboardingDetails} element={<OnboardingDetails />} />
                      <Route path={routes.onboardingTnc} element={<OnboardingTnc />} />
                      <Route path={routes.onboardingThankYou} element={<OnboardingThankYou />} />
                      <Route path={routes.loginPage} element={<Login/>} />
                      <Route exact path={routes.forgotPassword} element={<ForgotPassword />} />
                      <Route exact path={routes.changePassword} element={<ChangePassword getDeviceId={getDeviceId}/>} />
                      <Route path={routes.homePage} element={<Home />} />
                      <Route path={routes.TnCPage} element={<Tnc />} />
                      <Route path={routes.successPage} element={<Success />} />
                      <Route path={routes.subscribe} element={<SubscribePage />} />
                      <Route path={routes.buyerSettingPage} element={<NewSetting mainWrapperRef={mainWrapperRef} routerContainerRef={routerContainerRef} />} />
                      <Route path={routes.sellerSettingPage} element={<SellerSettings mainWrapperRef={mainWrapperRef}/>} />
                      <Route path={routes.createPoPage} element={<CreatePo />} />
                      <Route path={routes.videoLibrary} element={<VideoLibrary />} />
                      <Route path={routes.orderConfirmationPage} element={<OrderConfirmation />} />
                      <Route path={routes.acceptOrderPage} element={<AcceptOrder />} />
                      <Route path={routes.disputePage} element={<Dispute />} />
                      <Route path={routes.orderPage} element={<SellerOrderViewingPane />} />
                      <Route path={routes.previewOrderPage} element={<SellerOrderViewingPane />} />
                      <Route path={routes.deleteOrderPage} element={<SellerOrderViewingPane />} />
                      <Route path={routes.chat} element={<Chat />} />
                      <Route path={routes.impersonateList} element={<Impersonate  appVersion={_appVersion}/>} />
                      <Route path={routes.bomExtractor} element={<BomPdfExtractor />} />
                      <Route path={routes.bomUpload} element={<CreatePo />} />
                      <Route path={routes.bomUploadReview} element={<BomReview />} />
                      <Route path={routes.savedBom} element={<SavedBom />} />
                      <Route path={routes.viewPoHistory} element={<ViewPoHistory />} />
                      <Route path={routes.quotePage} element={<CreatePo />} />
                      <Route path={routes.orderManagementPage} element={<OrderManagement />} />
                    </Routes>
                </div>
                </div>
              </div>
            </div>
            {(showSideBar && isUserLoggedIn && location.pathname !== routes.bomExtractor) && <div className={clsx('RightContent', mainWindowWidth!==null && 'flexSpace')} ref={rightContentRef}>
                {!(apiFailureDueToNoInternet || noInternetAccessibility || !onlineStatus) && <RightWindow rightWindowRef={rightWindowRef} routerContentRef={routerContentRef} updateBackdropOverlay={updateBackdropOverlay}/>}
                {/* <img src='/corner2.svg' alt='acrylic' className='acrylicCorner' /> */}
            </div>}
          </div>
        </div>
        
        <MatPopup open={openMatPopup}
          popupClass={{
            root: 'UpdateButton',
            paper: 'dialogContent'
          }}>
          <div className='excitingNews'>
            <label>Exciting News! </label>
            <p>We've added auto-updates to the app. Please uninstall your current app, then download the latest version from <span onClick={openUpdateLink}>here</span>. After this one-time manual update, all future updates will be automatic. Thanks for your support!</p>
          </div>
        </MatPopup>
        </AuthenticationWrapper>
        </ErrorBoundary>
        </div>
      {/* <div className='bgEllips'></div>
      <div className='bgEllips1'></div>  */}
      {/* <label className="toggle-switch">
                    <input
                        type="checkbox"
                        onChange={handleToggle}
                        checked={theme === 'dark'}
                    />
                    <span className="slider"></span>
                </label> */}
    </div>
    </div>
);
}
  
export default App2;