<svg width="258" height="9" viewBox="0 0 258 9" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_933_1729)">
<rect x="258" width="9" height="258" rx="1" transform="rotate(90 258 0)" fill="url(#paint0_linear_933_1729)"/>
</g>
<defs>
<filter id="filter0_i_933_1729" x="0" y="0" width="261" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="8" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_933_1729"/>
<feOffset dx="3" dy="2"/>
<feGaussianBlur stdDeviation="15.4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.430833 0 0 0 0 0.810278 0 0 0 0 1 0 0 0 0.91 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_933_1729"/>
</filter>
<linearGradient id="paint0_linear_933_1729" x1="262.596" y1="-8.11254e-06" x2="301.871" y2="6.29627" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#01AAFF"/>
</linearGradient>
</defs>
</svg>
