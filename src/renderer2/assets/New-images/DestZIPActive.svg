<svg width="91" height="41" viewBox="0 0 91 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_78_462)">
<rect x="0.125977" y="0.176025" width="90" height="40" rx="10" fill="white" fill-opacity="0.04"/>
<rect x="0.375977" y="0.426025" width="89.5" height="39.5" rx="9.75" stroke="url(#paint0_linear_78_462)" stroke-width="0.5"/>
<rect x="0.375977" y="0.426025" width="89.5" height="39.5" rx="9.75" stroke="url(#paint1_linear_78_462)" stroke-width="0.5"/>
</g>
<defs>
<filter id="filter0_ii_78_462" x="-3.87402" y="-0.823975" width="95.5" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.5" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_78_462"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-5" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_78_462" result="effect2_innerShadow_78_462"/>
</filter>
<linearGradient id="paint0_linear_78_462" x1="-1.24902" y1="29.176" x2="83.6514" y2="29.7019" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBE73"/>
<stop offset="1" stop-color="#2F2E33" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_78_462" x1="69.5885" y1="172.866" x2="113.673" y2="165.899" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D1B1B" stop-opacity="0"/>
<stop offset="1" stop-color="#FFAE00"/>
</linearGradient>
</defs>
</svg>
