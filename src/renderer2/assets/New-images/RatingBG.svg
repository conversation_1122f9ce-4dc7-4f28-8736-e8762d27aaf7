<svg width="602" height="94" viewBox="0 0 602 94" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_682_2798)">
<path d="M0 0H602V94H0V0Z" fill="url(#paint0_linear_682_2798)"/>
</g>
<path d="M0.5 0.5H601.5V93.5H0.5V0.5Z" stroke="url(#paint1_linear_682_2798)" stroke-opacity="0.26"/>
<defs>
<filter id="filter0_i_682_2798" x="0" y="0" width="603" height="95" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_682_2798"/>
<feOffset dx="8" dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_682_2798"/>
</filter>
<linearGradient id="paint0_linear_682_2798" x1="18" y1="-41.8352" x2="154.682" y2="374.782" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F0F14"/>
<stop offset="1" stop-color="#393E47"/>
</linearGradient>
<linearGradient id="paint1_linear_682_2798" x1="190.667" y1="122.753" x2="181.015" y2="44.3089" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
</defs>
</svg>
