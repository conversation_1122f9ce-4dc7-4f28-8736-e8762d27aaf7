<svg width="367" height="157" viewBox="0 0 367 157" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#e4g1vdpmta)">
        <rect x="1" y="1" width="365" height="155" rx="12" fill="#1B1C21"/>
    </g>
    <rect x=".5" y=".5" width="366" height="156" rx="12.5" stroke="url(#w3ap9xvk0b)" stroke-opacity=".33"/>
    <g filter="url(#st6qqux6qc)">
        <path d="M1 46h365v98c0 6.627-5.373 12-12 12H13c-6.627 0-12-5.373-12-12V46z" fill="#1B1C21"/>
    </g>
    <defs>
        <filter id="e4g1vdpmta" x="0" y="0" width="371" height="161" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="4" dy="4"/>
            <feGaussianBlur stdDeviation="5.05"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_1338_4126"/>
        </filter>
        <filter id="st6qqux6qc" x="1" y="46" width="369" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="4" dy="4"/>
            <feGaussianBlur stdDeviation="5.05"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_1338_4126"/>
        </filter>
        <linearGradient id="w3ap9xvk0b" x1="326.5" y1="233.5" x2="234.233" y2="-7.345" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#1A1B20"/>
        </linearGradient>
    </defs>
</svg>
