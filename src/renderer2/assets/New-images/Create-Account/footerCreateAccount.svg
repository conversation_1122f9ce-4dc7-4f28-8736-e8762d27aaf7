<svg width="803" height="127" viewBox="0 0 803 127" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#binuk5u0ga)">
        <g clip-path="url(#q6iwj40jlb)">
            <path d="M1.4 0h800.2v104.026c0 8.839-7.165 16.004-16.004 16.004H17.404c-8.839 0-16.004-7.165-16.004-16.004V0z" fill="#0F0F14"/>
            <g opacity=".8" filter="url(#19ytkh891c)">
                <circle cx="733.481" cy="145.044" r="40.01" fill="#9786FF"/>
                <circle cx="733.481" cy="145.044" r="40.01" fill="url(#451ztbu3vd)" fill-opacity=".8"/>
                <g clip-path="url(#ragmuywl9e)" data-figma-skip-parse="true">
                    <foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2" transform="matrix(.04001 .0524 -.0375 .0732 733.481 148.294)">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#fefefe 0deg,rgba(255,255,255,0) 66.6644deg,#fff 143.541deg,rgba(255,255,255,0) 199.792deg,#fff 272.918deg,rgba(255,255,255,0) 349.795deg,#fefefe 360deg);height:100%;width:100%;opacity:.5"/>
                    </foreignObject>
                </g>
                <circle cx="733.481" cy="145.044" r="40.01" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:80.019943237304688,&quot;m01&quot;:-74.99865722656250,&quot;m02&quot;:730.97058105468750,&quot;m10&quot;:104.79769897460938,&quot;m11&quot;:146.41603088378906,&quot;m12&quot;:22.68664550781250},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
            </g>
            <g filter="url(#tjwqygremf)">
                <circle cx="129.448" cy="-104.01" r="104.042" fill="#9786FF"/>
                <circle cx="129.448" cy="-104.01" r="104.042" fill="url(#8x9885hbfg)" fill-opacity=".8"/>
                <g clip-path="url(#qh3lmx72th)" data-figma-skip-parse="true">
                    <foreignObject x="-941.285" y="-941.285" width="1882.57" height="1882.57" transform="matrix(.10404 .13626 -.09751 .19037 129.448 -95.56)">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#fefefe 0deg,rgba(255,255,255,0) 66.6644deg,#fff 143.541deg,rgba(255,255,255,0) 199.792deg,#fff 272.918deg,rgba(255,255,255,0) 349.795deg,#fefefe 360deg);height:100%;width:100%;opacity:.5"/>
                    </foreignObject>
                </g>
                <circle cx="129.448" cy="-104.01" r="104.042" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:208.08430480957031,&quot;m01&quot;:-195.02693176269531,&quot;m02&quot;:122.91971588134766,&quot;m10&quot;:272.51654052734375,&quot;m11&quot;:380.74108886718750,&quot;m12&quot;:-422.18838500976562},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
            </g>
        </g>
        <path d="M801.1.5v103.526c0 8.562-6.942 15.504-15.504 15.504H17.404c-8.562 0-15.504-6.942-15.504-15.504V.5h799.2z" stroke="url(#sq0v4h4p0i)" stroke-opacity=".4"/>
        <path d="M801.1.5v103.526c0 8.562-6.942 15.504-15.504 15.504H17.404c-8.562 0-15.504-6.942-15.504-15.504V.5h799.2z" stroke="url(#t5p1x447rj)" stroke-opacity=".27"/>
    </g>
    <defs>
        <linearGradient id="451ztbu3vd" x1="733.654" y1="129.229" x2="787.967" y2="158.883" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7ECEF"/>
            <stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="8x9885hbfg" x1="129.455" y1="-145.256" x2="270.691" y2="-68.143" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7ECEF"/>
            <stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="t5p1x447rj" x1="-34.609" y1="340.25" x2="144.622" y2="8.175" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#0F0F14"/>
        </linearGradient>
        <clipPath id="ragmuywl9e">
            <circle cx="733.481" cy="145.044" r="40.01"/>
        </clipPath>
        <clipPath id="qh3lmx72th">
            <circle cx="129.448" cy="-104.01" r="104.042"/>
        </clipPath>
        <clipPath id="q6iwj40jlb">
            <path d="M1.4 0h800.2v104.026c0 8.839-7.165 16.004-16.004 16.004H17.404c-8.839 0-16.004-7.165-16.004-16.004V0z" fill="#fff"/>
        </clipPath>
        <filter id="binuk5u0ga" x="0" y="0" width="803" height="126.932" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="3" in="SourceAlpha" result="effect1_dropShadow_1338_4191"/>
            <feOffset dy="5.5"/>
            <feGaussianBlur stdDeviation="2.2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.91 0"/>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1338_4191"/>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_1338_4191" result="shape"/>
        </filter>
        <filter id="19ytkh891c" x="633.456" y="45.019" width="200.05" height="200.05" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_1338_4191"/>
        </filter>
        <filter id="tjwqygremf" x="-104.646" y="-338.105" width="468.19" height="468.19" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="65.01" result="effect1_foregroundBlur_1338_4191"/>
        </filter>
        <radialGradient id="sq0v4h4p0i" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-82.6874 28.8207 -22.59536 -64.82674 248.795 -2.024)">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#fff" stop-opacity="0"/>
        </radialGradient>
    </defs>
</svg>
