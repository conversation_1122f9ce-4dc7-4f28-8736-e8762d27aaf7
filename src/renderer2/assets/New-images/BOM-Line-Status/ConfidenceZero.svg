<svg width="104" height="60" viewBox="0 0 104 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Confidence 0%">
<g clip-path="url(#clip0_851_1320)">
<rect width="104" height="60" rx="5.05263" fill="#17171B"/>
<rect id="Rectangle 240657454" x="3.23828" y="7" width="96" height="33" fill="url(#paint0_linear_851_1320)"/>
<rect id="Rectangle 240657455" x="2" y="6.31641" width="97" height="33" fill="#1A1A1F"/>
<path id="Vector 53" fill-rule="evenodd" clip-rule="evenodd" d="M4.23828 56.9999C0.92457 56.9999 -1.76172 54.3136 -1.76172 50.9999V9C-1.76172 5.68629 0.924582 3 4.23829 3L96.2383 3.00007C99.552 3.00007 102.238 5.68637 102.238 9.00007V51C102.238 54.3137 99.552 57 96.2383 57L4.23828 56.9999ZM9.15974 35.1189C9.15974 34.8972 8.96848 34.7237 8.74776 34.7454L3.54754 35.2556C3.35537 35.2745 3.20886 35.436 3.20886 35.6291V38.1247C3.20886 38.332 3.3769 38.5 3.58419 38.5H8.78441C8.9917 38.5 9.15974 38.332 9.15974 38.1247V35.1189ZM11.6627 38.5C11.4554 38.5 11.2874 38.332 11.2874 38.1247V34.7534C11.2874 34.5603 11.4339 34.3987 11.6261 34.3798L16.8263 33.8696C17.047 33.848 17.2383 34.0214 17.2383 34.2432V38.1247C17.2383 38.332 17.0702 38.5 16.8629 38.5H11.6627ZM19.2422 38.125C19.24 38.3322 19.4061 38.5019 19.6133 38.5042L24.8716 38.5632C25.0805 38.5656 25.2511 38.3968 25.2511 38.1879V33.0919C25.2511 32.8643 25.0502 32.6891 24.8247 32.7201L19.6111 33.4365C19.4267 33.4618 19.2888 33.6184 19.2868 33.8044L19.2422 38.125ZM33.2674 38.1666C33.2674 38.375 33.0976 38.5435 32.8893 38.5419L27.6143 38.5028C27.4068 38.5013 27.2399 38.3315 27.2418 38.124L27.2932 32.6323C27.2947 32.4667 27.4047 32.3216 27.5638 32.2754L32.7873 30.7575C33.0275 30.6878 33.2674 30.8679 33.2674 31.118V38.1666ZM35.2534 38.0665C35.2518 38.2735 35.418 38.4427 35.625 38.4447L40.8593 38.4963C41.068 38.4983 41.2383 38.3297 41.2383 38.121V28.4909C41.2383 28.2407 40.9982 28.0606 40.7581 28.1305L35.586 29.6359C35.4268 29.6822 35.3168 29.8276 35.3156 29.9934L35.2534 38.0665ZM49.2383 38.1247C49.2383 38.332 49.0702 38.5 48.8629 38.5H43.6136C43.4063 38.5 43.2383 38.332 43.2383 38.1247V27.3453C43.2383 27.1958 43.327 27.0606 43.464 27.0011L48.7134 24.7199C48.9612 24.6122 49.2383 24.7938 49.2383 25.0641V38.1247ZM51.2399 38.123C51.239 38.331 51.4073 38.5 51.6152 38.5H56.8629C57.0702 38.5 57.2383 38.332 57.2383 38.1247V21.2686C57.2383 20.9971 56.9589 20.8154 56.7107 20.9255L51.5262 23.2256C51.3911 23.2855 51.3037 23.4192 51.3031 23.5671L51.2399 38.123ZM65.2383 38.1284C65.2383 38.3358 65.0701 38.5039 64.8627 38.5037L59.6151 38.5002C59.4072 38.5001 59.2391 38.331 59.24 38.1232L59.3258 19.7809C59.3265 19.6209 59.4286 19.4789 59.5801 19.4273L64.7419 17.6691C64.9854 17.5861 65.2383 17.7671 65.2383 18.0244V38.1284ZM67.2383 38.1284C67.2383 38.3358 67.4064 38.5039 67.6138 38.5038L72.8631 38.5011C73.0704 38.501 73.2383 38.333 73.2383 38.1258V15.361C73.2383 15.1107 72.9979 14.9306 72.7577 15.0008L67.5083 16.5347C67.3483 16.5815 67.2383 16.7282 67.2383 16.895V38.1284ZM81.2383 38.1065C81.2383 38.3149 81.0686 38.4834 80.8602 38.4818L75.6109 38.4437C75.4047 38.4422 75.2383 38.2746 75.2383 38.0684V14.2226C75.2383 14.0486 75.3578 13.8975 75.527 13.8574L80.7764 12.6128C81.0121 12.5569 81.2383 12.7357 81.2383 12.978V38.1065ZM83.2383 38.1285C83.2383 38.3358 83.4064 38.5039 83.6138 38.5038L88.8631 38.5013C89.0703 38.5012 89.2383 38.3332 89.2383 38.126V10.9364C89.2383 10.6987 89.0201 10.5209 88.7873 10.5688L83.538 11.6486C83.3635 11.6844 83.2383 11.838 83.2383 12.0162V38.1285ZM97.2383 38.1247C97.2383 38.332 97.0702 38.5 96.8629 38.5H91.6136C91.4063 38.5 91.2383 38.332 91.2383 38.1247V10.2548C91.2383 10.0833 91.3546 9.93355 91.5208 9.89112L96.7701 8.55125C97.0075 8.49067 97.2383 8.66997 97.2383 8.91492V38.1247Z" fill="#17171B"/>
<path id="Confidence" d="M10.1666 47H9.62114C9.56432 46.7045 9.45778 46.419 9.30153 46.1435C9.14528 45.8651 8.94358 45.6151 8.69642 45.3935C8.4521 45.169 8.16801 44.9915 7.84415 44.8608C7.52028 44.7301 7.16233 44.6648 6.77028 44.6648C6.19642 44.6648 5.66801 44.8182 5.18506 45.125C4.70494 45.429 4.32 45.8764 4.03023 46.4673C3.7433 47.0582 3.59983 47.7812 3.59983 48.6364C3.59983 49.4972 3.7433 50.223 4.03023 50.8139C4.32 51.4048 4.70494 51.8523 5.18506 52.1562C5.66801 52.4574 6.19642 52.608 6.77028 52.608C7.16233 52.608 7.52028 52.544 7.84415 52.4162C8.16801 52.2855 8.4521 52.1094 8.69642 51.8878C8.94358 51.6634 9.14528 51.4119 9.30153 51.1335C9.45778 50.8551 9.56432 50.5682 9.62114 50.2727H10.1666C10.1041 50.625 9.98335 50.9702 9.80437 51.3082C9.62824 51.6463 9.39528 51.9531 9.10551 52.2287C8.81858 52.5014 8.47909 52.7187 8.08705 52.8807C7.69784 53.0398 7.25892 53.1193 6.77028 53.1193C6.04301 53.1193 5.40239 52.9347 4.84841 52.5653C4.29443 52.1932 3.85977 51.6705 3.54443 50.9972C3.23193 50.3239 3.07568 49.5369 3.07568 48.6364C3.07568 47.7358 3.23193 46.9489 3.54443 46.2756C3.85977 45.6023 4.29443 45.081 4.84841 44.7116C5.40239 44.3395 6.04301 44.1534 6.77028 44.1534C7.25892 44.1534 7.69784 44.2344 8.08705 44.3963C8.47909 44.5554 8.81858 44.7713 9.10551 45.044C9.39528 45.3139 9.62824 45.6179 9.80437 45.956C9.98335 46.294 10.1041 46.642 10.1666 47ZM21.1959 48.6364C21.1959 49.5369 21.0397 50.3239 20.7272 50.9972C20.4147 51.6676 19.98 52.1889 19.4232 52.5611C18.8692 52.9332 18.23 53.1193 17.5056 53.1193C16.7783 53.1193 16.1363 52.9332 15.5795 52.5611C15.0255 52.1861 14.5922 51.6634 14.2797 50.9929C13.9672 50.3196 13.811 49.5341 13.811 48.6364C13.811 47.7358 13.9672 46.9503 14.2797 46.2798C14.5951 45.6094 15.0297 45.0881 15.5837 44.7159C16.1377 44.3409 16.7783 44.1534 17.5056 44.1534C18.23 44.1534 18.8692 44.3395 19.4232 44.7116C19.98 45.0838 20.4147 45.6065 20.7272 46.2798C21.0397 46.9503 21.1959 47.7358 21.1959 48.6364ZM20.6761 48.6364C20.6761 47.8267 20.5397 47.125 20.267 46.5312C19.9971 45.9375 19.6235 45.4787 19.1462 45.1548C18.6689 44.8281 18.1221 44.6648 17.5056 44.6648C16.8891 44.6648 16.3422 44.8267 15.865 45.1506C15.3877 45.4716 15.0127 45.9304 14.74 46.527C14.4701 47.1207 14.3351 47.8239 14.3351 48.6364C14.3351 49.4403 14.4687 50.1406 14.7357 50.7372C15.0056 51.331 15.3792 51.7912 15.8564 52.1179C16.3337 52.4446 16.8834 52.608 17.5056 52.608C18.1221 52.608 18.6689 52.446 19.1462 52.1222C19.6235 51.7955 19.9971 51.3352 20.267 50.7415C20.5397 50.1477 20.6761 49.446 20.6761 48.6364ZM31.9866 44.2727V53H31.4668L25.9014 45.2401H25.8503V53H25.3176V44.2727H25.8418L31.4071 52.0412H31.4582V44.2727H31.9866ZM36.5451 53V44.2727H41.4159V44.767H37.0778V48.3849H41.011V48.8793H37.0778V53H36.5451ZM46.149 44.2727V53H45.6164V44.2727H46.149ZM53.1354 53H50.715V44.2727H53.3229C54.1411 44.2727 54.8414 44.446 55.4238 44.7926C56.0062 45.1392 56.4522 45.6364 56.7618 46.2841C57.0743 46.929 57.2292 47.7031 57.2263 48.6065C57.2235 49.5298 57.0615 50.3196 56.7405 50.9759C56.4195 51.6293 55.955 52.1307 55.3471 52.4801C54.7391 52.8267 54.0019 53 53.1354 53ZM51.2476 52.5057H53.1141C53.8925 52.5057 54.5488 52.3509 55.0829 52.0412C55.6169 51.7287 56.0204 51.2812 56.2931 50.6989C56.5687 50.1165 56.705 49.419 56.7022 48.6065C56.7022 47.8054 56.5701 47.1193 56.3059 46.5483C56.0417 45.9744 55.6567 45.5341 55.151 45.2273C54.6482 44.9205 54.0317 44.767 53.3016 44.767H51.2476V52.5057ZM61.3448 53V44.2727H66.2028V44.767H61.8775V48.3849H65.9428V48.8793H61.8775V52.5057H66.305V53H61.3448ZM77.1202 44.2727V53H76.6004L71.035 45.2401H70.9839V53H70.4512V44.2727H70.9754L76.5407 52.0412H76.5918V44.2727H77.1202ZM88.3264 47H87.781C87.7242 46.7045 87.6176 46.419 87.4614 46.1435C87.3051 45.8651 87.1034 45.6151 86.8563 45.3935C86.6119 45.169 86.3279 44.9915 86.004 44.8608C85.6801 44.7301 85.3222 44.6648 84.9301 44.6648C84.3563 44.6648 83.8279 44.8182 83.3449 45.125C82.8648 45.429 82.4798 45.8764 82.1901 46.4673C81.9031 47.0582 81.7597 47.7812 81.7597 48.6364C81.7597 49.4972 81.9031 50.223 82.1901 50.8139C82.4798 51.4048 82.8648 51.8523 83.3449 52.1562C83.8279 52.4574 84.3563 52.608 84.9301 52.608C85.3222 52.608 85.6801 52.544 86.004 52.4162C86.3279 52.2855 86.6119 52.1094 86.8563 51.8878C87.1034 51.6634 87.3051 51.4119 87.4614 51.1335C87.6176 50.8551 87.7242 50.5682 87.781 50.2727H88.3264C88.2639 50.625 88.1432 50.9702 87.9642 51.3082C87.7881 51.6463 87.5551 51.9531 87.2654 52.2287C86.9784 52.5014 86.6389 52.7187 86.2469 52.8807C85.8577 53.0398 85.4188 53.1193 84.9301 53.1193C84.2029 53.1193 83.5622 52.9347 83.0083 52.5653C82.4543 52.1932 82.0196 51.6705 81.7043 50.9972C81.3918 50.3239 81.2355 49.5369 81.2355 48.6364C81.2355 47.7358 81.3918 46.9489 81.7043 46.2756C82.0196 45.6023 82.4543 45.081 83.0083 44.7116C83.5622 44.3395 84.2029 44.1534 84.9301 44.1534C85.4188 44.1534 85.8577 44.2344 86.2469 44.3963C86.6389 44.5554 86.9784 44.7713 87.2654 45.044C87.5551 45.3139 87.7881 45.6179 87.9642 45.956C88.1432 46.294 88.2639 46.642 88.3264 47ZM92.414 53V44.2727H97.272V44.767H92.9467V48.3849H97.012V48.8793H92.9467V52.5057H97.3742V53H92.414Z" fill="white" fill-opacity="0.5"/>
<path id="0" d="M10.6295 20.1397C9.87767 20.1397 9.23728 19.9351 8.70834 19.5259C8.1794 19.1134 7.77521 18.5163 7.49577 17.7345C7.21632 16.9494 7.0766 16.0013 7.0766 14.8902C7.0766 13.7858 7.21632 12.8427 7.49577 12.0609C7.77853 11.2758 8.18439 10.677 8.71333 10.2645C9.2456 9.84864 9.88432 9.64073 10.6295 9.64073C11.3747 9.64073 12.0117 9.84864 12.5407 10.2645C13.0729 10.677 13.4788 11.2758 13.7582 12.0609C14.041 12.8427 14.1824 13.7858 14.1824 14.8902C14.1824 16.0013 14.0427 16.9494 13.7632 17.7345C13.4838 18.5163 13.0796 19.1134 12.5507 19.5259C12.0217 19.9351 11.3813 20.1397 10.6295 20.1397ZM10.6295 19.0419C11.3747 19.0419 11.9535 18.6826 12.366 17.9641C12.7785 17.2455 12.9848 16.2209 12.9848 14.8902C12.9848 14.0053 12.89 13.2518 12.7004 12.6297C12.5141 12.0077 12.2446 11.5336 11.892 11.2076C11.5427 10.8816 11.1218 10.7186 10.6295 10.7186C9.89097 10.7186 9.31379 11.0828 8.89796 11.8114C8.48213 12.5366 8.27421 13.5629 8.27421 14.8902C8.27421 15.7751 8.36735 16.5269 8.55365 17.1457C8.73994 17.7645 9.00774 18.2352 9.35704 18.5579C9.70967 18.8806 10.1338 19.0419 10.6295 19.0419Z" fill="white"/>
<path id="%" d="M34.8463 20H34.4036L37.9777 15.5275H38.4107L34.8463 20ZM39.2505 18.9453C39.2505 19.1211 39.2158 19.2795 39.1463 19.4206C39.079 19.5595 38.9868 19.6788 38.8696 19.7787C38.7525 19.8785 38.6168 19.9544 38.4627 20.0065C38.3087 20.0608 38.147 20.0879 37.9777 20.0879C37.8085 20.0879 37.6468 20.0608 37.4927 20.0065C37.3408 19.9544 37.2063 19.8785 37.0891 19.7787C36.9741 19.6788 36.8818 19.5595 36.8124 19.4206C36.7451 19.2795 36.7115 19.1211 36.7115 18.9453C36.7115 18.7674 36.7451 18.6079 36.8124 18.4668C36.8818 18.3258 36.9741 18.2064 37.0891 18.1088C37.2063 18.0111 37.3408 17.9363 37.4927 17.8842C37.6468 17.8321 37.8085 17.8061 37.9777 17.8061C38.147 17.8061 38.3087 17.8321 38.4627 17.8842C38.6168 17.9363 38.7525 18.0111 38.8696 18.1088C38.9868 18.2064 39.079 18.3258 39.1463 18.4668C39.2158 18.6079 39.2505 18.7674 39.2505 18.9453ZM38.8664 18.9453C38.8664 18.8303 38.8468 18.7229 38.8078 18.6231C38.7687 18.5211 38.7112 18.4321 38.6353 18.3562C38.5593 18.2802 38.466 18.2205 38.3553 18.1771C38.2447 18.1337 38.1188 18.112 37.9777 18.112C37.8367 18.112 37.7108 18.1337 37.6001 18.1771C37.4916 18.2205 37.3994 18.2802 37.3235 18.3562C37.2497 18.4321 37.1932 18.5211 37.1542 18.6231C37.1173 18.7229 37.0989 18.8303 37.0989 18.9453C37.0989 19.0604 37.1173 19.1689 37.1542 19.2709C37.1932 19.3707 37.2497 19.4586 37.3235 19.5345C37.3994 19.6083 37.4916 19.668 37.6001 19.7135C37.7108 19.757 37.8367 19.7787 37.9777 19.7787C38.1188 19.7787 38.2447 19.757 38.3553 19.7135C38.466 19.668 38.5593 19.6083 38.6353 19.5345C38.7112 19.4586 38.7687 19.3707 38.8078 19.2709C38.8468 19.1689 38.8664 19.0604 38.8664 18.9453ZM36.1126 16.5854C36.1126 16.7612 36.0778 16.9196 36.0084 17.0606C35.9389 17.1995 35.8456 17.3189 35.7284 17.4187C35.6134 17.5185 35.4789 17.5956 35.3248 17.6498C35.1707 17.7019 35.0091 17.7279 34.8398 17.7279C34.6705 17.7279 34.5089 17.7019 34.3548 17.6498C34.2029 17.5956 34.0683 17.5185 33.9512 17.4187C33.8361 17.3189 33.7439 17.1995 33.6745 17.0606C33.605 16.9196 33.5703 16.7612 33.5703 16.5854C33.5703 16.4074 33.605 16.2479 33.6745 16.1069C33.7439 15.9658 33.8361 15.8465 33.9512 15.7488C34.0683 15.6512 34.2029 15.5763 34.3548 15.5242C34.5089 15.4721 34.6705 15.4461 34.8398 15.4461C35.0091 15.4461 35.1707 15.4721 35.3248 15.5242C35.4789 15.5763 35.6134 15.6512 35.7284 15.7488C35.8456 15.8465 35.9389 15.9658 36.0084 16.1069C36.0778 16.2479 36.1126 16.4074 36.1126 16.5854ZM35.7252 16.5854C35.7252 16.4704 35.7057 16.3629 35.6666 16.2631C35.6275 16.1611 35.57 16.0722 35.4941 15.9962C35.4203 15.9203 35.3281 15.8606 35.2174 15.8172C35.1067 15.7738 34.9809 15.7521 34.8398 15.7521C34.6966 15.7521 34.5696 15.7738 34.459 15.8172C34.3504 15.8606 34.2582 15.9203 34.1823 15.9962C34.1085 16.0722 34.0521 16.1611 34.013 16.2631C33.9761 16.3629 33.9577 16.4704 33.9577 16.5854C33.9577 16.7004 33.9761 16.8089 34.013 16.9109C34.0521 17.0107 34.1085 17.0986 34.1823 17.1746C34.2582 17.2483 34.3504 17.308 34.459 17.3536C34.5696 17.397 34.6966 17.4187 34.8398 17.4187C34.9809 17.4187 35.1067 17.397 35.2174 17.3536C35.3281 17.308 35.4203 17.2483 35.4941 17.1746C35.57 17.0986 35.6275 17.0107 35.6666 16.9109C35.7057 16.8089 35.7252 16.7004 35.7252 16.5854Z" fill="white"/>
<g id="Ellipse 6595" opacity="0.21" filter="url(#filter0_f_851_1320)">
<ellipse cx="61.5" cy="78.5" rx="40.5" ry="18.5" fill="#9786FF"/>
<ellipse cx="61.5" cy="78.5" rx="40.5" ry="18.5" fill="url(#paint1_linear_851_1320)" fill-opacity="0.8"/>
<g clip-path="url(#paint2_angular_851_1320_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0405 0.0242284 -0.0379586 0.0338503 61.5 80.0025)"><foreignObject x="-944.528" y="-944.528" width="1889.06" height="1889.06"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="61.5" cy="78.5" rx="40.5" ry="18.5" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:80.999992370605469,&#34;m01&#34;:-75.917205810546875,&#34;m02&#34;:58.958610534667969,&#34;m10&#34;:48.456851959228516,&#34;m11&#34;:67.700531005859375,&#34;m12&#34;:21.923845291137695},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g id="Ellipse 6596" filter="url(#filter1_f_851_1320)">
<ellipse cx="21.0719" cy="-18.5075" rx="30.025" ry="9.94175" fill="#9786FF"/>
<ellipse cx="21.0719" cy="-18.5075" rx="30.025" ry="9.94175" fill="url(#paint3_linear_851_1320)" fill-opacity="0.8"/>
<g clip-path="url(#paint4_angular_851_1320_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.030025 0.0130202 -0.0281409 0.0181909 21.0719 -17.7)"><foreignObject x="-953.546" y="-953.546" width="1907.09" height="1907.09"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="21.0719" cy="-18.5075" rx="30.025" ry="9.94175" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:60.049961090087891,&#34;m01&#34;:-56.281803131103516,&#34;m02&#34;:19.187780380249023,&#34;m10&#34;:26.040309906005859,&#34;m11&#34;:36.381706237792969,&#34;m12&#34;:-48.911029815673828},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.232169" y="0.232169" width="103.536" height="59.5357" rx="4.82046" stroke="url(#paint5_radial_851_1320)" stroke-opacity="0.4" stroke-width="0.464338"/>
<rect x="0.232169" y="0.232169" width="103.536" height="59.5357" rx="4.82046" stroke="url(#paint6_radial_851_1320)" stroke-opacity="0.2" stroke-width="0.464338"/>
</g>
<defs>
<filter id="filter0_f_851_1320" x="-6.8603" y="32.1397" width="136.721" height="92.7206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="13.9302" result="effect1_foregroundBlur_851_1320"/>
</filter>
<clipPath id="paint2_angular_851_1320_clip_path"><ellipse cx="61.5" cy="78.5" rx="40.5" ry="18.5"/></clipPath><filter id="filter1_f_851_1320" x="-55.387" y="-74.8831" width="152.918" height="112.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="23.2169" result="effect1_foregroundBlur_851_1320"/>
</filter>
<clipPath id="paint4_angular_851_1320_clip_path"><ellipse cx="21.0719" cy="-18.5075" rx="30.025" ry="9.94175"/></clipPath><linearGradient id="paint0_linear_851_1320" x1="3.23828" y1="23.5" x2="99.2383" y2="23.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF0000"/>
<stop offset="0.25" stop-color="#FF8E1C"/>
<stop offset="0.5" stop-color="#FFE019"/>
<stop offset="0.77" stop-color="#C1F035"/>
<stop offset="1" stop-color="#00FF48"/>
</linearGradient>
<linearGradient id="paint1_linear_851_1320" x1="61.5" y1="71.1751" x2="90.8857" y2="106.299" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_851_1320" x1="21.0719" y1="-22.4438" x2="35.2988" y2="1.01517" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint5_radial_851_1320" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(32.1533 -1.01163) rotate(126.721) scale(17.9736 21.7294)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint6_radial_851_1320" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(74.5 70.5) rotate(-107.757) scale(27.6825 23.1228)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#17171B"/>
</radialGradient>
<clipPath id="clip0_851_1320">
<rect width="104" height="60" rx="5.05263" fill="white"/>
</clipPath>
</defs>
</svg>
