<svg width="104" height="26" viewBox="0 0 104 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_984_1459)">
<rect width="104" height="26" rx="4" fill="#17171B"/>
<g opacity="0.44" filter="url(#filter0_f_984_1459)">
<ellipse cx="85.5" cy="41" rx="14.5" ry="13" fill="#9786FF"/>
<ellipse cx="85.5" cy="41" rx="14.5" ry="13" fill="url(#paint0_linear_984_1459)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_984_1459_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0145 0.0170254 -0.0135901 0.0237867 85.5 42.0558)"><foreignObject x="-953.813" y="-953.813" width="1907.63" height="1907.63"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="85.5" cy="41" rx="14.5" ry="13" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:28.999996185302734,&#34;m01&#34;:-27.180234909057617,&#34;m02&#34;:84.590118408203125,&#34;m10&#34;:34.050762176513672,&#34;m11&#34;:47.573345184326172,&#34;m12&#34;:1.2437826395034790},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.87" filter="url(#filter1_f_984_1459)">
<ellipse cx="29" cy="-11.207" rx="7" ry="13" fill="#9786FF"/>
<ellipse cx="29" cy="-11.207" rx="7" ry="13" fill="url(#paint2_linear_984_1459)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_984_1459_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.007 0.0170254 -0.00656075 0.0237867 29 -10.1512)"><foreignObject x="-970.07" y="-970.07" width="1940.14" height="1940.14"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="29" cy="-11.207" rx="7" ry="13" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:13.999998092651367,&#34;m01&#34;:-13.121492385864258,&#34;m02&#34;:28.560747146606445,&#34;m10&#34;:34.050762176513672,&#34;m11&#34;:47.573345184326172,&#34;m12&#34;:-50.963249206542969},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.183801" y="0.183801" width="103.632" height="25.6324" rx="3.8162" stroke="url(#paint4_radial_984_1459)" stroke-opacity="0.53" stroke-width="0.367601"/>
<rect x="0.183801" y="0.183801" width="103.632" height="25.6324" rx="3.8162" stroke="url(#paint5_radial_984_1459)" stroke-opacity="0.16" stroke-width="0.367601"/>
<defs>
<filter id="filter0_f_984_1459" x="48.9439" y="5.94393" width="73.1121" height="70.1121" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.028" result="effect1_foregroundBlur_984_1459"/>
</filter>
<clipPath id="paint1_angular_984_1459_clip_path"><ellipse cx="85.5" cy="41" rx="14.5" ry="13"/></clipPath><filter id="filter1_f_984_1459" x="-14.7601" y="-60.9672" width="87.5202" height="99.5202" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="18.3801" result="effect1_foregroundBlur_984_1459"/>
</filter>
<clipPath id="paint3_angular_984_1459_clip_path"><ellipse cx="29" cy="-11.207" rx="7" ry="13"/></clipPath><linearGradient id="paint0_linear_984_1459" x1="85.5" y1="35.8528" x2="104.139" y2="47.2035" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_984_1459" x1="29" y1="-16.3542" x2="40.3538" y2="-13.0163" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_984_1459" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(32.1533 -0.438372) rotate(149.847) scale(12.4284 13.6173)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1B1F"/>
</radialGradient>
<radialGradient id="paint5_radial_984_1459" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(78.5 26) rotate(-122.852) scale(20.2774 11.7965)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1A1E"/>
</radialGradient>
<clipPath id="clip0_984_1459">
<rect width="104" height="26" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
