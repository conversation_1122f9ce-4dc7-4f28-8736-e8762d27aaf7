<svg width="126" height="189" viewBox="0 0 126 189" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_851_1403)">
<g clip-path="url(#clip0_851_1403)">
<rect width="126" height="189" rx="8" fill="#131314"/>
<g filter="url(#filter1_f_851_1403)">
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="#9786FF"/>
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="url(#paint0_linear_851_1403)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_851_1403_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0314019 0.0371387 -0.0294314 0.0518875 29.8316 -32.3273)"><foreignObject x="-940.952" y="-940.952" width="1881.9" height="1881.9"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:62.803733825683594,&#34;m01&#34;:-58.862773895263672,&#34;m02&#34;:27.861078262329102,&#34;m10&#34;:74.277313232421875,&#34;m11&#34;:103.77507019042969,&#34;m12&#34;:-121.35349273681641},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.8" filter="url(#filter2_f_851_1403)">
<circle cx="97.704" cy="211.704" r="14.7041" fill="#9786FF"/>
<circle cx="97.704" cy="211.704" r="14.7041" fill="url(#paint2_linear_851_1403)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_851_1403_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.014704 0.0192571 -0.0137814 0.0269046 97.7041 212.898)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="97.704" cy="211.704" r="14.7041" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:29.408096313476562,&#34;m01&#34;:-27.562726974487305,&#34;m02&#34;:96.781364440917969,&#34;m10&#34;:38.514160156250,&#34;m11&#34;:53.809295654296875,&#34;m12&#34;:166.73655700683594},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<path d="M36.6777 11.2168C36.5254 11.1426 36.3242 11.0586 36.0742 10.9648C35.8242 10.8711 35.541 10.7832 35.2246 10.7012C34.9082 10.6152 34.5645 10.5449 34.1934 10.4902C33.8223 10.4316 33.4414 10.4023 33.0508 10.4023C32.7383 10.4023 32.4707 10.4121 32.248 10.4316C32.0293 10.4512 31.8438 10.4766 31.6914 10.5078C31.543 10.5391 31.4238 10.5762 31.334 10.6191C31.248 10.6582 31.1816 10.6992 31.1348 10.7422C31.0879 10.7852 31.0566 10.8281 31.041 10.8711C31.0293 10.9141 31.0234 10.9531 31.0234 10.9883C31.0234 11.1055 31.082 11.209 31.1992 11.2988C31.3164 11.3848 31.4766 11.4629 31.6797 11.5332C31.8828 11.5996 32.1191 11.6641 32.3887 11.7266C32.6582 11.7852 32.9434 11.8477 33.2441 11.9141C33.5488 11.9766 33.8613 12.0469 34.1816 12.125C34.5059 12.1992 34.8184 12.2871 35.1191 12.3887C35.4238 12.4902 35.7109 12.6094 35.9805 12.7461C36.25 12.8789 36.4863 13.0352 36.6895 13.2148C36.8926 13.3945 37.0527 13.6016 37.1699 13.8359C37.2871 14.0703 37.3457 14.3379 37.3457 14.6387C37.3457 15.0254 37.2773 15.3574 37.1406 15.6348C37.0039 15.9121 36.8203 16.1465 36.5898 16.3379C36.3633 16.5293 36.0996 16.6836 35.7988 16.8008C35.498 16.9141 35.1816 17.002 34.8496 17.0645C34.5215 17.127 34.1875 17.168 33.8477 17.1875C33.5117 17.207 33.1914 17.2168 32.8867 17.2168C32.0898 17.2168 31.3516 17.1504 30.6719 17.0176C29.9961 16.8887 29.3984 16.7266 28.8789 16.5312V14.6094C29.4141 14.9062 30.0234 15.1387 30.707 15.3066C31.3945 15.4746 32.1289 15.5586 32.9102 15.5586C33.3711 15.5586 33.7461 15.5352 34.0352 15.4883C34.3281 15.4375 34.5566 15.375 34.7207 15.3008C34.8848 15.2227 34.9961 15.1387 35.0547 15.0488C35.1133 14.959 35.1426 14.873 35.1426 14.791C35.1426 14.6621 35.084 14.5508 34.9668 14.457C34.8496 14.3594 34.6895 14.2754 34.4863 14.2051C34.2832 14.1309 34.0469 14.0645 33.7773 14.0059C33.5078 13.9434 33.2207 13.8828 32.916 13.8242C32.6152 13.7617 32.3047 13.6953 31.9844 13.625C31.6641 13.5547 31.3516 13.4727 31.0469 13.3789C30.7461 13.2812 30.4609 13.1699 30.1914 13.0449C29.9219 12.916 29.6855 12.7656 29.4824 12.5938C29.2793 12.418 29.1191 12.2168 29.002 11.9902C28.8848 11.7598 28.8262 11.4941 28.8262 11.1934C28.8262 10.8379 28.8887 10.5312 29.0137 10.2734C29.1387 10.0156 29.3066 9.79688 29.5176 9.61719C29.7324 9.43359 29.9785 9.28516 30.2559 9.17188C30.5371 9.05859 30.832 8.9707 31.1406 8.9082C31.4492 8.8457 31.7617 8.80273 32.0781 8.7793C32.3945 8.75586 32.6953 8.74414 32.9805 8.74414C33.293 8.74414 33.6133 8.75977 33.9414 8.79102C34.2734 8.82227 34.5996 8.86719 34.9199 8.92578C35.2402 8.98047 35.5488 9.04492 35.8457 9.11914C36.1465 9.19336 36.4238 9.27148 36.6777 9.35352V11.2168ZM41.5938 12.0195L46.0469 8.94922H48.959L43.5391 12.8105L49.3223 17H46.1523L41.5938 13.6543V17H39.4082V8.94922H41.5938V12.0195ZM50.5879 17V8.94922H52.7617V17H50.5879ZM64.334 11.6973C64.334 12.0996 64.2754 12.4707 64.1582 12.8105C64.041 13.1465 63.8516 13.4375 63.5898 13.6836C63.332 13.9258 62.9961 14.1152 62.582 14.252C62.168 14.3887 61.6641 14.457 61.0703 14.457H57.4609V17H55.2871V8.94922H61.0703C61.6641 8.94922 62.168 9.01758 62.582 9.1543C62.9961 9.28711 63.332 9.47656 63.5898 9.72266C63.8516 9.96484 64.041 10.2539 64.1582 10.5898C64.2754 10.9258 64.334 11.2949 64.334 11.6973ZM62.1367 11.7324C62.1367 11.5449 62.1094 11.3848 62.0547 11.252C62.0039 11.1152 61.918 11.0039 61.7969 10.918C61.6758 10.832 61.5176 10.7695 61.3223 10.7305C61.127 10.6875 60.8867 10.666 60.6016 10.666H57.4609V12.7402H60.6016C60.8867 12.7402 61.127 12.7246 61.3223 12.6934C61.5176 12.6582 61.6758 12.6016 61.7969 12.5234C61.918 12.4414 62.0039 12.3359 62.0547 12.207C62.1094 12.0781 62.1367 11.9199 62.1367 11.7324ZM75.1855 11.6973C75.1855 12.0996 75.127 12.4707 75.0098 12.8105C74.8926 13.1465 74.7031 13.4375 74.4414 13.6836C74.1836 13.9258 73.8477 14.1152 73.4336 14.252C73.0195 14.3887 72.5156 14.457 71.9219 14.457H68.3125V17H66.1387V8.94922H71.9219C72.5156 8.94922 73.0195 9.01758 73.4336 9.1543C73.8477 9.28711 74.1836 9.47656 74.4414 9.72266C74.7031 9.96484 74.8926 10.2539 75.0098 10.5898C75.127 10.9258 75.1855 11.2949 75.1855 11.6973ZM72.9883 11.7324C72.9883 11.5449 72.9609 11.3848 72.9062 11.252C72.8555 11.1152 72.7695 11.0039 72.6484 10.918C72.5273 10.832 72.3691 10.7695 72.1738 10.7305C71.9785 10.6875 71.7383 10.666 71.4531 10.666H68.3125V12.7402H71.4531C71.7383 12.7402 71.9785 12.7246 72.1738 12.6934C72.3691 12.6582 72.5273 12.6016 72.6484 12.5234C72.7695 12.4414 72.8555 12.3359 72.9062 12.207C72.9609 12.0781 72.9883 11.9199 72.9883 11.7324ZM76.9902 17V8.94922H85.1406V10.666H79.2227V11.9961H84.8359V13.7129H79.2227V15.2832H85.2227V17H76.9902ZM97.1875 12.9688C97.1875 13.4844 97.1309 13.9434 97.0176 14.3457C96.9043 14.7441 96.7441 15.0918 96.5371 15.3887C96.334 15.6855 96.0879 15.9375 95.7988 16.1445C95.5137 16.3516 95.1953 16.5176 94.8438 16.6426C94.4922 16.7676 94.1133 16.8594 93.707 16.918C93.3008 16.9727 92.877 17 92.4355 17H87.3262V8.94922H92.4238C92.8652 8.94922 93.2891 8.97852 93.6953 9.03711C94.1016 9.0918 94.4805 9.18164 94.832 9.30664C95.1875 9.43164 95.5098 9.59766 95.7988 9.80469C96.0879 10.0078 96.334 10.2598 96.5371 10.5605C96.7441 10.8574 96.9043 11.2051 97.0176 11.6035C97.1309 12.002 97.1875 12.457 97.1875 12.9688ZM94.9961 12.9688C94.9961 12.582 94.9453 12.2461 94.8438 11.9609C94.7461 11.6758 94.5859 11.4414 94.3633 11.2578C94.1445 11.0703 93.8574 10.9316 93.502 10.8418C93.1465 10.748 92.7129 10.7012 92.2012 10.7012H89.5V15.248H92.2012C92.7129 15.248 93.1465 15.2031 93.502 15.1133C93.8574 15.0195 94.1445 14.8789 94.3633 14.6914C94.5859 14.5 94.7461 14.2617 94.8438 13.9766C94.9453 13.6914 94.9961 13.3555 94.9961 12.9688Z" fill="url(#paint4_radial_851_1403)"/>
</g>
<rect x="0.183801" y="0.183801" width="125.632" height="188.632" rx="7.8162" stroke="url(#paint5_linear_851_1403)" stroke-opacity="0.4" stroke-width="0.367601"/>
</g>
<defs>
<filter id="filter0_ii_851_1403" x="-2" y="-1" width="132" height="194" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_851_1403"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_851_1403" result="effect2_innerShadow_851_1403"/>
</filter>
<filter id="filter1_f_851_1403" x="-38.3304" y="-99.7484" width="136.325" height="130.235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="18.3801" result="effect1_foregroundBlur_851_1403"/>
</filter>
<clipPath id="paint1_angular_851_1403_clip_path"><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578"/></clipPath><filter id="filter2_f_851_1403" x="60.9439" y="174.944" width="73.5184" height="73.5204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.028" result="effect1_foregroundBlur_851_1403"/>
</filter>
<clipPath id="paint3_angular_851_1403_clip_path"><circle cx="97.704" cy="211.704" r="14.7041"/></clipPath><linearGradient id="paint0_linear_851_1403" x1="29.8316" y1="-45.8584" x2="70.3543" y2="-21.3588" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_851_1403" x1="97.704" y1="205.882" x2="117.665" y2="216.78" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_851_1403" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(30.3596 4.5) rotate(22.5283) scale(180.091 19.4295)">
<stop stop-color="#459FFF"/>
<stop offset="1" stop-color="#3F4046"/>
</radialGradient>
<linearGradient id="paint5_linear_851_1403" x1="140.415" y1="99.8338" x2="20.3616" y2="25.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1C21"/>
</linearGradient>
<clipPath id="clip0_851_1403">
<rect width="126" height="189" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
