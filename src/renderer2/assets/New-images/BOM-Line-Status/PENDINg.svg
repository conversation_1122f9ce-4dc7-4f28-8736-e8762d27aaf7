<svg width="126" height="189" viewBox="0 0 126 189" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_851_1372)">
<g clip-path="url(#clip0_851_1372)">
<rect width="126" height="189" rx="8" fill="#131314"/>
<g filter="url(#filter1_f_851_1372)">
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="#9786FF"/>
<ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" fill="url(#paint0_linear_851_1372)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_851_1372_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0314019 0.0371387 -0.0294314 0.0518875 29.8316 -32.3273)"><foreignObject x="-940.952" y="-940.952" width="1881.9" height="1881.9"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:62.803733825683594,&#34;m01&#34;:-58.862773895263672,&#34;m02&#34;:27.861078262329102,&#34;m10&#34;:74.277313232421875,&#34;m11&#34;:103.77507019042969,&#34;m12&#34;:-121.35349273681641},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.8" filter="url(#filter2_f_851_1372)">
<circle cx="97.704" cy="211.704" r="14.7041" fill="#9786FF"/>
<circle cx="97.704" cy="211.704" r="14.7041" fill="url(#paint2_linear_851_1372)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_851_1372_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.014704 0.0192571 -0.0137814 0.0269046 97.7041 212.898)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="97.704" cy="211.704" r="14.7041" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:29.408096313476562,&#34;m01&#34;:-27.562726974487305,&#34;m02&#34;:96.781364440917969,&#34;m10&#34;:38.514160156250,&#34;m11&#34;:53.809295654296875,&#34;m12&#34;:166.73655700683594},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<path d="M35.3066 11.6973C35.3066 12.0996 35.248 12.4707 35.1309 12.8105C35.0137 13.1465 34.8242 13.4375 34.5625 13.6836C34.3047 13.9258 33.9688 14.1152 33.5547 14.252C33.1406 14.3887 32.6367 14.457 32.043 14.457H28.4336V17H26.2598V8.94922H32.043C32.6367 8.94922 33.1406 9.01758 33.5547 9.1543C33.9688 9.28711 34.3047 9.47656 34.5625 9.72266C34.8242 9.96484 35.0137 10.2539 35.1309 10.5898C35.248 10.9258 35.3066 11.2949 35.3066 11.6973ZM33.1094 11.7324C33.1094 11.5449 33.082 11.3848 33.0273 11.252C32.9766 11.1152 32.8906 11.0039 32.7695 10.918C32.6484 10.832 32.4902 10.7695 32.2949 10.7305C32.0996 10.6875 31.8594 10.666 31.5742 10.666H28.4336V12.7402H31.5742C31.8594 12.7402 32.0996 12.7246 32.2949 12.6934C32.4902 12.6582 32.6484 12.6016 32.7695 12.5234C32.8906 12.4414 32.9766 12.3359 33.0273 12.207C33.082 12.0781 33.1094 11.9199 33.1094 11.7324ZM37.1113 17V8.94922H45.2617V10.666H39.3438V11.9961H44.957V13.7129H39.3438V15.2832H45.3438V17H37.1113ZM55.0527 17L49.6211 11.4688V17H47.4473V8.94922H49.8086L55.2461 14.5039V8.94922H57.4082V17H55.0527ZM69.7891 12.9688C69.7891 13.4844 69.7324 13.9434 69.6191 14.3457C69.5059 14.7441 69.3457 15.0918 69.1387 15.3887C68.9355 15.6855 68.6895 15.9375 68.4004 16.1445C68.1152 16.3516 67.7969 16.5176 67.4453 16.6426C67.0938 16.7676 66.7148 16.8594 66.3086 16.918C65.9023 16.9727 65.4785 17 65.0371 17H59.9277V8.94922H65.0254C65.4668 8.94922 65.8906 8.97852 66.2969 9.03711C66.7031 9.0918 67.082 9.18164 67.4336 9.30664C67.7891 9.43164 68.1113 9.59766 68.4004 9.80469C68.6895 10.0078 68.9355 10.2598 69.1387 10.5605C69.3457 10.8574 69.5059 11.2051 69.6191 11.6035C69.7324 12.002 69.7891 12.457 69.7891 12.9688ZM67.5977 12.9688C67.5977 12.582 67.5469 12.2461 67.4453 11.9609C67.3477 11.6758 67.1875 11.4414 66.9648 11.2578C66.7461 11.0703 66.459 10.9316 66.1035 10.8418C65.748 10.748 65.3145 10.7012 64.8027 10.7012H62.1016V15.248H64.8027C65.3145 15.248 65.748 15.2031 66.1035 15.1133C66.459 15.0195 66.7461 14.8789 66.9648 14.6914C67.1875 14.5 67.3477 14.2617 67.4453 13.9766C67.5469 13.6914 67.5977 13.3555 67.5977 12.9688ZM71.7754 17V8.94922H73.9492V17H71.7754ZM84.0801 17L78.6484 11.4688V17H76.4746V8.94922H78.8359L84.2734 14.5039V8.94922H86.4355V17H84.0801ZM94.1406 8.74414C94.9297 8.74414 95.6621 8.8125 96.3379 8.94922C97.0176 9.08203 97.6406 9.25391 98.207 9.46484V11.4336C97.9844 11.3086 97.7324 11.1875 97.4512 11.0703C97.1738 10.9531 96.8711 10.8496 96.543 10.7598C96.2148 10.6699 95.8633 10.5977 95.4883 10.543C95.1133 10.4883 94.7188 10.4609 94.3047 10.4609C93.7891 10.4609 93.3398 10.498 92.957 10.5723C92.5742 10.6465 92.2461 10.7461 91.9727 10.8711C91.6992 10.9961 91.4746 11.1426 91.2988 11.3105C91.123 11.4746 90.9844 11.6504 90.8828 11.8379C90.7812 12.0254 90.7109 12.2168 90.6719 12.4121C90.6328 12.6074 90.6133 12.7969 90.6133 12.9805C90.6133 13.1406 90.6289 13.3145 90.6602 13.502C90.6953 13.6895 90.7578 13.877 90.8477 14.0645C90.9414 14.252 91.0703 14.4316 91.2344 14.6035C91.3984 14.7754 91.6113 14.9277 91.873 15.0605C92.1387 15.1934 92.457 15.3008 92.8281 15.3828C93.1992 15.4609 93.6406 15.5 94.1523 15.5C94.4102 15.5 94.6348 15.4922 94.8262 15.4766C95.0176 15.4609 95.1914 15.4395 95.3477 15.4121C95.5078 15.3848 95.6562 15.3516 95.793 15.3125C95.9336 15.2734 96.0781 15.2324 96.2266 15.1895V14.2168H93.7891V12.4531H98.4004V16.5664C98.1113 16.6641 97.7871 16.7539 97.4277 16.8359C97.0723 16.9141 96.7031 16.9805 96.3203 17.0352C95.9414 17.0938 95.5605 17.1387 95.1777 17.1699C94.7949 17.2012 94.4336 17.2168 94.0938 17.2168C93.5664 17.2168 93.0605 17.1816 92.5762 17.1113C92.0918 17.041 91.6387 16.9336 91.2168 16.7891C90.7988 16.6406 90.416 16.4531 90.0684 16.2266C89.7246 16 89.4297 15.7305 89.1836 15.418C88.9414 15.1055 88.752 14.748 88.6152 14.3457C88.4824 13.9395 88.416 13.4844 88.416 12.9805C88.416 12.4805 88.4863 12.0273 88.627 11.6211C88.7676 11.2148 88.9629 10.8555 89.2129 10.543C89.4668 10.2305 89.7695 9.96094 90.1211 9.73438C90.4766 9.50781 90.8652 9.32227 91.2871 9.17773C91.7129 9.0293 92.166 8.91992 92.6465 8.84961C93.1309 8.7793 93.6289 8.74414 94.1406 8.74414Z" fill="url(#paint4_radial_851_1372)"/>
</g>
<rect x="0.183801" y="0.183801" width="125.632" height="188.632" rx="7.8162" stroke="url(#paint5_linear_851_1372)" stroke-opacity="0.4" stroke-width="0.367601"/>
</g>
<defs>
<filter id="filter0_ii_851_1372" x="-2" y="-1" width="132" height="194" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_851_1372"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_851_1372" result="effect2_innerShadow_851_1372"/>
</filter>
<filter id="filter1_f_851_1372" x="-38.3304" y="-99.7484" width="136.325" height="130.235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="18.3801" result="effect1_foregroundBlur_851_1372"/>
</filter>
<clipPath id="paint1_angular_851_1372_clip_path"><ellipse cx="29.8316" cy="-34.6305" rx="31.4019" ry="28.3578"/></clipPath><filter id="filter2_f_851_1372" x="60.9439" y="174.944" width="73.5184" height="73.5204" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.028" result="effect1_foregroundBlur_851_1372"/>
</filter>
<clipPath id="paint3_angular_851_1372_clip_path"><circle cx="97.704" cy="211.704" r="14.7041"/></clipPath><linearGradient id="paint0_linear_851_1372" x1="29.8316" y1="-45.8584" x2="70.3543" y2="-21.3588" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_851_1372" x1="97.704" y1="205.882" x2="117.665" y2="216.78" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_851_1372" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(27.5281 4.5) rotate(21.1634) scale(191.121 19.616)">
<stop stop-color="#FF9E20"/>
<stop offset="1" stop-color="#3F4046"/>
</radialGradient>
<linearGradient id="paint5_linear_851_1372" x1="140.415" y1="99.8338" x2="20.3616" y2="25.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1C21"/>
</linearGradient>
<clipPath id="clip0_851_1372">
<rect width="126" height="189" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
