<svg width="704" height="70" viewBox="0 0 704 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1174_1632)">
<rect width="704" height="70" rx="10.7969" fill="#17171B"/>
<g opacity="0.44" filter="url(#filter0_f_1174_1632)">
<ellipse cx="620.5" cy="105" rx="83.5" ry="35" fill="#9786FF"/>
<ellipse cx="620.5" cy="105" rx="83.5" ry="35" fill="url(#paint0_linear_1174_1632)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_1174_1632_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0835 0.0458376 -0.0782603 0.064041 620.5 107.843)"><foreignObject x="-945.775" y="-945.775" width="1891.55" height="1891.55"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="620.5" cy="105" rx="83.5" ry="35" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:166.99998474121094,&#34;m01&#34;:-156.52066040039062,&#34;m02&#34;:615.26031494140625,&#34;m10&#34;:91.675125122070312,&#34;m11&#34;:128.08209228515625,&#34;m12&#34;:-2.0359697341918945},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.87" filter="url(#filter1_f_1174_1632)">
<circle cx="97" cy="-33" r="35" fill="#9786FF"/>
<circle cx="97" cy="-33" r="35" fill="url(#paint2_linear_1174_1632)" fill-opacity="0.8"/>
<g clip-path="url(#paint3_angular_1174_1632_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.035 0.0458376 -0.0328037 0.064041 97 -30.1574)"><foreignObject x="-955.63" y="-955.63" width="1911.26" height="1911.26"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="97" cy="-33" r="35" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:69.999992370605469,&#34;m01&#34;:-65.607460021972656,&#34;m02&#34;:94.803733825683594,&#34;m10&#34;:91.675125122070312,&#34;m11&#34;:128.08209228515625,&#34;m12&#34;:-140.03596496582031},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.49612" y="0.49612" width="703.008" height="69.0078" rx="10.3008" stroke="url(#paint4_radial_1174_1632)" stroke-opacity="0.53" stroke-width="0.992241"/>
<rect x="0.49612" y="0.49612" width="703.008" height="69.0078" rx="10.3008" stroke="url(#paint5_radial_1174_1632)" stroke-opacity="0.26" stroke-width="0.992241"/>
<defs>
<filter id="filter0_f_1174_1632" x="477.466" y="10.4656" width="286.069" height="189.069" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="29.7672" result="effect1_foregroundBlur_1174_1632"/>
</filter>
<clipPath id="paint1_angular_1174_1632_clip_path"><ellipse cx="620.5" cy="105" rx="83.5" ry="35"/></clipPath><filter id="filter1_f_1174_1632" x="-37.2241" y="-167.224" width="268.448" height="268.448" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="49.612" result="effect1_foregroundBlur_1174_1632"/>
</filter>
<clipPath id="paint3_angular_1174_1632_clip_path"><circle cx="97" cy="-33" r="35"/></clipPath><linearGradient id="paint0_linear_1174_1632" x1="620.5" y1="91.1421" x2="675.064" y2="162.215" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1174_1632" x1="97" y1="-46.8579" x2="144.512" y2="-20.917" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint4_radial_1174_1632" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(115.837 -26.25) rotate(106.044) scale(82.8521 214.505)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1B1B1F"/>
</radialGradient>
<radialGradient id="paint5_radial_1174_1632" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(611 101) rotate(-39.4174) scale(94.4934 108.25)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1A1E"/>
</radialGradient>
<clipPath id="clip0_1174_1632">
<rect width="704" height="70" rx="10.7969" fill="white"/>
</clipPath>
</defs>
</svg>
