<svg width="282" height="190" viewBox="0 0 282 190" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_1190_3583)">
<rect width="282" height="190" rx="10.2895" fill="#D9D9D9" fill-opacity="0.04"/>
</g>
<rect x="0.5" y="0.5" width="281" height="189" rx="9.78951" stroke="url(#paint0_linear_1190_3583)" stroke-opacity="0.69"/>
<defs>
<filter id="filter0_i_1190_3583" x="0" y="0" width="285" height="193" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="2.0579"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1190_3583"/>
</filter>
<linearGradient id="paint0_linear_1190_3583" x1="272.658" y1="224.147" x2="164.236" y2="64.8005" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#1F2128"/>
</linearGradient>
</defs>
</svg>
