<svg width="360" height="50" viewBox="0 0 360 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_1997_536)">
<rect width="360" height="50" rx="10.2895" fill="#D9D9D9" fill-opacity="0.04"/>
<rect x="0.5" y="0.5" width="359" height="49" rx="9.78951" stroke="url(#paint0_linear_1997_536)" stroke-opacity="0.69"/>
</g>
<defs>
<filter id="filter0_i_1997_536" x="0" y="0" width="363" height="53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="2.0579"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1997_536"/>
</filter>
<linearGradient id="paint0_linear_1997_536" x1="348.074" y1="58.9862" x2="339.636" y2="-1.17744" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#1F2128"/>
</linearGradient>
</defs>
</svg>
