<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="322" height="450" viewBox="0 0 322 450" fill="none">
<g clip-path="url(#clip0_933_1789)">
<rect width="322" height="450" rx="20" fill="#191A20"/>




<g filter="url(#filter2_f_933_1789)">
<circle cx="84" cy="-80" r="80" fill="#9786FF"/>
<circle cx="84" cy="-80" r="80" fill="url(#paint9_linear_933_1789)" fill-opacity="0.8"/>
<g clip-path="url(#paint10_angular_933_1789_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.08 0.104772 -0.07498 0.14638 84 -73.5025)"><foreignObject x="-941.285" y="-941.285" width="1882.57" height="1882.57"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="84" cy="-80" r="80" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:159.99998474121094,&quot;m01&quot;:-149.95991516113281,&quot;m02&quot;:78.979965209960938,&quot;m10&quot;:209.54313659667969,&quot;m11&quot;:292.75906372070312,&quot;m12&quot;:-324.65365600585938},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
</g>
</g>
<rect x="0.5" y="0.5" width="321" height="450" rx="19.5" stroke="url(#paint11_radial_933_1789)" stroke-opacity="0.4"/>
<defs>



<filter id="filter2_f_933_1789" x="-96" y="-260" width="360" height="360" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_933_1789"/>
</filter>
<clipPath id="paint10_angular_933_1789_clip_path"><circle cx="84" cy="-80" r="80"/></clipPath>







<linearGradient id="paint9_linear_933_1789" x1="84" y1="-111.675" x2="192.6" y2="-52.3818" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint11_radial_933_1789" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(99.5517 -8.76744) rotate(104.922) scale(129.218 81.103)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>



</defs>
</svg>