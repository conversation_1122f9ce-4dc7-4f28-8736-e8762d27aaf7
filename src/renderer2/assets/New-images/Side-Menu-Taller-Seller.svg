<svg width="325" height="1000" viewBox="0 0 325 1000" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_865_3695)">
<rect x="2" y="1" width="321" height="999" rx="21" fill="#191A20"/>
</g>
<rect x="2.5" y="1.5" width="320" height="998" rx="20.5" stroke="url(#paint2_radial_865_3695)" stroke-opacity="0.4"/>
<g filter="url(#filter1_d_865_3695)">
<g clip-path="url(#clip1_865_3695)">
<path d="M2 16C2 7.16344 9.16344 0 18 0H307C315.837 0 323 7.16344 323 16V154H2V16Z" fill="#0F0F14"/>
<g opacity="0.8" filter="url(#filter2_f_865_3695)">
<circle cx="259" cy="214" r="40" fill="#9786FF"/>
<circle cx="259" cy="214" r="40" fill="url(#paint3_linear_865_3695)" fill-opacity="0.8"/>
<g clip-path="url(#paint4_angular_865_3695_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.04 0.0523858 -0.03749 0.0731898 259 217.249)"><foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="259" cy="214" r="40" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:79.999992370605469,&#34;m01&#34;:-74.979957580566406,&#34;m02&#34;:256.4899902343750,&#34;m10&#34;:104.77156829833984,&#34;m11&#34;:146.37953186035156,&#34;m12&#34;:91.673179626464844},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<g opacity="0.56">
<g filter="url(#filter3_f_865_3695)">
<ellipse cx="65" cy="-113.5" rx="116" ry="129.5" fill="#9786FF"/>
<ellipse cx="65" cy="-113.5" rx="116" ry="129.5" fill="url(#paint5_linear_865_3695)" fill-opacity="0.8"/>
<g clip-path="url(#paint6_angular_865_3695_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.116 0.169599 -0.108721 0.236952 65 -102.982)"><foreignObject x="-937.498" y="-937.498" width="1875" height="1875"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><ellipse cx="65" cy="-113.5" rx="116" ry="129.5" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:231.99996948242188,&#34;m01&#34;:-217.44187927246094,&#34;m02&#34;:57.720954895019531,&#34;m10&#34;:339.19796752929688,&#34;m11&#34;:473.90371704101562,&#34;m12&#34;:-509.53308105468750},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
</g>
<path d="M18 0.5H307C315.56 0.5 322.5 7.43959 322.5 16V153.5H2.5V16C2.5 7.43958 9.43959 0.5 18 0.5Z" stroke="url(#paint7_radial_865_3695)" stroke-opacity="0.4"/>
<path d="M18 0.5H307C315.56 0.5 322.5 7.43959 322.5 16V153.5H2.5V16C2.5 7.43958 9.43959 0.5 18 0.5Z" stroke="url(#paint8_linear_865_3695)" stroke-opacity="0.27"/>
</g>
<defs>
<filter id="filter0_d_865_3695" x="-2.08999" y="246.949" width="329.18" height="742.051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="10.9731" operator="erode" in="SourceAlpha" result="effect1_dropShadow_865_3695"/>
<feOffset dy="-15.9609"/>
<feGaussianBlur stdDeviation="7.53156"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_865_3695"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_865_3695" result="shape"/>
</filter>
<filter id="filter1_d_865_3695" x="0.6" y="0" width="323.8" height="160.9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="erode" in="SourceAlpha" result="effect1_dropShadow_865_3695"/>
<feOffset dy="5.5"/>
<feGaussianBlur stdDeviation="2.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.91 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_865_3695"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_865_3695" result="shape"/>
</filter>
<filter id="filter2_f_865_3695" x="159" y="114" width="200" height="200" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_865_3695"/>
</filter>
<clipPath id="paint4_angular_865_3695_clip_path"><circle cx="259" cy="214" r="40"/></clipPath><filter id="filter3_f_865_3695" x="-151" y="-343" width="432" height="459" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_865_3695"/>
</filter>
<clipPath id="paint6_angular_865_3695_clip_path"><ellipse cx="65" cy="-113.5" rx="116" ry="129.5"/></clipPath><linearGradient id="paint0_linear_865_3695" x1="387.73" y1="2085.82" x2="-1155.96" y2="628.325" gradientUnits="userSpaceOnUse">
<stop stop-color="#2B2D33"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint1_linear_865_3695" x1="158.964" y1="173.211" x2="168.641" y2="465.456" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1B21"/>
</linearGradient>
<radialGradient id="paint2_radial_865_3695" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(101.242 -15.8436) rotate(97.8729) scale(242.159 82.884)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_865_3695" x1="259" y1="198.162" x2="313.3" y2="227.809" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_865_3695" x1="65" y1="-164.774" x2="229.955" y2="-84.1002" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint7_radial_865_3695" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(101.242 -2.59651) rotate(131.893) scale(49.6751 62.2855)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint8_linear_865_3695" x1="-12.445" y1="436.545" x2="226.186" y2="298.308" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<clipPath id="clip0_865_3695">
<rect x="2" y="1" width="321" height="999" rx="21" fill="white"/>
</clipPath>
<clipPath id="clip1_865_3695">
<path d="M2 16C2 7.16344 9.16344 0 18 0H307C315.837 0 323 7.16344 323 16V154H2V16Z" fill="white"/>
</clipPath>
</defs>
</svg>
