<svg width="602" height="305" viewBox="0 0 602 305" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_682_2774)">
<rect width="602" height="305" rx="16" fill="url(#paint0_linear_682_2774)"/>
</g>
<rect x="0.5" y="0.5" width="601" height="304" rx="15.5" stroke="url(#paint1_linear_682_2774)" stroke-opacity="0.26"/>
<rect x="0.5" y="0.5" width="601" height="304" rx="15.5" stroke="url(#paint2_linear_682_2774)" stroke-opacity="0.3"/>
<defs>
<filter id="filter0_i_682_2774" x="0" y="0" width="603" height="306" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_682_2774"/>
<feOffset dx="8" dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_682_2774"/>
</filter>
<linearGradient id="paint0_linear_682_2774" x1="279.554" y1="-141.825" x2="930.224" y2="444.372" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F0F14"/>
<stop offset="1" stop-color="#393E47"/>
</linearGradient>
<linearGradient id="paint1_linear_682_2774" x1="190.667" y1="398.294" x2="101.696" y2="175.434" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#0F0F14"/>
</linearGradient>
<linearGradient id="paint2_linear_682_2774" x1="167.787" y1="-164.162" x2="328.053" y2="45.7733" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
