<svg width="258" height="52" viewBox="0 0 258 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_717_6202)">
<rect x="1" y="1" width="256" height="50" rx="13" fill="white" fill-opacity="0.04"/>
<rect x="0.5" y="0.5" width="257" height="51" rx="13.5" stroke="url(#paint0_linear_717_6202)"/>
</g>
<defs>
<filter id="filter0_i_717_6202" x="0" y="0" width="260.21" height="54.2099" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.20994" dy="2.20994"/>
<feGaussianBlur stdDeviation="1.10497"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_6202"/>
</filter>
<linearGradient id="paint0_linear_717_6202" x1="157.554" y1="145.485" x2="151.379" y2="13.5586" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C8B99"/>
<stop offset="1" stop-color="#2F2E33"/>
</linearGradient>
</defs>
</svg>
