<svg width="26" height="35" viewBox="0 0 26 35" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#k79t8i7sja)">
        <path d="M10.595 25.069c-.2 0-.402-.112-.555-.336L5.23 17.72c-.307-.448-.307-1.171 0-1.619.307-.448.804-.448 1.11 0l4.255 6.204L19.66 9.086c.306-.448.803-.448 1.11 0 .307.448.307 1.171 0 1.62l-9.62 14.027c-.153.224-.354.336-.555.336z" fill="#32FF6C" stroke="#32FF6C" stroke-width="2"/>
    </g>
    <defs>
        <filter id="k79t8i7sja" x="-3" y="0" width="32" height="43" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3_160627"/>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_3_160627" result="shape"/>
        </filter>
    </defs>
</svg>
