<svg width="282" height="144" viewBox="0 0 282 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Material Total">
<g id="BG">
<g filter="url(#filter0_i_865_3096)">
<path d="M0 10C0 4.47715 4.47715 0 10 0H272C277.523 0 282 4.47715 282 10V110H0V10Z" fill="#D9D9D9" fill-opacity="0.04"/>
</g>
<path d="M10 0.514476H272C277.239 0.514476 281.486 4.76129 281.486 10V109.486H0.514476V10C0.514476 4.76129 4.76129 0.514476 10 0.514476Z" stroke="url(#paint0_linear_865_3096)" stroke-opacity="0.69" stroke-width="1.02895"/>
</g>
<path id="Total BG" d="M0 110H282V134C282 139.523 277.523 144 272 144H10C4.47716 144 0 139.523 0 134V110Z" fill="url(#paint1_linear_865_3096)"/>
</g>
<defs>
<filter id="filter0_i_865_3096" x="-2.0579" y="-2" width="284.058" height="112" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.0579" dy="-2"/>
<feGaussianBlur stdDeviation="2.0579"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_865_3096"/>
</filter>
<linearGradient id="paint0_linear_865_3096" x1="30.5898" y1="-35.2591" x2="33.4085" y2="25.783" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#1F2128"/>
</linearGradient>
<linearGradient id="paint1_linear_865_3096" x1="111.076" y1="188.545" x2="101.661" y2="102.03" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#191A20"/>
</linearGradient>
</defs>
</svg>
