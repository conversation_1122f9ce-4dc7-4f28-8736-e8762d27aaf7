<svg width="638" height="42" viewBox="0 0 638 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_889_1654)">
<rect x="1" y="1" width="636" height="40" rx="10" fill="white" fill-opacity="0.04"/>
<rect x="0.75" y="0.75" width="636.5" height="40.5" rx="10.25" stroke="url(#paint0_linear_889_1654)" stroke-width="0.5"/>
<rect x="0.75" y="0.75" width="636.5" height="40.5" rx="10.25" stroke="url(#paint1_linear_889_1654)" stroke-width="0.5"/>
</g>
<defs>
<filter id="filter0_ii_889_1654" x="-3.5" y="-0.5" width="644" height="43" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_889_1654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-5" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_889_1654" result="effect2_innerShadow_889_1654"/>
</filter>
<linearGradient id="paint0_linear_889_1654" x1="-8.71667" y1="30" x2="590.122" y2="56.212" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFBE73"/>
<stop offset="1" stop-color="#2F2E33" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_889_1654" x1="491.869" y1="173.69" x2="633.944" y2="15.0043" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D1B1B" stop-opacity="0"/>
<stop offset="1" stop-color="#FFAE00"/>
</linearGradient>
</defs>
</svg>
