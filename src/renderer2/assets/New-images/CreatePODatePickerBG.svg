<svg width="254" height="192" viewBox="0 0 254 192" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_863_3102)">
<rect x="2" y="2" width="250" height="188" rx="13" fill="#1F2026"/>
<rect x="1.26335" y="1.26335" width="251.473" height="189.473" rx="13.7366" stroke="url(#paint0_linear_863_3102)" stroke-opacity="0.85" stroke-width="1.4733"/>
</g>
<defs>
<filter id="filter0_i_863_3102" x="0.527344" y="0.527344" width="256.945" height="194.945" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="5.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_863_3102"/>
</filter>
<linearGradient id="paint0_linear_863_3102" x1="222.884" y1="358.156" x2="251.833" y2="26.6366" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#18191E"/>
</linearGradient>
</defs>
</svg>
