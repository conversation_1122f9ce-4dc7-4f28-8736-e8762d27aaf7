<svg width="669" height="40" viewBox="0 0 669 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_1443_2843)">
<rect width="669" height="40" rx="5" fill="white" fill-opacity="0.04"/>
</g>
<rect x="0.5" y="0.5" width="668" height="39" rx="4.5" stroke="url(#paint0_linear_1443_2843)" stroke-opacity="0.32"/>
<rect x="0.5" y="0.5" width="668" height="39" rx="4.5" stroke="url(#paint1_radial_1443_2843)" stroke-opacity="0.25"/>
<line x1="554.5" y1="1" x2="554.5" y2="39" stroke="white" stroke-opacity="0.1"/>
<line x1="258.5" y1="2" x2="258.5" y2="40" stroke="white" stroke-opacity="0.1"/>
<line x1="453.5" y1="2" x2="453.5" y2="40" stroke="white" stroke-opacity="0.1"/>
<line x1="143.5" y1="1" x2="143.5" y2="39" stroke="white" stroke-opacity="0.1"/>
<defs>
<filter id="filter0_i_1443_2843" x="0" y="0" width="673" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1443_2843"/>
</filter>
<linearGradient id="paint0_linear_1443_2843" x1="244.5" y1="63" x2="242.5" y2="15" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#101014"/>
</linearGradient>
<radialGradient id="paint1_radial_1443_2843" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(67.5 -15) rotate(81.8699) scale(35.3553 313.9)">
<stop stop-color="white"/>
<stop offset="1" stop-color="#101014"/>
</radialGradient>
</defs>
</svg>
