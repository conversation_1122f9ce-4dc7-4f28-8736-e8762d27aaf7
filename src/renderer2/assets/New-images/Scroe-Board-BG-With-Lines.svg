<svg width="250" height="130" viewBox="0 0 250 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_933_2062)">
<g clip-path="url(#clip0_933_2062)">
<rect width="250" height="130" rx="13" fill="url(#paint0_linear_933_2062)" fill-opacity="0.86"/>
<line x1="58.125" y1="-5.46392e-09" x2="58.125" y2="50" stroke="#0F0F14" stroke-width="0.25"/>
<line y1="49.875" x2="250" y2="49.875" stroke="#0F0F14" stroke-width="0.25"/>
<rect y="89" width="250" height="3" fill="#333333"/>
</g>
</g>
<defs>
<filter id="filter0_i_933_2062" x="0" y="0" width="253" height="132" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="8" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_933_2062"/>
<feOffset dx="3" dy="2"/>
<feGaussianBlur stdDeviation="15.4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.430833 0 0 0 0 0.810278 0 0 0 0 1 0 0 0 0.36 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_933_2062"/>
</filter>
<linearGradient id="paint0_linear_933_2062" x1="221.309" y1="2.64905" x2="-46.9569" y2="-55.0643" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B8E7FF"/>
</linearGradient>
<clipPath id="clip0_933_2062">
<rect width="250" height="130" rx="13" fill="white"/>
</clipPath>
</defs>
</svg>
