<svg width="322" height="520" viewBox="0 0 322 520" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#6pmlnhmc8a)">
        <rect width="322" height="520" rx="20" fill="#191A20"/>
        <g opacity=".8" filter="url(#fzuibmuk7b)">
            <circle cx="265" cy="562" r="40" fill="#9786FF"/>
            <circle cx="265" cy="562" r="40" fill="url(#rjif260ync)" fill-opacity=".8"/>
            <g clip-path="url(#4ds1yd2t9d)" data-figma-skip-parse="true">
                <foreignObject x="-952.598" y="-952.598" width="1905.2" height="1905.2" transform="matrix(.04 .05239 -.03749 .07319 265 565.249)">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#fefefe 0deg,rgba(255,255,255,0) 66.6644deg,#fff 143.541deg,rgba(255,255,255,0) 199.792deg,#fff 272.918deg,rgba(255,255,255,0) 349.795deg,#fefefe 360deg);height:100%;width:100%;opacity:.5"/>
                </foreignObject>
            </g>
            <circle cx="265" cy="562" r="40" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:79.999992370605469,&quot;m01&quot;:-74.979957580566406,&quot;m02&quot;:262.4899902343750,&quot;m10&quot;:104.77156829833984,&quot;m11&quot;:146.37953186035156,&quot;m12&quot;:439.67318725585938},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
        </g>
        <g filter="url(#ndevtapu8e)">
            <circle cx="76" cy="-81.375" r="80" fill="#9786FF"/>
            <circle cx="76" cy="-81.375" r="80" fill="url(#b51stgrt8f)" fill-opacity=".8"/>
            <g clip-path="url(#kt5gjvt6qg)" data-figma-skip-parse="true">
                <foreignObject x="-941.285" y="-941.285" width="1882.57" height="1882.57" transform="matrix(.08 .10477 -.07498 .14638 76 -74.877)">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#fefefe 0deg,rgba(255,255,255,0) 66.6644deg,#fff 143.541deg,rgba(255,255,255,0) 199.792deg,#fff 272.918deg,rgba(255,255,255,0) 349.795deg,#fefefe 360deg);height:100%;width:100%;opacity:.5"/>
                </foreignObject>
            </g>
            <circle cx="76" cy="-81.375" r="80" data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.99964576959609985,&quot;g&quot;:0.99964576959609985,&quot;b&quot;:0.99964576959609985,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.18517892062664032},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.39872443675994873},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.55497723817825317},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:1.0},&quot;position&quot;:0.75810593366622925},{&quot;color&quot;:{&quot;r&quot;:1.0,&quot;g&quot;:1.0,&quot;b&quot;:1.0,&quot;a&quot;:0.0},&quot;position&quot;:0.97165143489837646}],&quot;stopsVar&quot;:[],&quot;transform&quot;:{&quot;m00&quot;:159.99998474121094,&quot;m01&quot;:-149.95991516113281,&quot;m02&quot;:70.979965209960938,&quot;m10&quot;:209.54313659667969,&quot;m11&quot;:292.75906372070312,&quot;m12&quot;:-326.02865600585938},&quot;opacity&quot;:0.50,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"/>
        </g>
        <g filter="url(#h2itleydth)">
            <path d="M20 81c0-5.523 4.477-10 10-10h262c5.523 0 10 4.477 10 10v262c0 5.523-4.477 10-10 10H30c-5.523 0-10-4.477-10-10V81z" fill="url(#n4tc7kkisi)" fill-opacity=".04"/>
        </g>
        <path d="M30 71.5h262a9.5 9.5 0 0 1 9.5 9.5v262a9.5 9.5 0 0 1-9.5 9.5H30a9.5 9.5 0 0 1-9.5-9.5V81a9.5 9.5 0 0 1 9.5-9.5z" stroke="url(#rt5g8agr3j)" stroke-opacity=".69"/>
        
        <path fill="#000" d="M121 220h180v2H121z"/>
        <path fill="#fff" fill-opacity=".15" d="M121 222h180v1H121z"/>
        <path fill="#000" d="M122 105h179v2H122z"/>
        <path fill="#3B3C41" d="M122 107h179v1H122z"/>
        <path transform="rotate(90 121 108)" fill="url(#99vangra7k)" d="M121 108h244v10H121z"/>
        <path fill="#000" d="M119 72h2v36h-2z"/>
        <path fill="#3B3C41" d="M121 72h1v36h-1z"/>
        <g clip-path="url(#06ahasmsel)">
            <path fill="#000" d="M21 105h99v2H21z"/>
            <path fill="#3B3C41" d="M21 107h101v1H21z"/>
        </g>
        <path d="M21 108h100v18a2 2 0 0 1-2 2H21v-20z" fill="url(#youltlmokm)" fill-opacity=".04"/>
        <path d="M21 126h97.475c1.341-.025 1.835-.247 2.525-1v11H21v-10z" fill="url(#dqxzshv4nn)"/>
    </g>
    <rect x=".5" y=".5" width="321" height="519" rx="19.5" stroke="url(#2ucpwycmyo)" stroke-opacity=".4"/>
    <defs>
        <linearGradient id="rjif260ync" x1="265" y1="546.162" x2="319.3" y2="575.809" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7ECEF"/>
            <stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="b51stgrt8f" x1="76" y1="-113.05" x2="184.6" y2="-53.757" gradientUnits="userSpaceOnUse">
            <stop stop-color="#E7ECEF"/>
            <stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="n4tc7kkisi" x1="-171.677" y1="13.79" x2="31.06" y2="345.716" gradientUnits="userSpaceOnUse">
            <stop stop-color="#2B2D33"/>
            <stop offset="1" stop-color="#0F0F14"/>
        </linearGradient>
        <linearGradient id="rt5g8agr3j" x1="181.795" y1="477.136" x2="-46.623" y2="209.347" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff" stop-opacity="0"/>
            <stop offset="1" stop-color="#1F2128"/>
        </linearGradient>
        <linearGradient id="99vangra7k" x1="251.9" y1="108" x2="251.814" y2="117.999" gradientUnits="userSpaceOnUse">
            <stop/>
            <stop offset="1" stop-color="#191A20" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="youltlmokm" x1="-46.971" y1="103.943" x2="-43.08" y2="135.79" gradientUnits="userSpaceOnUse">
            <stop stop-color="#2B2D33"/>
            <stop offset="1" stop-color="#0F0F14"/>
        </linearGradient>
        <linearGradient id="dqxzshv4nn" x1="74.647" y1="126" x2="74.438" y2="135.996" gradientUnits="userSpaceOnUse">
            <stop/>
            <stop offset="1" stop-color="#191A20" stop-opacity="0"/>
        </linearGradient>
        <clipPath id="4ds1yd2t9d">
            <circle cx="265" cy="562" r="40"/>
        </clipPath>
        <clipPath id="kt5gjvt6qg">
            <circle cx="76" cy="-81.375" r="80"/>
        </clipPath>
        <clipPath id="6pmlnhmc8a">
            <rect width="322" height="520" rx="20" fill="#fff"/>
        </clipPath>
        <clipPath id="06ahasmsel">
            <path fill="#fff" transform="translate(21 105)" d="M0 0h100v3H0z"/>
        </clipPath>
        <filter id="fzuibmuk7b" x="165" y="462" width="200" height="200" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="30" result="effect1_foregroundBlur_1398_4052"/>
        </filter>
        <filter id="ndevtapu8e" x="-104" y="-261.375" width="360" height="360" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_1398_4052"/>
        </filter>
        <filter id="h2itleydth" x="18" y="69" width="288" height="288" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="4" dy="4"/>
            <feGaussianBlur stdDeviation="2.058"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="shape" result="effect1_innerShadow_1398_4052"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="-2" dy="-2"/>
            <feGaussianBlur stdDeviation="2.058"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
            <feBlend in2="effect1_innerShadow_1398_4052" result="effect2_innerShadow_1398_4052"/>
        </filter>
        <radialGradient id="2ucpwycmyo" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-33.27413 124.86042 -78.36799 -20.88433 99.552 -8.767)">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#fff" stop-opacity="0"/>
        </radialGradient>
    </defs>
</svg>
