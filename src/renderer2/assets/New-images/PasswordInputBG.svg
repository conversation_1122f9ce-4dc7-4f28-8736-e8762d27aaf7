<svg width="258" height="52" viewBox="0 0 258 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_717_6199)">
<rect x="1" y="1" width="256" height="50" rx="13" fill="white" fill-opacity="0.04"/>
<rect x="0.5" y="0.5" width="257" height="51" rx="13.5" stroke="url(#paint0_linear_717_6199)" stroke-opacity="0.3"/>
</g>
<defs>
<filter id="filter0_ii_717_6199" x="-2.20994" y="-2.20994" width="262.42" height="54.2099" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.20994" dy="-2.20994"/>
<feGaussianBlur stdDeviation="1.4733"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_717_6199"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.20994"/>
<feGaussianBlur stdDeviation="1.4733"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_717_6199" result="effect2_innerShadow_717_6199"/>
</filter>
<linearGradient id="paint0_linear_717_6199" x1="120.795" y1="-40.1765" x2="88.8483" y2="-81.2578" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#2C2C2C"/>
</linearGradient>
</defs>
</svg>
