import { useMutation } from '@tanstack/react-query';
import { axios } from '@bryzos/giss-ui-library';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';

interface AchSubscribeParams {
  data: {
    user_count: number;
    payment_method: string;
    payment_details: {
      account_name: string;
      account_type: string;
      bank_name: string;
    };
    payment_method_id: string;
    setup_intent_id: string;
  };
}

interface AchSubscribeResponse {
  data: {
    client_secret: string;
    subscription_id?: string;
    status?: string;
    error_message?: {
      reason: string;
      email?: string[];
      message?: string;
    };
  };
}

/**
 * Hook for creating a subscription using ACH payment method
 */
const usePostAchSubscribe = () => {
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  return useMutation<AchSubscribeResponse, Error, AchSubscribeParams>(
    async (params) => {
      const response = await axios.post<AchSubscribeResponse>(
        `${import.meta.env.VITE_API_SERVICE}/subscription/ach/subscribe`,
        params
      );

      // Check if the response contains an error message
      if (response.data?.data?.error_message) {
        const errorData = response.data.data.error_message;
          // For general errors, show a dialog
          const errorMessage = errorData.message || 'Failed to create subscription. Please try again.';
          showCommonDialog(null, errorMessage, null, resetDialogStore, [
            { name: 'OK', action: resetDialogStore }
          ]);
      }

      return response.data;
    }
  );
};

export default usePostAchSubscribe;
