import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostSaveDefaultOrderSize = () => {

  return useMutation(async (data: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/user/default-order-size`,
        {data}
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          if (response.data.data !== 'Success') {
            const { userData, setUserData } = useGlobalStore.getState();
            const spreadUserData = {...userData}
            spreadUserData.data.order_size = data.order_size
            setUserData(spreadUserData)
          }
          return response.data.data;
        }
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostSaveDefaultOrderSize;
