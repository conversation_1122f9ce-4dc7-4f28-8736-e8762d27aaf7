import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

const useGetSharedProductPrices = () => {
  return useQuery({
    queryKey: [reactQueryKeys.getSharedProductPrices],
    queryFn: async () => {
      try {
        // const url = `${import.meta.env.VITE_API_SERVICE}/user/get-shared-product-prices`;
        const url = `${import.meta.env.VITE_API_SERVICE}/user/shared-product-prices`;
        const response = await axios.get(url);
        return response;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 0,
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: true
  });
};

export default useGetSharedProductPrices; 