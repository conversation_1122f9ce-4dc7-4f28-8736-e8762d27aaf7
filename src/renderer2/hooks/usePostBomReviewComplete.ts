import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostBomReviewComplete = () => {

  return useMutation(async (data: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/user/bom/review-complete`,
        data
      );

      if (response.data?.data) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostBomReviewComplete;
