import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

const useGetDraftLines = () => {
  return useMutation(async (id?: string) => {
    try {
      const API_URL = import.meta.env.VITE_API_ORDER_SERVICE + "/buyer/draft-lines/" + id;
      const response = await axios.get(API_URL);

      if (response.data?.data?.error_message) {
        return [];
      }

      return response.data;
    } catch (error: any) {
      throw new Error(error?.message || "Failed to fetch Search data");
    }
  });
};

export default useGetDraftLines; 