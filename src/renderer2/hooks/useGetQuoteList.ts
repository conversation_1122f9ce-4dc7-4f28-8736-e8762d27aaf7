import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";

const useGetQuoteList = () => {
  return useMutation(async (params?: Record<string, any>) => {
    try {
      const API_URL = import.meta.env.VITE_API_ORDER_SERVICE + "/buyer/quotes";
      const response = await axios.get(API_URL, { params });

      if (response.data?.data?.error_message) {
        return [];
      }

      return response.data;
    } catch (error: any) {
      throw new Error(error?.message || "Failed to fetch Search data");
    }
  });
};

export default useGetQuoteList; 