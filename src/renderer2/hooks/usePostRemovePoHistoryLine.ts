import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostRemovePoHistoryLine = () => {

  return useMutation(async (data: any) => {
    try {
      const url = `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/remove-line-from-po`;
      console.log("usePostRemovePoHistoryLine... ", data);
      const response = await axios.post(
        url,
        {data}
      );

      if (response.data?.data) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostRemovePoHistoryLine;
