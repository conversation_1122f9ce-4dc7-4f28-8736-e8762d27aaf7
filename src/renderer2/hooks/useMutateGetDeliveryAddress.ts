import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const useMutateGetDeliveryAddress = () => {
    return useMutation({
        mutationFn: async () => {
            try {
                const response = await axios.get(import.meta.env.VITE_API_ORDER_SERVICE + '/buyer/delivery-address');
                if (response.data?.data) {
                    if (
                        typeof response.data.data === "object" &&
                        "error_message" in response.data.data
                    ) {
                        throw new Error(response.data.data.error_message);
                    } else {
                        return response.data.data;
                    }
                } else {
                    return null;
                }
            } catch (error) {
                throw new Error(error?.message ?? error);
            }
        }
    });
};

export default useMutateGetDeliveryAddress;
