import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostAddPoHistoryLine = () => {

  return useMutation(async (data: any) => {
    try {
      const url = `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/add-new-lines-to-po`;
      console.log("usePostAddPoHistoryLine... ", data);
      const response = await axios.post(
        url,
        {data}
      );

      if (response.data?.data) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostAddPoHistoryLine;
