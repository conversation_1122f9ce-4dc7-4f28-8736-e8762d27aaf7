import React, { useState, useRef, useEffect } from "react";
import { ClickAwayListener } from "@mui/material";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import "./SearchBox.css"; // for styling
import { routes } from "src/renderer2/common";
import { useNavigate,useLocation  } from "react-router-dom";
import { Widgets } from "@mui/icons-material";
import { searchProducts, useCreatePoStore, useGlobalClockStore, useSearchStore } from "@bryzos/giss-ui-library";
import { filterObjectsByString, flattenObject } from "src/renderer2/helper";
import { useGlobalSearchStore } from "./globalSearchStore";
import { CustomMenu } from "../buyer/CustomMenu";
import SearchResultMenu from "./searchResultMenu";
import styles from "./searchBox.module.scss";
import { ReactComponent as SearchIcon } from "../../assets/New-images/Search.svg";
import { ReactComponent as DropdownIcon } from "../../assets/New-images/New-Image-latest/Polygon.svg";
import { ReactComponent as CheckIcon } from "../../assets/New-images/New-Image-latest/icon-check-dropdown.svg";
import useGetDraftLines from "src/renderer2/hooks/useGetDraftLines";

const SearchBox: React.FC<{ onSubscribeClick: () => void }> = ({ onSubscribeClick }) => {
  const [isFocused, setIsFocused] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchAnchorEl, setSearchAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const open = Boolean(anchorEl);
  const {savedSearchProducts } = useSearchStore();
  const {quoteList, purchasingList}  = useCreatePoStore();
  const [flattenedSavedSearchProducts, setFlattenedSavedSearchProducts] = useState([]);
  const [flattenedQuoteList, setFlattenedQuoteList] = useState([]);
  const [flattenedPurchasingList, setFlattenedPurchasingList] = useState([]);
  const [filteredObjects, setFilteredObjects] = useState([]);
  const { setSelectedIndex, setSelectedObject, createPoUpdatedData } = useGlobalSearchStore();
  const [searchText, setSearchText] = useState('');
  const {mutateAsync: getDraftLines} = useGetDraftLines();

  const options = [
    {label:'Instant Price Search', route:routes.homePage},
    {label:'Quoting', route:routes.quotePage},
    {label:'Purchasing', route:routes.createPoPage},
    {label:'Order Management'}//route:routes.homePage
    ];
  const currentPath = location.pathname; // e.g., "/pricing"
  const [selectedOption, setSelectedOption] = useState(options[0]);

useEffect(() => {
    const selectedOption = options.find(option => currentPath.includes(option.route));
    console.log('selectedOption', selectedOption);
    setSelectedOption(selectedOption || options[0]);
}, [currentPath]);

useEffect(() => {
    if(createPoUpdatedData){
      //filter quoteList and purchasingList and remove if we get a match replace it
      if(createPoUpdatedData.order_type === 'QUOTE'){
        setFlattenedQuoteList(prev =>
          prev.map(item =>
            item.id === createPoUpdatedData.id ? { ...item, ...flattenObject(createPoUpdatedData) } : item
          )
        );
      }else if(createPoUpdatedData.order_type === 'PO'){
        setFlattenedPurchasingList(prev =>
          prev.map(item =>
            item.id === createPoUpdatedData.id ? { ...item, ...flattenObject(createPoUpdatedData) } : item
          )
        );
      }
    }
},[createPoUpdatedData]);

useEffect(() => {
    if(savedSearchProducts){
        const flattenedData = savedSearchProducts.map((product: any) => flattenObject(product));
        
        setFlattenedSavedSearchProducts(flattenedData);
    }
},[savedSearchProducts]);

useEffect(() => {
  if (quoteList) {
    const loadData = async () => {
      const flattenedData = await Promise.all(
        quoteList.map(async (product: any) => {
          const tempFlattenedObj = flattenedQuoteList.find((item: any) => item.id === product.id);
          if (tempFlattenedObj ) {
            return tempFlattenedObj;
          }

          let cart_items = [];
          try {
            const res = await getDraftLines(product.id);
            cart_items = res.data;
          } catch (err) {
            console.error(`Failed to get draft lines for product ${product.id}`, err);
            cart_items = [];
          }
          const retObj = flattenObject({
            ...product,
            cart_items
          });

          return retObj;
        })
      );

      setFlattenedQuoteList(flattenedData);
    };

    loadData();
  }
}, [quoteList]);


useEffect(() => {
  if (purchasingList) {
    const loadData = async () => {
      const flattenedData = await Promise.all(
        purchasingList.map(async (product: any) => {
          const tempFlattenedObj = flattenedPurchasingList.find((item: any) => item.id === product.id);
          //console.log('tempFlattenedObj', tempFlattenedObj["cart_items#0.id"]);
          if (tempFlattenedObj) {
            return tempFlattenedObj;
          }

          let cart_items = [];
          try {
            const res = await getDraftLines(product.id);
            cart_items = res.data;
          } catch (err) {
            console.error(`Failed to get draft lines for product ${product.id}`, err);
            cart_items = [];
          }
          const retObj = flattenObject({
            ...product,
            cart_items
          });

          return retObj;
        })
      );

      setFlattenedPurchasingList(flattenedData);
    };

    loadData();
  }
}, [purchasingList]);

  const handleDropdownButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setIsFocused(true);
  };

  const handleMenuItemClick = (option) => {
    if(option.route){
      navigate(option.route);
      setIsFocused(false);
    }
    setAnchorEl(null);
  };

  const handleClickAway = () => {
    setIsFocused(false);
    setAnchorEl(null); 
    setFilteredObjects([]);
    setSearchText('');
  };

  const searchChangeHandler = (event) => {
    const searchString = event.target.value;
    setSearchText(searchString);
    if(searchString.length < 3){
      setFilteredObjects([]);
      return;
    }
    let filteredData = [];
    switch(selectedOption.route){
      case routes.homePage:
        filteredData = filterObjectsByString(flattenedSavedSearchProducts, searchString);
        break;
      case routes.quotePage:
        filteredData = filterObjectsByString(flattenedQuoteList, searchString);
        break;
      case routes.createPoPage:
        filteredData = filterObjectsByString(flattenedPurchasingList, searchString);
        break;
    }
    
    setFilteredObjects(filteredData);
  };

  const handleSearchFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    setSearchAnchorEl(event.currentTarget);
  };

  const handleSearchSelection = (obj: any) => {
      setFilteredObjects([]);
      setSearchText('');
      setIsFocused(false);
      switch(selectedOption.route){
        case routes.homePage:
          const item = savedSearchProducts[ obj.index];
          setSelectedIndex(obj.index);
          setSelectedObject({...item});
          break;
        case routes.quotePage:
          const quoteItem = quoteList[ obj.index];
          setSelectedIndex(obj.index);
          setSelectedObject({...quoteItem});
          break;
        case routes.createPoPage:
          const poItem = purchasingList[ obj.index];
          setSelectedIndex(obj.index);
          setSelectedObject({...poItem});
          break;
      }
    }
  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <div className="search-container" >
        <div className="search-button-and-field">
          {isFocused ? (
            <>
              <Button
                className={styles.searchTypeBtn}
                onClick={handleDropdownButtonClick}
              >
                {selectedOption.label} <DropdownIcon className={styles.dropdownIcon} />
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={() => setAnchorEl(null)}
                PaperProps={{
                  className: styles.dropdownMenu,
                  style: {
                    width: anchorEl?.offsetWidth || 300,
                    backgroundColor: 'transparent',
                  },
                }}
              >
                {options.map((option, index) => (
                  <MenuItem key={index} onClick={() => handleMenuItemClick(option)} disabled={!option.route}>
                    {selectedOption.label === option.label && <CheckIcon />}{option.label}
                  </MenuItem>
                ))}
              </Menu>
            </>
          ) : (
            <Button className={styles.subscribeBtn} onClick={onSubscribeClick} variant="contained">
              Subscribe
            </Button>
          )}
          <div className={styles.globalSearchField}>
            <SearchIcon className={styles.searchIcon} />
            <input
              className={styles.searchInput}
              placeholder="Search Your Account"
              value={searchText}
              onFocus={handleSearchFocus}
              onChange={searchChangeHandler}
              style={isFocused ? {borderRadius: '0px 30px 30px 0px'} : {borderRadius: '30px'}}
            />
            <SearchResultMenu
              anchorEl={searchAnchorEl}
              open={filteredObjects.length > 0}
              onClose={() => setFilteredObjects([])}
              items={filteredObjects}
              //items={filteredObjects.map((item,index)=>{return{id:index.toString(), label:item.filteredObj[item.field],item,date:'Wed 7/11' }})}
              // items={[{id:'1', label:'test'}]
              onItemClick={handleSearchSelection}
              PaperProps={{
                className: styles.dropdownMenu,
                style: {
                  minWidth: searchAnchorEl?.offsetWidth || 300,
                  backgroundColor: 'transparent',
                },
              }}
            />
            {/* <Menu
              anchorEl={searchAnchorEl}
              open={filteredObjects.length > 0}
              onClose={() => setFilteredObjects([])}
              PaperProps={{
                style: {
                  maxHeight: 300,
                  width: searchAnchorEl?.offsetWidth || 300,
                },
              }}
            >
              {filteredObjects.map((obj, idx) => (
                <MenuItem
                  key={idx}
                  onClick={() => handleSearchSelection(obj)}
                >
                <div>
                  <div style={{ fontWeight: 500 }}>{obj.filteredObj[obj.field].length > 15 ? obj.filteredObj[obj.field].slice(0, 15) + "..." : obj.filteredObj[obj.field]}</div>
                </div>
                </MenuItem>
              ))}
            </Menu> */}
          </div>
        </div>
      </div>
    </ClickAwayListener>
  );
};

export default SearchBox;
