import React, { useState, useRef } from 'react';
import { Popper, Paper, ClickAwayListener } from '@mui/material';
import styles from "./searchBox.module.scss";

type MenuItem = {
  id: string;
  label: string;
  date: string;
};

const SearchResultMenu = ({
  anchorEl,
  open,
  onClose,
  items,
  onItemClick,
}: {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  items: MenuItem[];
  onItemClick: (item: MenuItem) => void;
}) => {
  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement="bottom-start"
      style={{ zIndex: 1300 }}
      modifiers={[{ name: 'preventOverflow', options: { boundary: 'viewport' } }]}
      disablePortal={false} 
    >
      <ClickAwayListener onClickAway={onClose}>
        <Paper 
        className={styles.searchResultMenuPaper} 
        elevation={3}
        >
          {items.length === 0 ? (
            <div style={{ padding: 8, fontStyle: 'italic' }}>No results</div>
          ) : (
            items.map(item => (
              <div
                className={styles.searchResultMenuItem}
                key={item.id}
                onClick={() => onItemClick(item)}
              >
                <span className={styles.searchResultMenuItemLabel}>{item.label}</span>
                <span className={styles.searchResultMenuItemDateTime}>{item.date}</span>
              </div>
            ))
          )}
        </Paper>
      </ClickAwayListener>
    </Popper>
  );
};

export default SearchResultMenu;
