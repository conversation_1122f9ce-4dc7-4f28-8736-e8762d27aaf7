.chatMessagesContent {
    display: flex;
    padding: 0px 20px;

    .messageText {
        font-family: Roboto;
        display: flex;
        flex-direction: column;
        width: auto;
        max-width: 100%;
        min-width: 100px;
        background: #fff;
        margin: 0px 0px 8px 0px;
        padding: 12px 12px 6px 12px;
        border-radius: 10px;
        border: solid 1px #cecece;
        clear: both;
        font-size: 14px;
        position: relative;
        margin-left: 30px;

        p {
            margin-bottom: 5px;
        }

        .chatArrow {
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-right: 7px solid #fff;
            position: absolute;
            left: -6px;
            top: 12px;
            transform: translateY(-50%);
        }

        .chatTime {
            color: #000;
            opacity: 0.5;
            font-size: 10px;
            text-align: right;
            white-space: nowrap;
            margin-left: auto;
        }

    }

    .ownMessages {
        background-color: rgba(174, 208, 247, 0.9) !important;
        margin-left: auto;
        margin-right: 10px;
        border: solid 1px rgba(136, 188, 247, 0.9);

        .chatArrow {
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 7px solid rgba(174, 208, 247, 0.9);
            border-right: 0px;
            right: -6px;
            top: 12px;
            left: unset;
            transform: translateY(-50%);
        }

    }

    .ownMessagesBedge1 {
        top: 12px;
        right: -5px;
        font-size: 14px;
        font-weight: bold;
        min-width: 34px;
        padding: 2px 2px;
        height: 34px;
        border-radius: 50%;
    }

    .ownMessagesBedge {
        font-size: 14px;
        font-weight: bold;
        min-width: 34px;
        padding: 2px 2px;
        height: 34px;
        border-radius: 50%;
        top: 12px;
        right: -515px !important;
        background-color: #0072ff;
    }
    .ownMessagesSection{
        display: flex;
        margin-left: auto;
    }
}

.dateMain{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 8px;
    position: sticky;
    top: 0;
    z-index: 99;

    .date {
        font-size: 14px;
        background-color: #dbdbdb;
        border-radius: 12px;
        padding: 2px 16px;
        color: #000;
        text-align: center;
    }
    
}

.userName {
    font-size: 14px;
    text-transform: capitalize;
    margin-bottom: 3px;
}

.noChatFound {
    color: #fff;
    font-size: 18px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 580px;


    p {
        font-size: 16px;
        color: #fff;
    }
}

.reactionTooltip{
    margin-top: 0px !important;
    background-color: transparent !important;
    padding: 0px;
}