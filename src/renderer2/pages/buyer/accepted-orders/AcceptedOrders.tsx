import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { routes, userRole } from '../../../common';
import { useContext, useEffect, useState } from 'react';
import { UserContext } from '../../../UserContext';
import { useImmer } from 'use-immer';
import useCreateChannelAddUsers from '../../../hooks/useCreateChannelAddUsers';
import useGetAcceptedOrders from '../../../hooks/useGetAcceptedOrders';
import Loader from '../../../Loader/Loader';
import { useGlobalStore } from '@bryzos/giss-ui-library';

export const AcceptedOrders = () => {
    const navigate = useNavigate();
    const userContext: any = useContext(UserContext);
    const [orderData, setOrderData] = useImmer<any>(null);
    const [acceptedOrders, setAcceptedOrders] = useImmer<any>([]);

    const { userData, setUserData, sdk  }: any = useGlobalStore();

    const isBuyer = userData?.data?.type === userRole.buyerUser;

    const {
        data: _acceptedOrders,
        isLoading: isAcceptedOrdersLoading,
        error: isAcceptedOrdersError,
    } = useGetAcceptedOrders(userData?.type);

    const {
        data: createChannelAddUsersData,
        isLoading: iscreateChannelAddUsersLoading,
        error: createChannelAddUsersError,
        mutate: createChannelAddUsers,
    } = useCreateChannelAddUsers();

    useEffect(() => {
        if (isAcceptedOrdersLoading) {
            return;
        }
        if (_acceptedOrders?.length >= 0) {
            setAcceptedOrders(_acceptedOrders);
        }
    }, [isAcceptedOrdersError, isAcceptedOrdersLoading, _acceptedOrders]);

    useEffect(() => {
        if (iscreateChannelAddUsersLoading) {
            return;
        }

        if (createChannelAddUsersData && !createChannelAddUsersError) {
            const channelId = createChannelAddUsersData._id;
            (async () => {
                if (createChannelAddUsersData?.[userData?.id]) {
                    const data = createChannelAddUsersData[userData.id];
                    setUser((prev: any) => {
                        if (prev?.data) {
                            prev.data.is_moderator = data.isModerator;
                        }
                        return prev
                    });

                    if (!sdk?.roomJoined) {
                        if (data.isModerator) {
                            await sdk?.joinRoom({ accessToken: data.accessToken });
                        } else {
                            await sdk?.joinRoom({ uniqueUserIdentifier: data.uniqueUserIdentifier });
                        }
                    }
                    const result = await sdk?.selectChannel(channelId);
                }
            })();

            if (channelId && orderData) {
                navigate('/v2' + '/' + routes.chat, {
                    state: {
                        order: orderData,
                        channelName: orderData.buyer_po_number,
                        channelId: channelId,
                        userId: '65b114e56a41e0089bee5b19',
                    },
                });
            }
        }
    }, [
        iscreateChannelAddUsersLoading,
        createChannelAddUsersData,
        createChannelAddUsersError,
    ]);

    const acceptedOrderClickHandler = async (order: any) => {
        setOrderData(order);

        createChannelAddUsers({
            data: {
                channel_name: order.buyer_po_number,
                buyer_id: order.buyer_id,
                seller_id: order.seller_id,
                moderator_id: '' + 883,
            },
        });
    };

    const closeOrder = (i: number) => {
        setAcceptedOrders((prev: any) => {
            prev[i].is_order_closed = true;
            return prev;
        });
    };

    return (
        <div>
            <p>Accepted Orders</p>
            {isAcceptedOrdersLoading || iscreateChannelAddUsersLoading && <Loader />}
            <ul>
                {acceptedOrders?.map((order: any, i: number) => (
                    <li key={order.id}>
                        <span onClick={() => acceptedOrderClickHandler(order)}>
                            {order.buyer_po_number}
                        </span>
                        {userData?.type === userRole.buyerUser &&
                            !order?.is_order_closed && (
                                <button onClick={() => closeOrder(i)}>
                                    Close Order
                                </button>
                            )}
                    </li>
                ))}
            </ul>
        </div>
    );
};
