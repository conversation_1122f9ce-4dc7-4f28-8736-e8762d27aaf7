.buyerSettingInnerContent {
    padding: 12px 10px 12px 16px;
    border-radius: 0px 0px 10px 10px;
    // -webkit-backdrop-filter: blur(60px);
    // backdrop-filter: blur(60px);
    // background-color: rgba(0, 0, 0, 0.75);
    margin: 0px auto;
    max-width: 800px;
    width: 100%;

    .dFlex {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .impersonateIconStyle{
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 24px;
            color: #70ff00;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 18px;
            cursor: pointer;
            font-size: 14px;
        }
    }

    .requestReceived {
        width: 169px;
        height: 36px;
        padding: 8px 20px;
        border-radius: 50px;
        backdrop-filter: blur(20px);
        border: solid 1px #70ff00;
        background-color: rgba(255, 255, 255, 0.3);
        font-family: Noto Sans;
        font-size: 12px;
        line-height: 1.6;
         color: #70ff00;
         position: absolute;
         right: 0;
         top: -47px;
    }
    .requestReceivedtext{
        position: absolute;
        left: 42px;
      
    }
    .generalSettings {
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        margin-bottom: 12px;
        display: block;
    }

    .errorMessage {
        padding: 6px 20px;
        border-radius: 50px;
        border: solid 1px #ff5d47;
        background-color: #ffefed;
        font-family: Noto Sans;
        font-size: 14px;
        line-height: 1.6;
        text-align: left;
        color: #ff5d47;
        display: flex;
        gap: 4px;
    }

    .formInnerContent {
        .FormInputGroup {
            display: flex;
            margin-bottom: 8px;

            .lblInput {
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: normal;
                line-height: 1.6;
                text-align: left;
                color: #fff;
                width: 152px;
                height: 34px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                padding: 6px 4px 6px 10px;
                border: solid 0.5px #000;
                background-color: rgba(0, 0, 0, 0.25);
                border-radius: 4px 0px 0px 4px;
                border-right: 0px;

                .netTermsChk {
                    width: 100%;
                    display: flex;
                    // align-items: center;
                }

                .lblCheckbox {
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                    padding-left: 18px;
                    font-size: 12px;
                    &:focus-within{
                        .checkmark{
                            border: 1px solid #70ff00;
                        }
                    }
                    .checkmark {
                        width: 12px;
                        height: 12px;
                        border-radius: 1.7px;
                        border: solid 0.7px #3b4665;
                        background-color: #ebedf0;
                        top: 4px;

                        &::after {
                            left: 3.5px;
                            top: 1.5px;
                            width: 2px;
                            height: 5px;
                        }
                    }

                    input:checked~.checkmark {
                        background-color: #70ff00;
                        border: solid 0.7px #70ff00;
                    }

                }
            }

            .lblAdress {
                flex: 0 0 152px;
            }

            .inputSection {
                height: 34px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: flex-start;
                padding: 6px 6px 6px 10px;
                border: solid 0.5px #000;
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 0px 4px 4px 0px;
                flex: 1 auto;

                &.comanyName.comanyName{
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: left;
                    color: #fff;
                    padding: 0px;
                    .comanyNameInput1.comanyNameInput1{
                        padding: 6px 6px 6px 10px;
                      }
                  }

                  .changePassBtn{
                    display: flex;
                    align-items: center;
                    height: 100%;
                    padding: 6px 6px 6px 10px;
                    cursor: pointer;
                    
                    &.disabled {
                        cursor: not-allowed;
                        opacity: 0.5;
                        pointer-events: auto;
                    }
                  }

                &.paddingLR0 {
                    padding-left: 0px;
                    padding-right: 0px;
                }

                &.companyState {
                    flex: 0 0 72px
                }

                &.phoneNo {
                    width: 115px;
                }

                &.bdrRadius0 {
                    border-radius: 0px;
                }

                &.bdrRight0 {
                    border-right: 0px;
                }


                input {
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: left;
                    color: #fff;
                    border: 0px;
                    background-color: transparent;
                    padding: 0px;
                    width: 100%;

                    &:focus {
                        outline: none;
                        box-shadow: none;
                    }

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.5);
                        font-weight: normal;
                    }
                }
            }

            &.FormInputGroupError {
                .lblInput {
                    border: solid 0.5px #f00;
                    background-color: #f00;
                    cursor: pointer;
                    white-space: nowrap;
                }

                .borderOfError {
                    border: solid 0.5px #f00;
                }
            }

            .cityInput {
                flex: 0 0 96px;
            }

            .zipCodeInput {
                flex: 0 0 72px;
                padding: 6px 3px 6px 6px;

            }

            &.resaleCertificateSection {
                min-height: 84px;

                .lblInput {
                    width: 100%;
                    max-width: 152px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .state1 {
                        font-size: 12px;
                        padding-left: 5px;
                        display: flex;
                        align-items: center;
                     
                    }

                    .deleteCertBtn{
                        padding: 0px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        &:focus{
                            svg{
                                path{
                                    fill:#70ff00;
                                }
                        }   
                        }
                        &:hover{
                            svg{
                                    path{
                                        fill:#70ff00;
                                    }
                            }
                        }
                       
                    }

                    .viewCert {
                        font-size: 10px;
                        font-weight: 300;
                        margin-left: 3px;
                        text-decoration: none;
                        color: #70ff00;
                        transition: all 0.1s;
                        &:hover,
                        &:focus-within {
                            text-decoration: underline;
                            outline: none;
                        }
                    }
                }

                .inputSection1 {
                    border-top: solid 0.5px #000;
                    border-bottom: solid 0.5px #000;

                    svg {
                        height: 100%;
                        width: 100%;
                    }
                }

                .UploadCerTitle {
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 1.6;
                    text-align: center;
                    color: #fff;
                    margin-bottom: 3px;
                }

                .inputSection2 {
                    width: 99px;
                    height: 100%;
                    padding: 6px 8px;
                    display: flex;
                    align-items: center;
                    flex-direction: column;
                    border-top: solid 0.5px #000;
                    border-bottom: solid 0.5px #000;
                    border-left: solid 0.5px #000;
                    border-right: solid 0.5px #000;
                    background-color: rgba(255, 255, 255, 0.2);

                    .uploadText {
                        font-family: Noto Sans;
                        font-size: 10px;
                        font-weight: 300;
                        line-height: 1.6;
                        text-align: center;
                        color: #fff;
                        margin-bottom: 3px;
                        cursor: pointer;
                        transition: all 0.1s;
                        &:hover{
                            color: #70ff00;
                        }
                        &:focus-within{
                            color: #70ff00;
                        }
                    }

                    input {
                        display: none;
                    }

                }

                .inputSection3 {
                    width: 84px;
                    height: 100%;
                    flex-grow: 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 6px 10px;
                    border-top: solid 0.5px #000;
                    border-bottom: solid 0.5px #000;
                    background-color: rgba(255, 255, 255, 0.2);
                }

                .inputSection4 {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    border-radius: 0px;
                    border-right: 0px;
                    padding-right: 10px;
                    flex:0 144px;

                    .inputSectionDate {
                        display: flex;
                        width: 100%;

                        input {
                            width: 76px;
                            height: 20px;
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-start;
                            align-items: center;
                            padding: 2px 4px;
                            border-radius: 2px;
                            background-color: rgba(255, 255, 255, 0.2);
                            font-size: 10px;
                        }
                    }
                }


                .inputSection5 {
                    width: 89px;
                    height: 100%;
                    justify-content: center;
                    flex-direction: column;
                    align-items: center;

                    .UploadCertStatus {
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: 300;
                        font-stretch: normal;
                        line-height: 1.6;
                        text-align: center;
                        color: #fff;
                        height: 23px;
                    }

                    .inputSection {
                        background-color: rgba(255, 255, 255, 0.2);
                    }
                }
                .resaleFirstTopDiv{
                    border-radius: 4px 0px 0px 0px;
                    border-bottom: none;
                    height: 50px;
                }
                .resaleMiddleTopDiv{
                    border-bottom: none;
                    height: 50px;
                }
                .resaleLastTopDiv{
                    border-bottom: none;
                    height: 50px;
                    border-radius: 0px 4px 0px 0px;
                    display: unset;
                }
                .resaleFirstBottomDiv{
                    border-radius: 0px 0px 0px 0px;
                    border-top: none;
                    height: 32px;
                }
                .resaleLastBottomDiv{
                    border-top: none;
                    height: 32px;
                    border-radius: 0px 0px 0px 0px;
                }
                .resaleMiddleBottomDiv{
                    border-top: none;
                    height: 32px;
                }
                .delIconCertif{
                    flex-direction: unset;
                    align-items: center;
                    padding-left: 6px;
                }
                .deleteCertLine{
                            .lblInput {
                                .state1AddLine {
                                    padding-left: 20px;
                                }
                            }
                        
                    
                    &:last-child{
                        .resaleFirstBottomDiv{
                            border-radius: 0px 0px 0px 4px;
                        }
                        .resaleLastBottomDiv{
                            border-radius: 0px 0px 4px 0px;
                        }
                    }
                }
            }

            .deleteCertLineBtn{
                svg{
                    width: 14px;
                    height: 14px;
                    circle,line{
                        stroke: #ff0000;
                        fill: transparent;
                    }
                }
            }

            &.recevingHours {
                height: 81px;

                .lblInput {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    border-right: solid 1px #000;

                    .lblReceivingHours {
                        font-size: 12px;
                        padding-left: 6px;
                    }
                }

                .inputSectionRecevingHours {
                    width: 58.4px;
                    height: 100%;
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 4px 10px;
                    border: solid 0.5px #000;
                    border-left: 0px;
                    background-color: rgba(255, 255, 255, 0.2);
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 1.6;
                    text-align: center;
                    color: #fff;

                    label {
                        cursor: pointer;

                        &:hover {
                            color: #70ff00;
                        }
                    }

                    input[type=checkbox] {
                        display: none;
                    }

                    .daylbl1 {
                        color: #70ff00;
                    }

                    .daylbl2,
                    .daylbl3 {
                        font-size: 12px;
                    }

                    &:last-child {
                        border-radius: 0px 4px 4px 0px;
                    }
                }
            }

            .desiredcreditLineInput {
                line-height: 1;
            }

            .desiredcreditLine {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: normal;
                text-align: left;
                color: #fff;
                width: 100%;
            }

            &.netTerms.netTerms {
                .lblInput {
                    flex: 0 0 152px;
                }

                .inputSection {
                    input {
                        font-size: 12px;
                    }

                    &:nth-child(4) {
                        flex: 0 0 144px;
                    }
                }
            }

            &.netPaymentApporvedSection.netPaymentApporvedSection {
                height: 100%;



                .lblInput {
                    height: 92px;
                    flex-direction: column;
                    align-items: flex-start;
                    padding-right: 8px;

                    .requestcreditlineIncreaseText {
                        opacity: 0.5;
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 1.4;
                        text-align: left;
                        color: #70ff00;
                        margin-top: auto;
                        cursor: pointer;
                        &:focus{
                            text-decoration: underline;
                        }
                    }

                    .requestCreditLineInput {
                        width: 100%;
                        height: 24px;
                        padding: 0 8px;
                        border-radius: 2px;
                        border: solid 0.5px #fff;
                        background-color: rgba(0, 0, 0, 0.25);
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 1.2;
                        text-align: left;
                        color: #fff;
                        margin-bottom: 4px;

                        &::placeholder {
                            color: #fff;
                        }

                        &:focus {
                            border: solid 0.5px #70ff00;
                            outline: none;
                        }
                    }

                    .cancelBtn {
                        width: 54px;
                        height: 24px;
                        display: inline-flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        padding: 0 8px;
                        border-radius: 2px;
                        background-color: rgba(255, 255, 255, 0.25);
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 1.2;
                        text-align: center;
                        color: #fff;
                        margin-right: 4px;
                        &:hover,
                        &:focus{
                            border: solid 0.5px #fff; 
                        }
                    }

                    .submitBtn {
                        width: 74px;
                        height: 24px;
                        display: inline-flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        padding: 0 8px;
                        border-radius: 2px;
                        background-color: #70ff00;
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 1.2;
                        text-align: center;
                        color: #000000;
                        border: 1px solid #70ff00;
                        &:focus{
                            border: 1px solid #fff; 
                        }

                        &:disabled {
                            opacity: 0.5;
                            cursor: not-allowed;
                            background-color: rgba(255, 255, 255, 0.2);
                            color: rgba(255, 255, 255, 0.25);
                            border: 1px solid rgba(255, 255, 255, 0.25); 
                        }
                    }
                }

                .netPaymentApporved {
                    display: flex;
                    flex-direction: column;
                    width: 100%;

                    .netPaymentApporvedCol {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 1.2;
                        text-align: left;
                        color: #fff;
                        width: 100%;

                        .pendingStatusNetTerms {
                            font-size: 10px;
                            font-weight: 300;
                            line-height: 1.2;
                            text-align: left;
                            color: #ffaea3;
                        }

                        &:first-child {
                            .inputSection {
                                border-bottom: 0px;

                                &:last-child {
                                    border-bottom-right-radius: 0px;
                                }
                            }
                        }

                        &:last-child {
                            .inputSection {
                                &:last-child {
                                    border-top-right-radius: 0px;
                                }
                            }
                        }

                        .inputSection {
                            height: 46px;
                            flex: 1;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }

                        div {
                            &:first-child {
                                font-size: 10px;
                                font-weight: 300;
                                margin-bottom: 4px;
                            }
                        }
                    }
                }
            }



            &.achCredit {
                height: 40px;
                position: relative;

                .lblInput {
                    height: 40px;
                    flex: 0 0 152px;
                    font-size: 12px;
                }

                .inputSection {
                    height: 40px;
                    flex-direction: column;

                    label {
                        font-family: Noto Sans;
                        font-size: 10px;
                        font-weight: 300;
                        line-height: 1.2;
                        text-align: left;
                        color: #fff;
                    }

                    input {
                        font-size: 12px;
                    }
                }

                .accountNo {
                    flex: 0 0 104px;
                }

                .accountNoPending {
                    flex: 0 0 90px;
                }

                .showNo {
                    display: none;
                }

                .textHoverMask {
                    font-family: Noto Sans;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 1.2;
                    text-align: left;
                    color: #fff;
                    margin-top: 2px;

                    &:hover {
                        .maskText {
                            display: none;
                        }

                        .showNo {
                            display: block;
                        }
                    }

                }
            }

            .btnApplyMain {
                padding: 0px;
                background-color: transparent;

                .applyBtn {
                    width: 80px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    padding: 3px 10px;
                    border: solid 0.5px #000;
                    background-color: rgba(0, 0, 0, 0.25);
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 1.6;
                    text-align: left;
                    color: #fff;
                    border-radius: 0px 4px 4px 0px;
                    border: 0px;
                    cursor: pointer;
                    &:focus{
                        color: #70ff00;
                    }
                }
            }

            .pendingBtn {
                flex: 0 0 auto;

                .applyBtn {
                    color: #ff5d47;
                }
            }

        }

        .myReportsTitle {
            font-family: Noto Sans;
            font-size: 20px;
            font-weight: bold;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            margin-top: 8px;
            margin-bottom: 8px;

            span {
                font-size: 12px;
                font-weight: normal;
            }
        }
        .purchaseOrderHistoryText {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            margin-bottom: 4px;
            &:hover,
            &:focus {
                color: #70ff00;
            }
        }
    }

}

.btnSection {
    display: grid;
    grid: 20% auto 20%;
    grid-template-columns: 20% 60% 20%;
    border-top: 1px solid #000;
    align-items: center;
    padding: 8px 6px 0px 0px;
    margin-top: 19px;

    .termsAndPatent {
        position: relative;
        text-align: center;
        .TermsandConditions {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            cursor: pointer;
            &:focus{
                text-decoration: underline;
            }
        }

        .patentPendingText {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.6;
            text-align: center;
            color: #fff;
        }
        .version{
            color: #70ff00 ;
        }
    }

    .backBtn {
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;
        border: 0px;
        outline: none;
        background-color: transparent;
        text-align: left;
        cursor: pointer;
        transition: all 0.1s;
        &:not(:disabled){
            &:hover,
            &:focus {
                color: #b3b3b3;
            }
        }
       
    }

    .saveBtn {
        margin-left: auto;

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }

    .btnRight {
        text-align: right;
    }
}

.Dropdownpaper.Dropdownpaper {
    padding: 3px 4px 8px 12px;
    -webkit-backdrop-filter: blur(30px);
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
    margin-top: 7px;
    overflow: hidden;
    width: 72px;
    border-radius: 0px 0px 4px 4px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    &.DropdownDeliveryDate {
        margin-top: 8px;

        ul{
            li{
                &:first-child{
                    background-color: transparent;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 1.57;
                    padding-left: 0px;
                    position: unset;
                    &:hover {
                        background-color: transparent;
                        color: #fff;
                    }
                }
            }
        }
    }

    ul {
        overflow: auto;
        max-height: 230px;
        padding-right: 4px;
        padding-top: 0px;
        padding-bottom: 0px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        li {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            border-radius: 2px;
            padding: 3px 8px;
            margin-bottom: 2px;

    
            &:hover {
                background-color: #fff;
                color: #000;
            }

            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
                border-radius: 2px;
            }
        }
    }
}

.SubmitApp {
    .dialogContent {
        max-width: 349px;
        width: 100%;
        height: 414px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 40px 22px 12px 22px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }

        .hereLink {
            font-weight: bold;
            color: #70ff00;
            cursor: pointer;
        }

        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 40px;
            transition: all 0.1s;

            &:hover,
            &:focus {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }


    }

}

.successPopup {
    .dialogContent {
        height: 419px;
        padding: 36px;
        line-height: 1.4;

        .successPopupTitle {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            text-align: center;
            color: #70ff00;
        }
        p{
            margin-bottom: 10px;
        }
    }

}

.backToHomePopup {
    .dialogContent {
        height: 234px;

        .bactToHomeTitle {
            font-family: Noto Sans;
            font-size: 20px;
            font-weight: 600;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            ;
        }

        .btnOfPopup {
            opacity: 0.5;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            margin-bottom: 15px;
            font-weight: normal;
            cursor: pointer;

            &:hover,
            &:focus {
                opacity: unset;
                color: #70ff00;
            }
        }
    }
}

.closeIcon {
    position: absolute;
    top: 10px;
    right: 12px;
    cursor: pointer;
    &:focus{
        svg{
            opacity: unset;
        }
    }

    svg {
        height: 20px;
        width: 20px;
        opacity: 0.5;

        path {
            fill: #fff;
        }
        &:hover{
            opacity: unset;
        }
        
    }
}

.DropdownUploadCertpaper.DropdownUploadCertpaper {
    padding: 6px;
    -webkit-backdrop-filter: blur(30px);
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
    overflow: hidden;
    border-radius: 4px 4px 0px 0px;
    width: 84px;
    margin-left: -11px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    &.Bottom.DropdownUploadCertpaperBottom {
        border-radius: 0px 0px 4px 4px;
    }

    ul {

        width: 100%;
        overflow: auto;
        max-height: 180px;
        padding-right: 3px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }


        li {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding: 4px 5px;
            justify-content: center;
            margin-bottom: 2px;

            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
                border-radius: 2px;
            }


            &:hover {
                border-radius: 2px;
                background-color: #fff;
                color: #000;
            }

        }

    }
}

.RecevingTimepaper.RecevingTimepaper {
    width: 82.50px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 6px;
    -webkit-backdrop-filter: blur(30px);
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px 4px 0px 0px;
    margin-left: -11px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    &.Expirationpaper.Expirationpaper {
        width: 144px;
        margin-left: -11px;

        ul {
            li {
                padding: 4px 5px;
            }
        }
    }

    ul {
        padding: 0px;
        width: 100%;

        li {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding: 4px 8px;
            justify-content: center;
            margin-bottom: 2px;

            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
                border-radius: 2px;
            }

            &:hover {
                border-radius: 2px;
                background-color: #fff;
                color: #000;
            }

        }

    }
}

.RecevingTimepaper1.RecevingTimepaper1 {
    width: 82.50px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 6px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 0px 0px 4px 4px;
    margin-left: -11px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    &.Expirationpaper1.Expirationpaper1 {
        width: 144px;
        margin-left: -11px;

        ul {
            li {
                padding: 4px 5px;
            }
        }
    }

    ul {
        padding: 0px;
        width: 100%;

        li {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding: 4px 8px;
            justify-content: center;
            margin-bottom: 2px;

            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
                border-radius: 2px;
            }

            &:hover {
                border-radius: 2px;
                background-color: #fff;
                color: #000;
            }
        }


    }
}

.questionIcon {
    display: flex;
    align-items: center;
    margin-left: auto;
    transition: all 0.1s;

    .questionIcon2 {
        display: none;
    }

    &:hover {
        .questionIcon1 {
            display: none;
        }

        .questionIcon2 {
            display: block;
        }
    }
}

.resaleCertTitle {
    margin-bottom: 1.5px;
    display: flex;
    width: 100%;
    align-items: center;
}

.addCertificateBtn{
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    text-align: left;
    color: #70ff00;
    margin-top: auto;
    cursor: pointer;
    display: flex;
    align-items: center;   
    justify-content: center;
    svg{
        width: 15px;
        height: 15px;
        margin-right: 3px;
    }
}

.achSetupBlank {
    position: absolute;
    z-index: 101;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff0000;
}

.achSetupBlankOverlay {
    position: absolute;
    background-color: rgb(0, 0, 0, 0.7);
    z-index: 100;
    width: 100%;
    height: 100%;
    border-radius: 6px;
}

.ErrorDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }


        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 20px;
            transition: all 0.1s;

            &:hover,
            &:focus {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }

        .deleteBtnSection{
            display: flex;
            justify-content: center;
            button{
                display: inline-flex;
                &:last-child{
                    margin-left: 16px;
                }
            }
        }


    }

}
.errorMesDiv{
    display: flex;
    align-items: center;
    position: absolute;
    width: 100%;
    justify-content: center;
    top: 7px;
}

.autocompleteDescPanel {
    border-radius: 4px;
  }
  
  .autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    background-color: #ffffff4c;
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    padding-right: 4px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 0px 0px 4px 4px;
    margin-top: 1px;
  }
  
  .listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    max-height: 316px;
    padding: 6px 4px 6px 10px;
    margin-top: 4px;
  
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  
    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 20px;
  
    }
  
    li {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      box-shadow: none;
      padding: 6px 8px;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 4px;
      border-radius: 2px;
  
      &:hover {
        background-color: #fff;
        color: #000;
      }

      &[aria-selected="true"] {
        background-color: #fff !important;
        color: #000;
      }
    }
  }

  .companyInput {
    width: 100%;
    height: 100%;
    padding: 6px 6px 6px 10px;
    color: #fff;
    resize: none;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;

    &::placeholder {
      color: #bbb;
    }

    &:focus-within {
      border: solid 1px #70ff00;
      outline: none;
      box-shadow: none;
      border-radius: 0px 2px 2px 0px;
    }
  }

  .borderOfError {
    border: solid 0.5px #f00;
}

.disabledCert{
    span{
        pointer-events: none;
        opacity: 0.5;
        &:nth-child(1){
           pointer-events: unset;
           opacity: unset;
        }
    }
}

.changePassDialog{
    .dialogContent{
        width: 520px;
        height: auto;
        max-height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-end;
        margin: 26px 24px 29px;
        padding: 0;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
    }
}

.blurBg{
    filter: blur(4px);
}