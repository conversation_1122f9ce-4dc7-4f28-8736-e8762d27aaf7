import * as yup from "yup";

export const createPoSchema = yup.object().shape({
  poJobName: yup.string().trim().required('Po Job Name is not valid'),
    descriptionLines: yup
      .array()
      .of(
        yup
          .object()
          .shape({
            descriptionObj: yup.object().required(),
            qtyVal: yup.string().required() ,
            qtyUnit: yup.string().required(),
            umVal: yup.string(),
            umUnit:yup.string().required(),
            totalVal:yup.string(),
            partNumber: yup.string().default(""),
            domesticMaterialOnly: yup.boolean().nullable().oneOf([true, false, null]).default(null),
            sessionId: yup.string().default("").defined(),
          }) 
      ),
      materialTotal: yup.number(),
      deliveryDate:yup.string().required(),
      totalPurchase:yup.number(),
      depositAmount: yup.number(),
      selectedOptionPayment:yup.string().required(),
      deliverTo: yup.object().shape({
        line1: yup.string().trim().required('Address is not valid'),
        city: yup.string().trim().required('City is not valid'),
        state_id: yup.number().required('State is not valid'),
        zip: yup.string().trim().required('Zip is not valid'),
      }),
      
  });