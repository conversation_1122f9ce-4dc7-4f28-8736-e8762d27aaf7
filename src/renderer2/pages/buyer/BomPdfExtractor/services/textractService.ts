import axios from 'axios';
import { API_CONFIG } from '../config';
import { TextractResult } from '../types/PdfExtractorTypes';
import { v4 as uuidv4 } from 'uuid';

interface UploadResponse {
  s3Url: string;
  [key: string]: any;
}

/**
 * Service for handling AWS Textract operations
 */
class TextractService {
  /**
   * Upload a PDF file to S3 via the backend API
   * @param {File} pdfFile - The PDF file to upload
   * @returns {Promise<UploadResponse>} - Response with S3 URL
   */
  async uploadPdf(pdfFile: File): Promise<UploadResponse> {
    try {
      //getSignedurl pay load
      const extension = pdfFile.name.split('.').pop();
      const signedUrlPayload = {
        data: {
          "bucket_name": import.meta.env.VITE_S3_UPLOAD_BOM_BUCKET_NAME,
          "object_key": import.meta.env.VITE_ENVIRONMENT + '/bom/' + 'BOM-' + uuidv4() + '.' + extension,
          "expire_time": 3000
        }
      };
        const signedUrlResponse = await axios.post(`${import.meta.env.VITE_API_SERVICE}/user/get_signed_url`, signedUrlPayload);
        const signedUrl = signedUrlResponse.data.data;

        const uploadResponse = await axios.put(signedUrl, pdfFile, {
            headers: {
                'Content-Type': pdfFile.type,
            }
        });

     

      
    } catch (error) {
      throw error;
    }
  }

  /**
   * Process a PDF file with AWS Textract
   * @param {string} s3Url - The S3 URL of the PDF file
   * @returns {Promise<TextractResult>} - Textract analysis results
   */
  async processPdf(s3Url: string): Promise<TextractResult> {
    try {
      // Process the uploaded file with Textract
      const response = await fetch(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.processPdf}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ s3Url }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Processing failed: ${response.statusText}`);
      }
      const data = await response.json();
      return data;
      
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upload and process a PDF file in one operation
   * @param {File} pdfFile - The PDF file to upload and process
   * @returns {textractData: TextractResult, s3Url: string} - Textract analysis results
   */
  async uploadAndProcessPdf(pdfFile: File): Promise<{textractData: TextractResult, s3Url: string}> {
    try {
      // Step 1: Upload the PDF file
      const uploadData = await this.uploadPdf(pdfFile);

      // Step 2: Process the uploaded file with Textract
      //const textractData = await this.processPdf(uploadData.s3Url);
      //return {textractData:textractData, s3Url: uploadData.s3Url };
    } catch (error) {
      throw error;
    }
  }
}

// Create and export a singleton instance
const textractService = new TextractService();
export default textractService; 