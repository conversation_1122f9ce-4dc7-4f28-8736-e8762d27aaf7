import clsx from "clsx";
import React, { useEffect, useState } from "react";
import { useBomPdfExtractorStore } from "../BomPdfExtractorStore";
import styles from "../styles/BomExtractor.module.scss";
import Slider from "./Slider";
import { useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library";
import { useNavigate } from "react-router-dom";
import { routes } from "src/renderer2/common";
import { useRef } from "react";

const LeftToolBar = () => {
    const {pdfFile, setPdfFile, setDoClearAll, setDoUndoBox, setDoResetAll, setExtractText, domesticOnly,
        setDomesticOnly, setAutoSelectColumns, autoSelectColumns, textractRBushInitialized, resetBomPdfExtractorStore,
        gridOpacity, setGridOpacity, isImageBasedPdf, allBoxes, hasBoxes, setHasBoxes} = useBomPdfExtractorStore();
    const { backNavigation } = useGlobalStore();
    const {setCreatePoData , uploadBomInitialData} = useCreatePoStore()
    const navigate = useNavigate();
    const fileInputRef = useRef(null); // Add ref for file input

   //const [hasBoxes, setHasBoxes] = useState(false);

    const handleCancel = () => {
        setCreatePoData(uploadBomInitialData)
        navigate(routes.createPoPage , {state: {from: 'bomExtractor'}})
    }

    useEffect (() => {
        if(!allBoxes) {
            setHasBoxes(false);    
            return;
        }

        let hasBoxesNewValue = false;
        allBoxes.forEach(pageBoxes=>{
            hasBoxesNewValue = hasBoxesNewValue || pageBoxes?.length>0;
        });
        setHasBoxes(hasBoxesNewValue);
    }, [allBoxes]);

    
    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            if (!file) return;

            if (!file.type.includes('pdf')) {
              const errorMsg = 'Please select a PDF file.';
              return;
            }
            setDoResetAll(true);
        
            // setShowLoader(true);
            navigate(routes.bomExtractor, { state: { file } });

            // Handle the file upload logic here
            // You can navigate with the file data or process it directly
            // navigate('/bom-upload', { state: { file } });
        }
    };

    const handleUploadClick = () => {
            // Trigger file input dialog
            fileInputRef.current?.click();

    };
    return (
        <div className={styles.leftToolBar}>
            { pdfFile &&
                <>
                    <button className={styles.uploadButton} onClick={handleUploadClick}>UPLOAD BOM</button>
                    <input
                                                            type="file"
                                                            ref={fileInputRef}
                                                            onChange={handleFileUpload}
                                                            accept=".pdf,.doc,.docx,.xlsx,.xls"
                                                            style={{ display: 'none' }}
                                                        />

                    <div className={styles.actionsContainer}>
                    <label className={styles.actionLabel}>ACTIONS UNITS</label>
                    <button className={styles.filterBtn} onClick={() => setDoUndoBox(true)} disabled={!hasBoxes}>UNDO</button>
                    <button className={styles.filterBtn} onClick={() => setDoClearAll(true)} disabled={!hasBoxes}>CLEAR</button>
                    <button className={styles.filterBtn} onClick={() => setDoResetAll(true)} disabled={!hasBoxes}>RESET</button>
                    <button 
                        className={clsx(styles.filterBtn, (textractRBushInitialized && autoSelectColumns) && isImageBasedPdf && styles.activeBtn)} 
                        disabled={!textractRBushInitialized || !isImageBasedPdf} 
                        onClick={() => {
                            setAutoSelectColumns(!autoSelectColumns)
                        }}>Auto Select Column</button>
                    {/* <button className={styles.filterBtn} onClick={() => setExtractText(true)}>EXTRACT</button> */}
                    </div>

                    <div className={styles.actionsContainer}>
                        <label className={styles.actionLabel}>DOMESTIC REQUIRED</label>
                        <button className={clsx(styles.filterBtn, domesticOnly && styles.activeBtn)} onClick={() => setDomesticOnly(true)}>APPLY ALL</button>
                        <button className={clsx(styles.filterBtn, !domesticOnly && styles.activeBtn)} onClick={() => setDomesticOnly(false)}>NO</button>
                    </div>

                    <Slider
                        min={0}
                        max={100}
                        value={gridOpacity*100}
                        onChange={(value)=>{setGridOpacity(value/100)}}
                        label="GRID Opacity"
                    />
                </>
            }   
            <button className={styles.cancelBtn} onClick={handleCancel}>CANCEL</button>

        </div>
    )
}

export default LeftToolBar;
