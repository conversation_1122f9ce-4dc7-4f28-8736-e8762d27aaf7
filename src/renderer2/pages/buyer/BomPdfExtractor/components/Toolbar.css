.main-toolbar {
    flex: 1;
    padding: 10px;
    background-color: #f8f9fa;
  }
  
  .box-type-section {
    position: relative;
    margin-bottom: 15px;
  }
  
  .box-type-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
  }
  
  .custom-column-creator-container {
    position: relative;
    z-index: 1000;
  }
  
  .box-type-button {
    padding: 6px 12px;
    border: 2px solid;
    border-radius: 4px;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
  }
  
  .box-type-button:hover:not(:disabled) {
    opacity: 0.8;
  }
  
  .box-type-button.selected {
    font-weight: bold;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
  }
  
  .box-type-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .create-custom-button {
    padding: 6px 12px;
    border: 2px solid #000;
    border-radius: 4px;
    background-color: #000;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: bold;
  }
  
  .create-custom-button:hover:not(:disabled) {
    opacity: 0.8;
  }
  
  .create-custom-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .settings-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
  }
  
  .settings-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
  }
  
  .extraction-controls {
    display: flex;
    gap: 10px;
  }
  
  .extract-button, .export-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .extract-button {
    background-color: #007bff;
    color: white;
  }
  
  .export-button {
    background-color: #28a745;
    color: white;
  }
  
  .extract-button:hover:not(:disabled), .export-button:hover:not(:disabled) {
    opacity: 0.9;
  }
  
  .extract-button:disabled, .export-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .main-toolbar {
    border: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
    margin-bottom: 0;
    width: 100%;
  }
  
  @media (max-width: 768px) {
    .extraction-controls {
      flex-direction: column;
    }
  }
  