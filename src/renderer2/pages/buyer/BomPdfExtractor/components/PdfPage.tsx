import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import { ADD_BOX, DELETE_BOX, RESIZE_BOX, useBomPdfExtractorStore } from '../BomPdfExtractorStore';
import { v4 as uuidv4 } from 'uuid';
import styles from "../styles/BomExtractor.module.scss";

import {
    BOX_TYPES,
    getBoxStyles,
    createEmptyDataArrays,
  } from '../config/boxTypes';
import clsx from "clsx";
import { timeStamp } from "console";
import { createActions } from "../utils/BOMUploadUtils";

  const logPage = (...args) => {
  };

  const logRender = (...args) => {
  };
  const logError = (...args) => {
  };
  const logWarning = (...args) => {
  };
  const logBox = (...args) => {
  };
  const logExtract = (...args) => {
  };
  const logPdf = (...args) => {
  };
const GRID_SIZE = 6;

const PdfPage = ({ page, scale, fineRotations,
                    parentShowMagnifyingGlass, snapToGrid, gridOpacity,
                    textractRBush, overlapPercent, index, onMount },ref) => {
    const canvasRef = useRef(null);
    const overlayCanvasRef = useRef(null);
    const hiResCanvasRef = useRef(null);
    const magnifyingGlassCanvasRef = useRef(null);
    const renderTaskRef = useRef(null);
    const pdfContainerRef = useRef(null);

  const [boxes, setBoxes] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null); // 'tl', 'tr', 'bl', 'br', 'left', 'right', 'top', 'bottom'
  const [resizingBoxId, setResizingBoxId] = useState(null);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [currentRect, setCurrentRect] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [hoveredBox, setHoveredBox] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [showMagnifyingGlass, setShowMagnifyingGlass] = useState(false);
  const [magnifyingGlassPosition, setMagnifyingGlassPosition] = useState({ x: 0, y: 0 });

  const [pdfRenderKey, setPdfRenderKey] = useState(0);
  // Use the box styles from our configuration
  const boxStyles = getBoxStyles();

  const [extractedData, setExtractedData] = useState({});
  const [allExtractedData, setAllExtractedData] = useState({});
  //const [allBoxes, setAllBoxes] = useState({});
  //const [processedBoxIds, setProcessedBoxIds] = useState(new Set());
  //const [processingBoxIds, setProcessingBoxIds] = useState(new Set());

  // Store raw box data with position information for all pages
  const [rawBoxData, setRawBoxData] = useState({});
  // Use a ref to track the latest raw box data for use in setTimeout callbacks
  const rawBoxDataRef = useRef({});

  const [pdfJSPage, setPdfJSPage] = useState(null);
  const { currentBoxType, zoomPercentage, setAllBoxes, allBoxes, sourceCanvasRefs, autoSelectColumns, setBoxDrawing, boxDrawing,
     isImageBasedPdf, pageRotations, setdoAutoScroll, setGridRowData } = useBomPdfExtractorStore();


  useImperativeHandle(ref, () => ({
    getBoxes: () => boxes,
    getCanvasSize: () => ({
      width: canvasRef.current?.width || 0,
      height: canvasRef.current?.height || 0,
    }),
    deleteBox:(boxToDelete)=>{
      setBoxes((prev) => prev.filter(box => box.id !== boxToDelete.id));
    },
    resizeBox:(boxToResize)=>{
      setBoxes((prev) => prev.map(box =>
        box.id === boxToResize.id ? {...box, rect:{...boxToResize.rect}} : box
      ));
    },
    addBox:(boxToAdd)=>{
      setBoxes((prev) => [...prev, {...boxToAdd, rect:{...boxToAdd.rect}, timeStamp:Date.now() }]);
    },
    clearBoxes:()=>{
      boxes.forEach(box=>{
        createActions(DELETE_BOX, {...box, rect:{...box.rect}, pageIndex:index},{...box, rect:{...box.rect}, pageIndex:index});
      })
      setBoxes([]);
    },
    setPageBoxes: (pageBoxes)=>{
      if(pageBoxes && pageBoxes.length>0){
        setBoxes(pageBoxes);
      } 
    }
  }));

  
  useEffect(() => {
    const newBoxes = [...allBoxes];
    newBoxes[index] = [...boxes];
    setAllBoxes(newBoxes)
    
    //If a box is added or removed clear grid data. 
    setGridRowData([])

  }, [boxes]);

  useEffect(() => {
    sourceCanvasRefs[index] = overlayCanvasRef.current;
  }, [overlayCanvasRef.current]);

  useEffect(()=>{
    if(onMount) onMount(index);
  },[]);

  useEffect(() => {

  }, [zoomPercentage]);


    // Render PDF page onto canvas
  useEffect(() => {
    const renderPage = async () => {
      // Cancel any ongoing render task
      if (renderTaskRef.current) {
        try {
          renderTaskRef.current.cancel();
        } catch (error) {
          // Error cancelling previous render task
        }
        renderTaskRef.current = null;
      }

      // Clear canvas if no PDF file
      if (!page) {
        const canvas = canvasRef.current;
        if (!canvas) return;

        // Ensure canvas has dimensions before clearing
        if (canvas.width === 0 || canvas.height === 0) {
          canvas.width = 800; // Default width
          canvas.height = 600; // Default height
        }

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Also clear overlay canvas
        const overlayCanvas = overlayCanvasRef.current;
        if (overlayCanvas) {
          overlayCanvas.width = canvas.width;
          overlayCanvas.height = canvas.height;
          const overlayCtx = overlayCanvas.getContext('2d');
          overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
        }

        return; // Exit early if no PDF
      }

      if (!canvasRef.current) return;

      // Create the hidden high-resolution canvas if it doesn't exist
      // or recreate it if it's already in the DOM (to avoid stale references)
      if (hiResCanvasRef.current) {
        try {
          document.body.removeChild(hiResCanvasRef.current);
        } catch (error) {
          // Error removing existing high-resolution canvas
        }
      }

      hiResCanvasRef.current = document.createElement('canvas');
      hiResCanvasRef.current.style.display = 'none'; // Hide the canvas
      hiResCanvasRef.current.id = 'hiResCanvas'; // Add an ID for debugging
      document.body.appendChild(hiResCanvasRef.current); // Add to DOM but hidden

      // Create or recreate the magnifying glass canvas
      if (magnifyingGlassCanvasRef.current) {
        try {
          document.body.removeChild(magnifyingGlassCanvasRef.current);
        } catch (error) {
          // Error removing existing magnifying glass canvas
        }
      }

      magnifyingGlassCanvasRef.current = document.createElement('canvas');
      magnifyingGlassCanvasRef.current.style.display = 'none'; // Hide the canvas initially
      magnifyingGlassCanvasRef.current.id = 'magnifyingGlassCanvas'; // Add an ID for debugging
      document.body.appendChild(magnifyingGlassCanvasRef.current); // Add to DOM but hidden

      try {
        //const arrayBuffer = await pdfFile.arrayBuffer();

        //const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;

        //setNumPages(page.numPages);
        //const page = await pdf.getPage(pageNumber);

        // Get the current rotation for this page
        const currentPageRotation = page.rotate + (pageRotations || 0);
        

        // Create a base viewport with scale 1 to calculate aspect ratio
        const baseViewport = page.getViewport({
          scale: 1,
          rotation: currentPageRotation,
        });

        // Calculate scale to fit the fixed height of 800px
        const FIXED_HEIGHT = 800;
        const heightScale = FIXED_HEIGHT / baseViewport.height;

        // Apply zoom percentage to the scale
        const zoomScale = heightScale * (zoomPercentage / 100);

        // Apply base rotation to viewport with fixed height scale and zoom
        const viewport = page.getViewport({
          scale: zoomScale,
          rotation: currentPageRotation,
        });

        const canvas = canvasRef.current;
        if (!canvas) {
          return;
        }

        // Create a new canvas element for rendering
        // This avoids the "Cannot use the same canvas during multiple render() operations" error
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = viewport.width;
        tempCanvas.height = viewport.height;

        // Set canvas dimensions based on viewport (which includes zoom)
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        // Set up the hidden high-resolution canvas (3x zoom for OCR quality)
        const hiResCanvas = hiResCanvasRef.current;
        const hiResScale = heightScale * 3; // 3x zoom for better OCR quality

        // Create high-resolution viewport
        const hiResViewport = page.getViewport({
          scale: hiResScale,
          rotation: currentPageRotation,
        });

        // Set high-resolution canvas dimensions
        hiResCanvas.width = hiResViewport.width;
        hiResCanvas.height = hiResViewport.height;

        // Create a temporary canvas for high-resolution rendering
        // This avoids the "Cannot use the same canvas during multiple render() operations" error
        const hiResTempCanvas = document.createElement('canvas');
        hiResTempCanvas.width = hiResViewport.width;
        hiResTempCanvas.height = hiResViewport.height;
        const hiResTempCtx = hiResTempCanvas.getContext('2d');

        // Set the width of the pdf-content div to match the canvas width
        if (pdfContainerRef.current) {
          const contentDiv =
            pdfContainerRef.current.querySelector('.pdf-content');
          if (contentDiv) {
            // Let the content div adjust to the canvas size
            contentDiv.style.width = 'auto';
          }
        }

        const tempCtx = tempCanvas.getContext('2d');

        // Store the render task in the ref so we can cancel it if needed
        const renderTask = page.render({ canvasContext: tempCtx, viewport });
        renderTaskRef.current = renderTask;

        await renderTask.promise;

        // Now render the high-resolution version
        const hiResRenderTask = page.render({
          canvasContext: hiResTempCtx,
          viewport: hiResViewport
        });

        // Wait for high-resolution rendering to complete
        await hiResRenderTask.promise;

        // Copy from temp canvas to the actual high-res canvas
        const hiResCtx = hiResCanvas.getContext('2d');
        hiResCtx.drawImage(hiResTempCanvas, 0, 0);

        // Get the skew adjustment for this page
        const fineRotation = fineRotations || 0;

        // If we have a skew adjustment, we need to calculate the new canvas size
        if (fineRotation !== 0) {
          // Calculate the bounding rectangle after rotation
          const angleInRadians = (fineRotation * Math.PI) / 180;
          const cosAngle = Math.abs(Math.cos(angleInRadians));
          const sinAngle = Math.abs(Math.sin(angleInRadians));

          // Calculate new dimensions to contain the rotated content
          const newWidth = Math.ceil(
            tempCanvas.width * cosAngle + tempCanvas.height * sinAngle
          );
          const newHeight = Math.ceil(
            tempCanvas.width * sinAngle + tempCanvas.height * cosAngle
          );

          // Resize the main canvas to fit the rotated content
          canvas.width = newWidth;
          canvas.height = newHeight;

          // Also resize the overlay canvas
          if (overlayCanvasRef.current) {
            overlayCanvasRef.current.width = newWidth;
            overlayCanvasRef.current.height = newHeight;
          }

          // Apply the rotation transformation to main canvas
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Translate to the center of the canvas
          ctx.translate(newWidth / 2, newHeight / 2);

          // Rotate around the center
          ctx.rotate(angleInRadians);

          // Draw the image centered
          ctx.drawImage(
            tempCanvas,
            -tempCanvas.width / 2,
            -tempCanvas.height / 2,
            tempCanvas.width,
            tempCanvas.height
          );

          // Reset the transformation
          ctx.setTransform(1, 0, 0, 1, 0, 0);

          // Also apply rotation to the high-resolution canvas
          if (hiResCanvasRef.current) {
            // Calculate new dimensions for high-res canvas
            const hiResNewWidth = Math.ceil(
              hiResCanvas.width * cosAngle + hiResCanvas.height * sinAngle
            );
            const hiResNewHeight = Math.ceil(
              hiResCanvas.width * sinAngle + hiResCanvas.height * cosAngle
            );

            // Create a new temporary canvas for the rotated high-res content
            const rotatedHiResTempCanvas = document.createElement('canvas');
            rotatedHiResTempCanvas.width = hiResNewWidth;
            rotatedHiResTempCanvas.height = hiResNewHeight;
            const rotatedHiResTempCtx = rotatedHiResTempCanvas.getContext('2d');

            // Apply rotation to the temporary canvas
            rotatedHiResTempCtx.clearRect(0, 0, rotatedHiResTempCanvas.width, rotatedHiResTempCanvas.height);
            rotatedHiResTempCtx.translate(hiResNewWidth / 2, hiResNewHeight / 2);
            rotatedHiResTempCtx.rotate(angleInRadians);
            rotatedHiResTempCtx.drawImage(
              hiResTempCanvas,
              -hiResTempCanvas.width / 2,
              -hiResTempCanvas.height / 2,
              hiResTempCanvas.width,
              hiResTempCanvas.height
            );
            rotatedHiResTempCtx.setTransform(1, 0, 0, 1, 0, 0);

            // Resize the high-res canvas
            hiResCanvas.width = hiResNewWidth;
            hiResCanvas.height = hiResNewHeight;

            // Copy the rotated content to the high-res canvas
            const hiResCtx = hiResCanvas.getContext('2d');
            hiResCtx.drawImage(rotatedHiResTempCanvas, 0, 0);

            // Update the magnifying glass canvas with the high-res content but at 1.5x zoom
            if (magnifyingGlassCanvasRef.current) {
              // Set magnifying glass canvas dimensions to 1.5x of the standard canvas
              const magScale = 1.5; // 1.5x zoom for magnifying glass
              magnifyingGlassCanvasRef.current.width = canvas.width * magScale;
              magnifyingGlassCanvasRef.current.height = canvas.height * magScale;

              // Draw the high-res canvas content scaled down to 1.5x
              const magCtx = magnifyingGlassCanvasRef.current.getContext('2d');
              magCtx.drawImage(
                hiResCanvas,
                0, 0, hiResCanvas.width, hiResCanvas.height,
                0, 0, magnifyingGlassCanvasRef.current.width, magnifyingGlassCanvasRef.current.height
              );
            }
          }
        } else {
          // No skew adjustment, just copy the content directly
          const ctx = canvas.getContext('2d');
          ctx.drawImage(tempCanvas, 0, 0);

          // Also copy the high-resolution content directly
          if (hiResCanvasRef.current) {
            const hiResCtx = hiResCanvas.getContext('2d');
            hiResCtx.drawImage(hiResTempCanvas, 0, 0);
            // Update the magnifying glass canvas with the high-res content but at 1.5x zoom
            if (magnifyingGlassCanvasRef.current) {
              // Set magnifying glass canvas dimensions to 1.5x of the standard canvas
              const magScale = 1.5; // 1.5x zoom for magnifying glass
              magnifyingGlassCanvasRef.current.width = canvas.width * magScale;
              magnifyingGlassCanvasRef.current.height = canvas.height * magScale;

              // Draw the high-res canvas content scaled down to 1.5x
              const magCtx = magnifyingGlassCanvasRef.current.getContext('2d');
              magCtx.drawImage(
                hiResCanvas,
                0, 0, hiResCanvas.width, hiResCanvas.height,
                0, 0, magnifyingGlassCanvasRef.current.width, magnifyingGlassCanvasRef.current.height
              );
            }
          }
        }

        // Clear the reference since rendering is complete
        renderTaskRef.current = null;

        // Increment the render key to trigger overlay redraw
        setPdfRenderKey(prev => prev + 1);
      } catch (error) {
        // Check if this is a cancelled render task
        if (error.name === 'RenderingCancelledException') {
          // Rendering was cancelled
        } else {
          console.log('Error rendering PDF. Please try again.');
        }
      }
    };

    renderPage();

    // Cleanup function to cancel any ongoing render task when the component unmounts
    // or when the dependencies change
    return () => {
      if (renderTaskRef.current) {
        try {
          renderTaskRef.current.cancel();
        } catch (error) {
          // Error cancelling render task during cleanup
        }
        renderTaskRef.current = null;
      }

      // Clean up the high-resolution canvas if it exists
      if (hiResCanvasRef.current) {
        try {
          document.body.removeChild(hiResCanvasRef.current);
          hiResCanvasRef.current = null;
        } catch (error) {
          // Error removing high-resolution canvas during cleanup
        }
      }

      // Clean up the magnifying glass canvas if it exists
      if (magnifyingGlassCanvasRef.current) {
        try {
          document.body.removeChild(magnifyingGlassCanvasRef.current);
          magnifyingGlassCanvasRef.current = null;
        } catch (error) {
          // Error removing magnifying glass canvas during cleanup
        }
      }
    };
  }, [page, scale, pageRotations, fineRotations, zoomPercentage]);

  // Draw boxes overlay
  useEffect(() => {
    const drawOverlay = () => {
      try {
        const canvas = overlayCanvasRef.current;
        const pdfCanvas = canvasRef.current;

        if (!canvas || !pdfContainerRef.current) {
          return;
        }

        // Set default dimensions if PDF canvas is not available
        if (!pdfCanvas) {
          canvas.width = 800;
          canvas.height = 600;
        } else {
          canvas.width = pdfCanvas.width;
          canvas.height = pdfCanvas.height;
        }

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw grid
        drawGrid(ctx, canvas.width, canvas.height);

        // Draw dotted lines at mouse position only when not actively drawing
        if (!isDrawing) {
          drawDottedLines(ctx, mousePosition.x, mousePosition.y, canvas.width, canvas.height);
        }

        // Draw existing boxes
        if (boxes && boxes.length > 0) {
          boxes.forEach((box) => {
            const isBoxHovered = hoveredBox === box.id;
            const isBoxResizing = resizingBoxId === box.id;
            
            drawBox(ctx, box, {
              isHovered: isBoxHovered,
              isResizing: isBoxResizing,
              showHeader: true,
              showResizeHandles: true
            });
          });
        }

        // Draw current rectangle being drawn
        if (currentRect) {
          const currentBox = { type: currentBoxType, rect: currentRect, id: 'current' };
          drawBox(ctx, currentBox, {
            isCurrentBox: true,
            showHeader: false,
            showResizeHandles: false
          });
          setBoxDrawing(boxDrawing + 1);
        } else {
          setBoxDrawing(0);
        }

        // Update the magnifying glass canvas with the overlay content
        if (magnifyingGlassCanvasRef.current && hiResCanvasRef.current) {
          const magCtx = magnifyingGlassCanvasRef.current.getContext('2d');

          // Set magnifying glass canvas dimensions to 1.5x of the standard canvas
          const magScale = 1.5;
          magnifyingGlassCanvasRef.current.width = canvas.width * magScale;
          magnifyingGlassCanvasRef.current.height = canvas.height * magScale;

          // Draw the high-res canvas content scaled down to 1.5x
          magCtx.drawImage(
            hiResCanvasRef.current,
            0, 0, hiResCanvasRef.current.width, hiResCanvasRef.current.height,
            0, 0, magnifyingGlassCanvasRef.current.width, magnifyingGlassCanvasRef.current.height
          );

          // Calculate the scale factor between magnifying glass canvas and standard canvas
          const scaleFactorX = magnifyingGlassCanvasRef.current.width / canvas.width;
          const scaleFactorY = magnifyingGlassCanvasRef.current.height / canvas.height;

          // Scale the overlay content to match the high-resolution canvas
          magCtx.save();
          magCtx.scale(scaleFactorX, scaleFactorY);

          // Draw grid
          magCtx.save();
          magCtx.strokeStyle = `rgba(143, 186, 255, 255)`;
          magCtx.lineWidth = 1 / scaleFactorX;

          for (let x = 0; x <= canvas.width; x += GRID_SIZE) {
            magCtx.beginPath();
            magCtx.moveTo(x, 0);
            magCtx.lineTo(x, canvas.height);
            magCtx.stroke();
          }

          for (let y = 0; y <= canvas.height; y += GRID_SIZE) {
            magCtx.beginPath();
            magCtx.moveTo(0, y);
            magCtx.lineTo(canvas.width, y);
            magCtx.stroke();
          }
          magCtx.restore();

          // Draw dotted lines at mouse position only when not actively drawing
          if (!isDrawing) {
            drawDottedLines(magCtx, mousePosition.x, mousePosition.y, canvas.width, canvas.height);
          }

          // Draw boxes at high resolution
          if (boxes && boxes.length > 0) {
            boxes.forEach((box) => {
              const isBoxHovered = hoveredBox === box.id;
              const isBoxResizing = resizingBoxId === box.id;
              
              drawBox(magCtx, box, {
                isHovered: isBoxHovered,
                isResizing: isBoxResizing,
                showHeader: false,
                showResizeHandles: true
              });
            });
          }

          // Draw current rectangle being drawn
          if (currentRect) {
            const currentBox = { type: currentBoxType, rect: currentRect, id: 'current' };
            drawBox(magCtx, currentBox, {
              isCurrentBox: true,
              showHeader: false,
              showResizeHandles: false
            });
          }

          magCtx.restore();
        }

      } catch (error) {
        console.error('Error drawing overlay:', error);
      }
    };

    drawOverlay();
  }, [
    boxes,
    currentRect,
    scale,
    currentBoxType,
    snapToGrid,
    gridOpacity,
    fineRotations,
    mousePosition,
    pdfRenderKey,
    zoomPercentage,
  ]);

  function drawHeaderBG(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x, y + height);
    ctx.lineTo(x, y + radius);
    ctx.arcTo(x, y, x + radius, y, radius);
    ctx.lineTo(x + width - radius, y);
    ctx.arcTo(x + width, y, x + width, y + radius, radius);
    ctx.lineTo(x + width, y + height);
    ctx.closePath();
    ctx.fill(); // or ctx.stroke();
  }

  function drawHeaderBGStroke(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x, y + height);
    ctx.lineTo(x, y + radius);
    ctx.arcTo(x, y, x + radius, y, radius);
    ctx.lineTo(x + width - radius, y);
    ctx.arcTo(x + width, y, x + width, y + radius, radius);
    ctx.lineTo(x + width, y + height);
    ctx.closePath();
    ctx.stroke();
  }

  // Function to draw grid on the overlay canvas
  const drawGrid = (ctx, canvasWidth, canvasHeight) => {
    // Always draw grid (snapToGrid is always true now)

    ctx.save();
    ctx.strokeStyle = `rgba(143, 186, 255, ${gridOpacity})`; // Light grey with configurable opacity
    ctx.lineWidth = 0.5;

    // Draw vertical lines every 6 pixels
    for (let x = 0; x <= canvasWidth; x += GRID_SIZE) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvasHeight);
      ctx.stroke();
    }

    // Draw horizontal lines every 6 pixels
    for (let y = 0; y <= canvasHeight; y += GRID_SIZE) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvasWidth, y);
      ctx.stroke();
    }

    ctx.restore();
  };

  // Function to draw dotted lines at mouse position
  const drawDottedLines = (ctx, mouseX, mouseY, canvasWidth, canvasHeight) => {
    ctx.save();

    // Set dotted line style
    ctx.strokeStyle = 'rgba(0, 120, 215, 0.8)'; // Blue, semi-transparent
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 3]); // 5px dash, 3px gap

    // Draw vertical line from top to bottom
    ctx.beginPath();
    ctx.moveTo(mouseX, 0);
    ctx.lineTo(mouseX, canvasHeight);
    ctx.stroke();

    // Draw horizontal line from left to right
    ctx.beginPath();
    ctx.moveTo(0, mouseY);
    ctx.lineTo(canvasWidth, mouseY);
    ctx.stroke();

    ctx.restore();
  };

  // Function to draw extended box edges to the canvas boundaries
  const drawExtendedBoxEdges = (ctx, boxX, boxY, boxWidth, boxHeight, canvasWidth, canvasHeight) => {
    ctx.save();

    // Set dashed line style
    ctx.strokeStyle = 'rgba(255, 165, 0, 0.7)'; // Orange, semi-transparent
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 3]); // 5px dash, 3px gap

    // Extend top edge to left and right edges of canvas
    ctx.beginPath();
    ctx.moveTo(0, boxY);
    ctx.lineTo(boxX, boxY); // Draw to left edge of box
    ctx.moveTo(boxX + boxWidth, boxY); // Move to right edge of box
    ctx.lineTo(canvasWidth, boxY); // Draw to right edge of canvas
    ctx.stroke();

    // Extend bottom edge to left and right edges of canvas
    ctx.beginPath();
    ctx.moveTo(0, boxY + boxHeight);
    ctx.lineTo(boxX, boxY + boxHeight); // Draw to left edge of box
    ctx.moveTo(boxX + boxWidth, boxY + boxHeight); // Move to right edge of box
    ctx.lineTo(canvasWidth, boxY + boxHeight); // Draw to right edge of canvas
    ctx.stroke();

    // Extend left edge to top and bottom edges of canvas
    ctx.beginPath();
    ctx.moveTo(boxX, 0);
    ctx.lineTo(boxX, boxY); // Draw to top edge of box
    ctx.moveTo(boxX, boxY + boxHeight); // Move to bottom edge of box
    ctx.lineTo(boxX, canvasHeight); // Draw to bottom edge of canvas
    ctx.stroke();

    // Extend right edge to top and bottom edges of canvas
    ctx.beginPath();
    ctx.moveTo(boxX + boxWidth, 0);
    ctx.lineTo(boxX + boxWidth, boxY); // Draw to top edge of box
    ctx.moveTo(boxX + boxWidth, boxY + boxHeight); // Move to bottom edge of box
    ctx.lineTo(boxX + boxWidth, canvasHeight); // Draw to bottom edge of canvas
    ctx.stroke();

    ctx.restore();
  };

  const toCanvasCoords = (e) => {
    const container = pdfContainerRef.current;

    // Get the content div that contains the canvas
    const contentDiv = container.querySelector('.pdf-content');
    if (!contentDiv) {
      console.error('PDF content div not found');
      return { x: 0, y: 0 };
    }

    // Get the content div's bounding rectangle
    const contentRect = contentDiv.getBoundingClientRect();

    // Account for scrolling in both directions
    const scrollLeft = container.scrollLeft;
    const scrollTop = container.scrollTop;

    // Calculate coordinates relative to the canvas
    return {
      x: e.clientX - contentRect.left + scrollLeft,
      y: e.clientY - contentRect.top + scrollTop,
    };
  };

  // Convert canvas coordinates to normalized coordinates (0-1 range)
  const toNormalizedCoords = (canvasCoords) => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return { x: 0, y: 0 };
    }

    return {
      x: canvasCoords.x / canvas.width,
      y: canvasCoords.y / canvas.height,
    };
  };

  // Convert normalized coordinates to canvas coordinates
  const normalizedToCanvas = (normalizedCoords) => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return { x: 0, y: 0 };
    }

    return {
      x: normalizedCoords.x * canvas.width,
      y: normalizedCoords.y * canvas.height,
    };
  };

  // Convert normalized rect to canvas rect
  const normalizedToCanvasRect = (normalizedRect) => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    return {
      x: normalizedRect.x * canvas.width,
      y: normalizedRect.y * canvas.height,
      width: normalizedRect.width * canvas.width,
      height: normalizedRect.height * canvas.height,
    };
  };

  // Function to snap a value to the grid
  const snapToGridValue = (value) => {
    if (!snapToGrid) return value;
    return Math.round(value / GRID_SIZE) * GRID_SIZE;
  };

  // Function to check if a point is inside a circle
  const isPointInCircle = (x, y, cx, cy, radius) => {
    const dx = x - cx;
    const dy = y - cy;
    return dx * dx + dy * dy <= radius * radius;
  };

  // Function to check if a point is inside a rectangle
  const isPointInRect = (x, y, rx, ry, rw, rh) => {
    return x >= rx && x <= rx + rw && y >= ry && y <= ry + rh;
  };

  const isPointInBox = (canvasX, canvasY, normalizedRect) => {
    // Convert canvas coordinates to normalized coordinates
    const normalizedPoint = toNormalizedCoords({ x: canvasX, y: canvasY });

    return normalizedPoint.x >= normalizedRect.x &&
           normalizedPoint.x <= normalizedRect.x + normalizedRect.width &&
           normalizedPoint.y >= normalizedRect.y &&
           normalizedPoint.y <= normalizedRect.y + normalizedRect.height;
  };

  // Function to check if mouse is over a resize handle or delete button
  const getResizeHandleAtPoint = (canvasX, canvasY, box) => {
    if (!box || !box.rect) return null;

    const { rect } = box;
    const handleRadius = 4; // Radius of corner circles (reduced by half)
    const edgeHandleWidth = 20; // Doubled from 10px
    const edgeHandleHeight = 8; // Doubled from 4px

    // Convert normalized rect to canvas coordinates
    const canvasRect = normalizedToCanvasRect(rect);
    const boxX = canvasRect.x;
    const boxY = canvasRect.y;
    const boxWidth = canvasRect.width;
    const boxHeight = canvasRect.height;

    // Check if point is in delete button (top-right corner)
    const deleteButtonSize = 16;
    const padding = 4;
    const deleteButtonX = boxX + boxWidth - deleteButtonSize/2 - padding;
    const deleteButtonY = boxY + deleteButtonSize/2 + padding;

    if (isPointInCircle(canvasX, canvasY, deleteButtonX, deleteButtonY, deleteButtonSize/2)) {
      return 'delete';
    }

    // Check corner circles (top-left, top-right, bottom-left, bottom-right)
    if (isPointInCircle(canvasX, canvasY, boxX, boxY, handleRadius)) {
      return 'tl'; // top-left
    }
    if (isPointInCircle(canvasX, canvasY, boxX + boxWidth, boxY, handleRadius)) {
      return 'tr'; // top-right
    }
    if (isPointInCircle(canvasX, canvasY, boxX, boxY + boxHeight, handleRadius)) {
      return 'bl'; // bottom-left
    }
    if (isPointInCircle(canvasX, canvasY, boxX + boxWidth, boxY + boxHeight, handleRadius)) {
      return 'br'; // bottom-right
    }

    // Check edge handles (middle of each edge)
    // Left edge
    if (isPointInRect(
      canvasX, canvasY,
      boxX - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    )) {
      return 'left';
    }

    // Right edge
    if (isPointInRect(
      canvasX, canvasY,
      boxX + boxWidth - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    )) {
      return 'right';
    }

    // Top edge
    if (isPointInRect(
      canvasX, canvasY,
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    )) {
      return 'top';
    }

    // Bottom edge
    if (isPointInRect(
      canvasX, canvasY,
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY + boxHeight - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    )) {
      return 'bottom';
    }

    return null;
  };

  const drawBox = (ctx, box, options = {}) => {
    const { 
      isHovered = false, 
      isResizing = false, 
      isCurrentBox = false,
      showHeader = true,
      showResizeHandles = true 
    } = options;

    const { type, rect, id } = box;
    if (!rect) return;

    const style = boxStyles[type] || boxStyles['Description'];
    const canvasRect = normalizedToCanvasRect(rect);
    const headerHeight = 20;

    // Draw box fill and border
    ctx.fillStyle = style.fill;
    ctx.fillRect(canvasRect.x, canvasRect.y, canvasRect.width, canvasRect.height);
    
    ctx.strokeStyle = style.stroke;
    ctx.lineWidth = 2;
    ctx.strokeRect(canvasRect.x, canvasRect.y, canvasRect.width, canvasRect.height);

    // Draw header if needed
    if (showHeader) {
      ctx.fillStyle = style.stroke;
      drawHeaderBG(ctx, canvasRect.x, canvasRect.y - headerHeight, canvasRect.width, headerHeight, 5);
      drawHeaderBGStroke(ctx, canvasRect.x, canvasRect.y - headerHeight, canvasRect.width, headerHeight, 5);
      
      ctx.fillStyle = style?.textColor || 'rgba(255, 255, 255, 1)';
      ctx.font = '14px Teko, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(
        style.abbreviation,
        canvasRect.x + (canvasRect.width / 2),
        canvasRect.y - (headerHeight / 2) + 4
      );
    }

    // Draw resize handles if needed
    if (showResizeHandles && (isHovered || isResizing)) {
      drawResizeHandles(ctx, rect, true);
    }

    // Draw extended edges for current box
    if (isCurrentBox && isDrawing) {
      drawExtendedBoxEdges(ctx, canvasRect.x, canvasRect.y, canvasRect.width, canvasRect.height, ctx.canvas.width, ctx.canvas.height);
    }
  };

  // Function to draw resize handles for a box
  const drawResizeHandles = (ctx, normalizedRect, isHovered) => {
    const handleRadius = 4; // Radius of corner circles (reduced by half)
    const edgeHandleWidth = 20; // Doubled from 10px
    const edgeHandleHeight = 8; // Doubled from 4px

    // Convert normalized rect to canvas coordinates
    const canvasRect = normalizedToCanvasRect(normalizedRect);
    const boxX = canvasRect.x;
    const boxY = canvasRect.y;
    const boxWidth = canvasRect.width;
    const boxHeight = canvasRect.height;

    // Only draw handles if the box is hovered
    if (!isHovered) return;

    // Draw delete button (X) in top-right corner
    const deleteButtonSize = 16;
    const padding = 4;

    // Draw button background
    ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
    ctx.beginPath();
    ctx.arc(
      boxX + boxWidth - deleteButtonSize/2 - padding,
      boxY + deleteButtonSize/2 + padding,
      deleteButtonSize/2,
      0,
      2 * Math.PI
    );
    ctx.fill();

    // Draw X
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    const offset = deleteButtonSize/4;

    // Draw X lines
    ctx.beginPath();
    ctx.moveTo(
      boxX + boxWidth - deleteButtonSize - padding + offset,
      boxY + padding + offset
    );
    ctx.lineTo(
      boxX + boxWidth - padding - offset,
      boxY + deleteButtonSize - padding - offset
    );
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(
      boxX + boxWidth - padding - offset,
      boxY + padding + offset
    );
    ctx.lineTo(
      boxX + boxWidth - deleteButtonSize - padding + offset,
      boxY + deleteButtonSize - padding - offset
    );
    ctx.stroke();

    // Draw corner circles
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.lineWidth = 1;

    // Top-left corner
    ctx.beginPath();
    ctx.arc(boxX, boxY, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Top-right corner
    ctx.beginPath();
    ctx.arc(boxX + boxWidth, boxY, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Bottom-left corner
    ctx.beginPath();
    ctx.arc(boxX, boxY + boxHeight, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Bottom-right corner
    ctx.beginPath();
    ctx.arc(boxX + boxWidth, boxY + boxHeight, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Draw edge handles
    // Left edge
    ctx.fillRect(
      boxX - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );
    ctx.strokeRect(
      boxX - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );

    // Right edge
    ctx.fillRect(
      boxX + boxWidth - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );
    ctx.strokeRect(
      boxX + boxWidth - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );

    // Top edge
    ctx.fillRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );
    ctx.strokeRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );

    // Bottom edge
    ctx.fillRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY + boxHeight - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );
    ctx.strokeRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY + boxHeight - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );
  };

  const findBoxAndHandleAtPoint = (x, y) => {
    if (!boxes || boxes.length === 0) return { boxId: null, handle: null };

    // Check boxes in reverse order (last drawn on top)
    for (let i = boxes.length - 1; i >= 0; i--) {
      const box = boxes[i];
      if (!box.rect) continue;

      // Skip boxes that are being processed
      //if (processingBoxIds.has(box.id)) continue;

      // First check if we're over a resize handle
      const handle = getResizeHandleAtPoint(x, y, box);
      if (handle) {
        return { boxId: box.id, handle };
      }

      // Then check if we're over the box itself
      if (isPointInBox(x, y, box.rect)) {
        return { boxId: box.id, handle: null };
      }
    }

    return { boxId: null, handle: null };
  };

  // Function to update cursor style based on what's under the mouse
  const updateCursorStyle = (boxId, handle) => {
    // Update cursor based on what we're hovering over
    if (handle) {
      // Set cursor based on handle type
      let cursor = 'default';
      switch (handle) {
        case 'tl':
        case 'br':
          cursor = 'nwse-resize';
          break;
        case 'tr':
        case 'bl':
          cursor = 'nesw-resize';
          break;
        case 'left':
        case 'right':
          cursor = 'ew-resize';
          break;
        case 'top':
        case 'bottom':
          cursor = 'ns-resize';
          break;
      }
      pdfContainerRef.current.style.cursor = cursor;
    }
    // else if (boxId) {
    //   // Over a box but not a handle
    //   pdfContainerRef.current.style.cursor = 'move';
    // }
    else {
      // Not over anything
      pdfContainerRef.current.style.cursor = 'default';
    }
  };

  useEffect(()=>{
    setdoAutoScroll(isDrawing||isResizing);
  },[isDrawing, isResizing]);

  const [fromBox,setFromBox] = useState(null);


  const handleMouseDown = (e) => {
    if (!page) {
      logBox('Box drawing attempted but no PDF is loaded');
      return;
    }

    if (!pdfContainerRef.current) {
      logBox('Box drawing failed: PDF container not available');
      return;
    }

    // Get mouse coordinates relative to the container
    const canvasCoords = toCanvasCoords(e);

    // Check if we're clicking on a resize handle or box
    const { boxId, handle } = findBoxAndHandleAtPoint(canvasCoords.x, canvasCoords.y);

    // If we're clicking on the delete button, remove the box
    if (boxId && handle === 'delete') {
      // Find the box we're deleting
      const boxToDelete = boxes.find(box => box.id === boxId);
      if (!boxToDelete) return;
      createActions(DELETE_BOX,{...boxToDelete, rect:{...boxToDelete.rect}, pageIndex:index},{...boxToDelete, rect:{...boxToDelete.rect}, pageIndex:index});

      // Remove the box
      setBoxes(prev => prev.filter(box => box.id !== boxId));

      return;
    }

    // If we're clicking on a resize handle, start resizing
    if (boxId && handle) {
      // Start resizing
      setIsResizing(true);
      setResizingBoxId(boxId);
      setResizeHandle(handle);
      setStartPoint(canvasCoords);

      // Show magnifying glass if enabled
      if (parentShowMagnifyingGlass) {
        setShowMagnifyingGlass(true);
        setMagnifyingGlassPosition(canvasCoords);
      }

      // Find the box we're resizing
      const boxToResize = boxes.find(box => box.id === boxId);
      if (boxToResize) {
        // Store the original rect for reference after resize to create undoStack
        setFromBox(prv=>{
          return { ...boxToResize, rect:{...boxToResize.rect},pageIndex:index };
        });
        // Store the original rect for reference after resize to create undoStack
        setCurrentRect({ ...boxToResize.rect });
      }
      return;
    }

    // If we're not resizing, proceed with normal box drawing
    logBox('Mouse down event detected (OCR mode)', {
      originalCoordinates: { x: e.clientX, y: e.clientY },
      canvasCoordinates: canvasCoords,
    });

    // Snap to grid if enabled
    const snappedCanvasCoords = {
      x: snapToGrid ? snapToGridValue(canvasCoords.x) : canvasCoords.x,
      y: snapToGrid ? snapToGridValue(canvasCoords.y) : canvasCoords.y
    };

    if (snapToGrid) {
      logBox('Coordinates snapped to grid', {
        original: canvasCoords,
        snapped: snappedCanvasCoords,
        gridSize: GRID_SIZE,
      });
    }

    // Convert to normalized coordinates for storage
    const normalizedCoords = toNormalizedCoords(snappedCanvasCoords);

    // Start drawing
    setIsDrawing(true);
    setStartPoint(snappedCanvasCoords);
    setCurrentRect({
      x: normalizedCoords.x,
      y: normalizedCoords.y,
      width: 0,
      height: 0
    });

    // Update magnifying glass position (it should already be visible if enabled)
    if (parentShowMagnifyingGlass) {
      setMagnifyingGlassPosition(snappedCanvasCoords);
    }

    logBox('OCR box drawing started', {
      startPoint: normalizedCoords,
      boxType: currentBoxType,
      boxStyle: boxStyles[currentBoxType],
      mode: 'OCR',
    });
  };

  const handleMouseMove = (e) => {
    // Get mouse coordinates and update state
    const pt = toCanvasCoords(e);
    setMousePosition(pt);

    // Handle hover effects if not drawing or resizing
    if (!isDrawing && !isResizing) {
      // We don't call handleMouseMoveForHover here anymore since it would update mousePosition again
      // Instead, we'll do the hover detection directly
      const { boxId, handle } = findBoxAndHandleAtPoint(pt.x, pt.y);
      updateCursorStyle(boxId, handle);
      setHoveredBox(boxId);
      return;
    }

    if (!pdfContainerRef.current) {
      return;
    }

    try {

      // Handle resizing
      if (isResizing && resizingBoxId && currentRect) {
        // Find the box we're resizing
        const boxToResize = boxes.find(box => box.id === resizingBoxId);
        if (!boxToResize) return;

        // Snap to grid if enabled
        const snappedCanvasCoords = {
          x: snapToGrid ? snapToGridValue(pt.x) : pt.x,
          y: snapToGrid ? snapToGridValue(pt.y) : pt.y
        };

        // Convert to normalized coordinates
        const normalizedCoords = toNormalizedCoords(snappedCanvasCoords);

        // Calculate the new rect based on which handle is being dragged
        let newRect = { ...currentRect };

        const minSize = 0.01; // Minimum size in normalized coordinates (1% of canvas)

        switch (resizeHandle) {
          case 'tl': // Top-left
            newRect = {
              x: Math.min(normalizedCoords.x, (currentRect.x + currentRect.width)),
              y: Math.min(normalizedCoords.y, (currentRect.y + currentRect.height)),
              width: Math.abs((currentRect.x + currentRect.width) - normalizedCoords.x),
              height: Math.abs((currentRect.y + currentRect.height) - normalizedCoords.y),
            };
            break;
          case 'tr': // Top-right
            newRect = {
              x: currentRect.x,
              y: Math.min(normalizedCoords.y, (currentRect.y + currentRect.height)),
              width: Math.max(minSize, normalizedCoords.x - currentRect.x),
              height: Math.abs((currentRect.y + currentRect.height) - normalizedCoords.y),
            };
            break;
          case 'bl': // Bottom-left
            newRect = {
              x: Math.min(normalizedCoords.x, (currentRect.x + currentRect.width)),
              y: currentRect.y,
              width: Math.abs((currentRect.x + currentRect.width) - normalizedCoords.x),
              height: Math.max(minSize, normalizedCoords.y - currentRect.y),
            };
            break;
          case 'br': // Bottom-right
            newRect = {
              x: currentRect.x,
              y: currentRect.y,
              width: Math.max(minSize, normalizedCoords.x - currentRect.x),
              height: Math.max(minSize, normalizedCoords.y - currentRect.y),
            };
            break;
          case 'left': // Left edge
            newRect = {
              x: Math.min(normalizedCoords.x, (currentRect.x + currentRect.width)),
              y: currentRect.y,
              width: Math.abs((currentRect.x + currentRect.width) - normalizedCoords.x),
              height: currentRect.height,
            };
            break;
          case 'right': // Right edge
            newRect = {
              x: currentRect.x,
              y: currentRect.y,
              width: Math.max(minSize, normalizedCoords.x - currentRect.x),
              height: currentRect.height,
            };
            break;
          case 'top': // Top edge
            newRect = {
              x: currentRect.x,
              y: Math.min(normalizedCoords.y, (currentRect.y + currentRect.height)),
              width: currentRect.width,
              height: Math.abs((currentRect.y + currentRect.height) - normalizedCoords.y),
            };
            break;
          case 'bottom': // Bottom edge
            newRect = {
              x: currentRect.x,
              y: currentRect.y,
              width: currentRect.width,
              height: Math.max(minSize, normalizedCoords.y - currentRect.y),
            };
            break;
        }

        // Update current rect
        setCurrentRect(newRect);

        // Update the box in the boxes array (temporary update during resize)
        setBoxes(prev => prev.map(box =>
          box.id === resizingBoxId ? { ...box, rect: newRect } : box
        ));

        return;
      }

      // Handle drawing
      if (isDrawing) {
        // Snap to grid if enabled
        const snappedCanvasCoords = {
          x: snapToGrid ? snapToGridValue(pt.x) : pt.x,
          y: snapToGrid ? snapToGridValue(pt.y) : pt.y
        };

        const dx = snappedCanvasCoords.x - startPoint.x;
        const dy = snappedCanvasCoords.y - startPoint.y;

        // Convert to normalized coordinates
        const currentNormalizedCoords = toNormalizedCoords(snappedCanvasCoords);
        const startNormalizedCoords = toNormalizedCoords(startPoint);

        setCurrentRect({
          x: dx < 0 ? currentNormalizedCoords.x : startNormalizedCoords.x,
          y: dy < 0 ? currentNormalizedCoords.y : startNormalizedCoords.y,
          width: Math.abs(currentNormalizedCoords.x - startNormalizedCoords.x),
          height: Math.abs(currentNormalizedCoords.y - startNormalizedCoords.y),
        });
      }
    } catch (error) {
      console.error('Error during mouse move:', error);
      setIsDrawing(false);
      setIsResizing(false);
    }
  };

  const handleMouseUp = () => {
    // Handle resizing completion
    if (isResizing && resizingBoxId) {
      try {
        // Find the box we're resizing
        const boxIndex = boxes.findIndex(box => box.id === resizingBoxId);
        if (boxIndex === -1) {
          throw new Error(`Box with ID ${resizingBoxId} not found`);
        }

        const oldBox = boxes[boxIndex];

        // Ensure the new rect has minimum dimensions (in normalized coordinates)
        const minSize = 0.01; // 1% of canvas size
        if (currentRect && currentRect.width > minSize && currentRect.height > minSize) {
          // Create a new box with the new dimensions and ID
          const newBox = {
            ...oldBox,
            rect: currentRect,
          };
          createActions(RESIZE_BOX, fromBox, {...newBox,rect:{...newBox.rect}, pageIndex:index});

          // Update the boxes array
          const updatedBoxes = [...boxes];
          updatedBoxes[boxIndex] = newBox;
          setBoxes(updatedBoxes);

          logBox('Box resized successfully', {
            oldBoxId: oldBox.id,
            boxType: oldBox.type,
            oldRect: oldBox.rect,
            newRect: currentRect,
          });
        } else {
          // Revert to original dimensions if too small
          setBoxes(prev => prev.map(box =>
            box.id === resizingBoxId ? oldBox : box
          ));

          logBox('Box resize cancelled - too small', {
            minimumSize: { width: minSize, height: minSize },
            actualSize: currentRect
              ? {
                  width: Math.round(currentRect.width),
                  height: Math.round(currentRect.height),
                }
              : null,
            zoomFactor
          });
        }
      } catch (error) {
        logError('Error during box resize completion', {
          errorMessage: error.message,
          errorStack: error.stack,
        });
      } finally {
        setIsResizing(false);
        setResizingBoxId(null);
        setResizeHandle(null);
        setCurrentRect(null);

        // Hide magnifying glass when resizing ends
        setShowMagnifyingGlass(false);
      }

      return;
    }

    // Handle drawing completion
    if (isDrawing) {
      try {
        const minSize = 0.01; // 1% of canvas size in normalized coordinates
        if (currentRect && currentRect.width > minSize && currentRect.height > minSize) {
          const newBox = {
            type: currentBoxType,
            id: uuidv4(),
            rect: currentRect,
          };

          // Check if auto-select columns is enabled and TextractRBush is initialized
          if (autoSelectColumns && textractRBush && isImageBasedPdf && (pageRotations !== 90 && pageRotations !== 270)) {
            // Get the current rotation for this page
            const currentPageRotation = pageRotations|| 0;

            const zoomFactor = zoomPercentage / 100;

            // Apply rotation in normalized space (1x1) to match textract's 0° reference frame
            const rotatedRect = transformBoundingBox(
              {
                top: currentRect.y,
                left: currentRect.x,
                width: currentRect.width,
                height: currentRect.height
              },
              -currentPageRotation,
              1, // Use normalized space
              1  // Use normalized space
            );

            // Call getColumnBoundingBox with normalized coordinates
            const columnBoundingBox = textractRBush.getColumnBoundingBox(
              rotatedRect.left,
              rotatedRect.top,
              rotatedRect.width,
              rotatedRect.height,
              overlapPercent/100,
              index+1,
              zoomFactor
            );

            if (columnBoundingBox) {
              //Reverse rotation in normalized space to get back to current view rotation
              const finalRect = transformBoundingBox(
                {
                  top: columnBoundingBox.top,
                  left: columnBoundingBox.left,
                  width: columnBoundingBox.width,
                  height: columnBoundingBox.height
                },
                currentPageRotation, // Negative rotation to reverse
                1, // Use normalized space
                1  // Use normalized space
              );
  

              // Store directly as normalized coordinates
              newBox.rect = {
                x: finalRect.left,
                y: finalRect.top,
                width: finalRect.width,
                height: finalRect.height,
              };
            } else {
              console.log("No column found for auto-selection, using original box");
            }
          } else {
            console.log("Auto-select columns disabled or TextractRBush not initialized, using original box");
          }
          createActions(ADD_BOX, {...newBox, rect:{...newBox.rect}, pageIndex:index}, {...newBox, rect:{...newBox.rect}, pageIndex:index});
          // Add the new box to the boxes array
          setBoxes((prev) => [...prev, {...newBox, timeStamp:Date.now()}]);

          logBox('OCR box created successfully', {
            boxType: currentBoxType,
            boxId: newBox.id,
            position: {
              x: Math.round(currentRect.x),
              y: Math.round(currentRect.y),
            },
            size: {
              width: Math.round(currentRect.width),
              height: Math.round(currentRect.height),
            },
            totalBoxes: boxes.length + 1,
            boxStyle: boxStyles[currentBoxType],
          });
        } else {
          logBox('OCR box creation cancelled - too small', {
            minimumSize: { width: minSize, height: minSize },
            actualSize: currentRect
              ? {
                  width: Math.round(currentRect.width),
                  height: Math.round(currentRect.height),
                }
              : null
          });
        }
      } catch (error) {
        logError('Error during mouse up in OCR mode', {
          errorMessage: error.message,
          errorStack: error.stack,
        });
      } finally {
        setIsDrawing(false);
        setCurrentRect(null);

        // Hide magnifying glass when drawing ends
        setShowMagnifyingGlass(false);
      }
    }
  };

  function transformBoundingBox({ top, left, width, height }, rotation, canvasWidth, canvasHeight) {
    const rot = ((rotation % 360) + 360) % 360;
    let newLeft, newTop;
    switch (rot) {
      case 0:
        newLeft = left;
        newTop  = top;
        break;
      case 90:
        newLeft = canvasHeight - top - height;
        newTop  = left;
        break;
      case 180:
        newLeft = canvasWidth  - left  - width;
        newTop  = canvasHeight - top   - height;
        break;
      case 270:
        newLeft = top;
        newTop  = canvasWidth  - left  - width;
        break;
      default:
        throw new Error('Rotation must be a multiple of 90°');
    }
    return { left: newLeft, top: newTop, width, height };
  }

    return <>
    <div
        ref={pdfContainerRef}
        className={clsx(styles.pdfContainer,"pdf-container")}
        onMouseDown={handleMouseDown}
        onMouseMove={(e) => {
          // Always update mouse position for dotted lines
          const pt = toCanvasCoords(e);
          setMousePosition(pt);

          // Get the PDF content div
          const contentDiv = e.currentTarget.querySelector('.pdf-content');

          // Show magnifying glass whenever mouse is over the PDF content (not just when drawing/resizing)
          if (contentDiv && parentShowMagnifyingGlass) {
            const contentRect = contentDiv.getBoundingClientRect();

            // Check if mouse is over the PDF content
            if (
              e.clientX >= contentRect.left &&
              e.clientX <= contentRect.right &&
              e.clientY >= contentRect.top &&
              e.clientY <= contentRect.bottom
            ) {
              // Show magnifying glass and update position
              setShowMagnifyingGlass(true);
              setMagnifyingGlassPosition(pt);
            } else {
              // Hide magnifying glass when not over PDF content
              setShowMagnifyingGlass(false);
            }
          } else {
            // Ensure magnifying glass is hidden when disabled
            setShowMagnifyingGlass(false);
          }

          // Call the regular mouse move handler
          handleMouseMove(e);
        }}
        onMouseUp={handleMouseUp}
        onMouseEnter={(e) => {
          // Update mouse position for dotted lines
          const pt = toCanvasCoords(e);
          setMousePosition(pt);

          // Show magnifying glass whenever mouse enters the PDF content area (if enabled)
          if (!parentShowMagnifyingGlass) {
            setShowMagnifyingGlass(false);
            return;
          }

          const contentDiv = e.currentTarget.querySelector('.pdf-content');
          if (contentDiv) {
            const contentRect = contentDiv.getBoundingClientRect();

            // Check if mouse is over the PDF content
            if (
              e.clientX >= contentRect.left &&
              e.clientX <= contentRect.right &&
              e.clientY >= contentRect.top &&
              e.clientY <= contentRect.bottom
            ) {
              setShowMagnifyingGlass(true);
              setMagnifyingGlassPosition(pt);
            }
          }
        }}
        onMouseLeave={() => {
          setShowMagnifyingGlass(false);
          // Reset mouse position to hide dotted lines when mouse leaves the PDF
          setMousePosition({ x: -100, y: -100 });
        }}
        style={{
          width: '100%',
          height: '100%',
          textAlign: 'center', // Center the content horizontally
          cursor: (parentShowMagnifyingGlass && showMagnifyingGlass) ? 'crosshair' : 'default', // Show crosshair cursor when magnifying glass is visible
          display: 'flex',
          // justifyContent: 'center', // Align content to the start to allow proper scrolling
          alignItems: 'flex-start'
        }}
      >
        <div className="pdf-content" style={{
          display: 'inline-block',
          position: 'relative',
          textAlign: 'left', // Reset text alignment for the content
          overflow: 'visible', // Allow content to overflow for zoom
          height: 'auto', // Let height adjust to content
          width: 'auto' // Let width adjust to content
        }}>
        <div style={{
              width: '100%',
              height: '100%'
            }}>
            <canvas
              ref={canvasRef}
              style={{
                height: 'auto', // Let height be determined by the canvas
                width: 'auto', // Let width be determined by the canvas
                display: 'block' // Remove any extra space below the canvas
              }}
            />
            <canvas
              ref={overlayCanvasRef}
              className="pdf-overlay"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                pointerEvents: 'none',
                width: '100%',
                height: '100%'
              }}
            />
          </div>
          { magnifyingGlassCanvasRef.current && showMagnifyingGlass && (
            <div
              className="magnifying-glass-wrapper"
              style={{
                position: 'absolute',
                // Position at bottom left of cursor, but ensure it stays within viewport
                // Account for scroll position of the container
                left: Math.min(
                  magnifyingGlassPosition.x + 20 - (pdfContainerRef.current?.scrollLeft || 0) - 10, // Offset by frame width
                  (canvasRef.current?.width || 0) - 180 // Adjusted for frame width
                ),
                top: Math.min(
                  magnifyingGlassPosition.y + 20 - (pdfContainerRef.current?.scrollTop || 0) - 10, // Offset by frame height
                  (canvasRef.current?.height || 0) - 130 // Adjusted for frame height
                ),
                width: 182, // Width + frame padding
                height: 73, // Height + frame padding
                padding: '7px', // Frame padding
                borderRadius: '8px', // Slightly larger than inner div
                background: 'white', // Gradient background for frame
                boxShadow: 'rgba(0, 0, 0, 0.94) 0px 4px 6.7px 1px', // Stronger shadow for frame
                pointerEvents: 'none',
                color: '#333',
                zIndex: 1000
              }}
            >
              <div
                className={styles.magnifyingGlass}
                style={{
                  position: 'relative', // Changed from absolute to relative
                  width: 70, // Fill the wrapper
                  height: 59, // Fill the wrapper
                  borderRadius: '8px', // Slightly smaller than wrapper
                  overflow: 'hidden',
                  border: '1px solid #333',
                  backgroundColor: 'white'
                }}
            >
              <canvas
                style={{
                  position: 'absolute',
                  width: '100%',
                  height: '100%'
                }}
                ref={(canvas) => {
                  if (canvas && magnifyingGlassCanvasRef.current) {
                    const ctx = canvas.getContext('2d');
                    canvas.width = 80;
                    canvas.height = 59;

                    // Calculate the position in the magnifying glass canvas coordinates
                    const magX = magnifyingGlassPosition.x * (magnifyingGlassCanvasRef.current.width / canvasRef.current.width);
                    const magY = magnifyingGlassPosition.y * (magnifyingGlassCanvasRef.current.height / canvasRef.current.height);

                    // Draw the portion of the magnifying glass canvas that corresponds to the current position
                    // Clear the canvas first
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // Draw the portion centered on the cursor position
                    ctx.drawImage(
                      magnifyingGlassCanvasRef.current,
                      magX - canvas.width/2, magY - canvas.height/2, canvas.width, canvas.height, // Source rectangle (centered on cursor)
                      0, 0, canvas.width, canvas.height // Destination rectangle
                    );

                    // Draw a small crosshair in the center to indicate the exact cursor position
                    ctx.strokeStyle = 'rgba(255, 0, 0, 0.7)';
                    ctx.lineWidth = 1;
                    const screenCenterX = canvas.width / 2;
                    const screenCenterY = canvas.height / 2;
                    const crosshairSize = 10;


                    // Horizontal line
                    ctx.beginPath();
                    ctx.moveTo(screenCenterX - crosshairSize/2, screenCenterY);
                    ctx.lineTo(screenCenterX + crosshairSize/2, screenCenterY);
                    ctx.stroke();

                    // Vertical line
                    ctx.beginPath();
                    ctx.moveTo(screenCenterX, screenCenterY - crosshairSize/2);
                    ctx.lineTo(screenCenterX, screenCenterY + crosshairSize/2);
                    ctx.stroke();
                  }
                }}
              />
            </div>
            <div className={styles.magnifyGlass}>
               <h4>Super Zoom</h4>
                <p>Press ‘esc’ to <br/>close this feature.</p>
            </div>
        </div>
    )}
    </div>
    </div>
    </>;
}

export default forwardRef(PdfPage);
//export default PdfPage;

