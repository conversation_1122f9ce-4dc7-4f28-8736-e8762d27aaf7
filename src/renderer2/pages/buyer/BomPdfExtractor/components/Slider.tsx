import React from 'react';
import styles from '../styles/BomExtractor.module.scss';

interface SliderProps {
  min: number;
  max: number;
  value: number;
  onChange: (value: number) => void;
  label?: string;
  className?: string;
}

const Slider: React.FC<SliderProps> = ({
  min,
  max,
  value,
  onChange,
  label,
  className
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(event.target.value, 10);
    onChange(newValue);
  };

  return (
    <div className={`${styles.sliderContainer} ${className || ''}`}>
      {label && (
        <label className={styles.sliderLabel}>{label}</label>
      )}
      <div className={styles.sliderWrapper}>
        <input
          type="range"
          min={min}
          max={max}
          value={value}
          onChange={handleChange}
          className={styles.slider}
        />
        <div className={styles.sliderValue}>{Math.round(value)}%</div>
      </div>
    </div>
  );
};

export default Slider;
