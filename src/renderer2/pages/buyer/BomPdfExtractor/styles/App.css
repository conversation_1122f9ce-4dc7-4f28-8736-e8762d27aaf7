/* App.css - Global styles for the PDF Text Extractor application

:root {
  --text-color: #ffffff;
  --button-background: #000000;
  --button-text: #ffffff;
  --button-hover: #222222;
  --button-disabled: #555555;
  --border-color: #666666;
  --success-background: #2a4d3e;
  --success-text: #b7ebd8;
  --warning-background: #4d432a;
  --warning-text: #ebe6b7;
  --error-background: #4d2a2a;
  --error-text: #ebb7b7;
  --table-header: #222222;
  --table-row-odd: #3a3a3a;
  --table-row-even: #444444;
  --table-border: #555555;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-color);
}

.app-container {
  max-width: 1800px;
  margin: 0 auto;
  padding: 20px;
}

.card {
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Button styles */
/* button {
  background-color: var(--button-background);
  color: var(--button-text);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

button:hover:not(:disabled) {
  background-color: var(--button-hover);
}

button:disabled {
  background-color: var(--button-disabled);
  cursor: not-allowed;
  opacity: 0.7;
} */

/* Select styles */
/* select {
  background-color: var(--button-background);
  color: var(--button-text);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 7px 16px;
  margin-right: 8px;
  cursor: pointer;
  font-size: 14px;
} */

/* Input styles */
/* input[type="file"] {
  display: none;
} */

/* Alert styles */
/* .alert {
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.alert-success {
  background-color: var(--success-background);
  color: var(--success-text);
  border: 1px solid var(--success-text);
}

.alert-warning {
  background-color: var(--warning-background);
  color: var(--warning-text);
  border: 1px solid var(--warning-text);
}

.alert-error {
  background-color: var(--error-background);
  color: var(--error-text);
  border: 1px solid var(--error-text);
} */

/* PDF filename styles */
/* .pdf-filename {
  background-color: var(--card-background);
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 4px;
  border-left: 4px solid var(--button-background);
  font-size: 16px;
} */

/* PDF container styles */
/* .pdf-container {
  position: relative;
  border-radius: 4px;
  min-height: 500px;
  margin-bottom: 20px;
  overflow-x: hidden;
  overflow-y: hidden;
  white-space: nowrap;
  text-align: center; /* Center the content horizontally */
/* }

.pdf-container-main {
  position: relative;
  border-radius: 4px;
  min-height: 500px;
  margin-bottom: 20px;
  white-space: nowrap;
  text-align: center; /* Center the content horizontally */
/* } */

/* .pdf-content {
  position: relative;
  display: inline-block;
  text-align: left; /* Reset text alignment for the content */
/* }  */

/* .pdf-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 10;
} */

/* Magnifying glass styles */
/* .magnifying-glass {
  position: absolute;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  z-index: 1000;
  border: 2px solid white;
} */

/* Table styles */
/* table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  border: 1px solid var(--table-border);
}

thead {
  background-color: var(--table-header);
}

th {
  padding: 10px;
  text-align: left;
  border-bottom: 2px solid var(--table-border);
}

td {
  padding: 8px 10px;
  border-bottom: 1px solid var(--table-border);
}

tbody tr:nth-child(odd) {
  background-color: var(--table-row-odd);
}

tbody tr:nth-child(even) {
  background-color: var(--table-row-even);
} */

/* PDF Controls styles */
/* .pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  flex-wrap: wrap;
}

.rotation-controls {
  display: flex;
  gap: 10px;
} */

/* Pagination styles */
/* .pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15px 0;
}

.pagination button {
  margin: 0 5px;
}

.pagination span {
  margin: 0 10px;
  color: var(--text-color);
} */

/* Toolbar styles */
/* .toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 5px;
  padding:  5px 10px;
  background-color: var(--card-background);
  border-radius: 4px;
} */

/* Responsive styles */
/* @media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: flex-start;
  }

  button, select {
    margin-bottom: 10px;
    width: 100%;
  }
}

.toolbar-title-container {
  display: inline-flex;
  flex-direction: column;
  margin-right: 1rem;
  flex-shrink: 0;
}

.toolbar-title {
  font-size: 25px;
  font-weight: bold;
  line-height: 1.2;
}

.toolbar-filename {
  font-size: 14px;
  color: #cccccc;
  margin-top: 2px;
}



.box-type-buttons {
  width:380px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.custom-column-creator-container {
  position: relative;
  z-index: 1000;
}

.box-type-button {
  padding: 6px 12px;
  border: 2px solid;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.box-type-button:hover:not(:disabled) {
  opacity: 0.8;
}

.box-type-button.selected {
  font-weight: bold;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
}

.box-type-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.create-custom-button {
  padding: 6px 12px;
  border: 2px solid #000;
  border-radius: 4px;
  background-color: #000;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: bold;
}

.create-custom-button:hover:not(:disabled) {
  opacity: 0.8;
}

.create-custom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
} */


.pdf-content{
  margin: 0 auto;
  margin-bottom: 0.2rem;
}