/**
 * TextractRBush - A library for creating an RBush spatial index from AWS TextExtract data
 * and querying text by coordinates.
 */

import RBush from 'rbush';

/**
 * TextractRBush class for creating and querying a spatial index of text from AWS TextExtract
 */
class TextractRBush {
    /**
     * Constructor
     */
    constructor() {
        this.rbush = null;
        this.blockMap = {};
        this.initialized = false;
    }

    /**
     * Initialize the RBush index with AWS TextExtract data
     * @param {Object} textractData - The JSON data from AWS TextExtract
     * @returns {TextractRBush} - Returns this instance for chaining
     */
    initialize(textractData) {
        if (!textractData || !textractData.Blocks || !Array.isArray(textractData.Blocks)) {
            throw new Error('Invalid TextExtract data: Blocks array is missing or invalid');
        }

        // Create a new RBush instance
        this.rbush = new RBush();
        this.blockMap = {};

        // First, create a map of all blocks by ID for easy lookup
        textractData.Blocks.forEach(block => {
            this.blockMap[block.Id] = block;
        });

        // Process all blocks with geometry
        const items = [];
        textractData.Blocks.forEach(block => {
            if (block.Geometry && block.Geometry.BoundingBox) {
                const bbox = block.Geometry.BoundingBox;
                const text = this._getTextFromBlock(block);


                // Only include blocks that have text or are of specific types we want to index
                if (text || this._isIndexableBlockType(block.BlockType)) {
                    items.push({
                        minX: bbox.Left,
                        minY: bbox.Top,
                        maxX: bbox.Left + bbox.Width,
                        maxY: bbox.Top + bbox.Height,
                        block: {
                            id: block.Id,
                            type: block.BlockType,
                            text: text,
                            confidence: block.Confidence,
                            page: block.Page,
                            boundingBox: bbox
                        }
                    });
                }
            }
        });

        // Load items into the RBush index
        this.rbush.load(items);
        this.initialized = true;

        return this;
    }

    /**
     * Query the RBush index with a rectangle
     * @param {Object} rect - The rectangle to query {x, y, width, height} (normalized coordinates 0-1)
     * @param {number} overlapRatio - The minimum overlap ratio required (0-1)
     * @param {Object} pageSize - The page size {width, height}
     * @param {number} [page] - Optional page number to filter results
     * @param {number} [rotation] - Optional rotation angle in degrees
     * @param {number} [zoomFactor] - Optional zoom factor (zoom/100) to scale tolerance
     * @returns {Array} - Array of text items with their coordinates
     */
    query(rect, overlapRatio = 0.5, pageSize, page = null, rotation = 0, zoomFactor = 1) {
        if (!this.initialized) {
            throw new Error('TextractRBush not initialized. Call initialize() first.');
        }

        if (!rect || typeof rect.left !== 'number' || typeof rect.top !== 'number' ||
            typeof rect.width !== 'number' || typeof rect.height !== 'number') {
            throw new Error('Invalid rectangle: must have left, top, width, height properties');
        }

        if (typeof overlapRatio !== 'number' || overlapRatio < 0 || overlapRatio > 1) {
            throw new Error('Invalid overlapRatio: must be a number between 0 and 1');
        }

        // Convert rectangle to RBush format
        const searchRect = {
            minX: rect.left,
            minY: rect.top,
            maxX: rect.left + rect.width,
            maxY: rect.top + rect.height
        };

        // Search for intersecting blocks
        let results = this.rbush.search(searchRect);

        // Filter by page if specified
        if (page !== null) {
            results = results.filter(item => item.block.page === page);
        }

        // Filter by overlap ratio
        let overlapRegions = results.filter(item => {
            const intersection = this._calculateIntersectionArea(searchRect, {
                minX: item.minX,
                minY: item.minY,
                maxX: item.maxX,
                maxY: item.maxY
            });

            const itemArea = (item.maxX - item.minX) * (item.maxY - item.minY);
            const overlapPercentage = intersection / itemArea;

            // Store the overlap percentage for reference
            item.block.overlapPercentage = overlapPercentage;
            return overlapPercentage >= overlapRatio;
        });
        
        const cellResults = []//overlapRegions.filter(item => item.block.isTableCell || item.block.type === 'CELL' || item.block.type === 'TABLE_TITLE');
        

          if(cellResults.length === 0){
              const tableResults = []//results.filter(item => item.block.type === 'TABLE' || item.block.type === 'TABLE_FOOTER');
              if(tableResults.length === 0){
                const wordResults = overlapRegions.filter(item=>item.block.type === 'WORD' || item.block.type === 'KEY_VALUE_SET');
                if(wordResults.length === 0){
                  overlapRegions = [];
                }else{
                  overlapRegions = wordResults;
                }
              }else{
                overlapRegions = tableResults;
              }
          }else{
            overlapRegions = cellResults;
          }

          overlapRegions = overlapRegions.map(item => {
                const transformedBoundingBox = this._transformBoundingBox({
                    top: item.block.boundingBox.Top,
                    left: item.block.boundingBox.Left,
                    width: item.block.boundingBox.Width,
                    height: item.block.boundingBox.Height
                }, rotation );

                const retValue = {
                    text: item.block.text,
                    type: item.block.type,
                    confidence: item.block.confidence,
                    page: item.block.page,
                    boundingBox: {x:transformedBoundingBox.left*pageSize.width, y:transformedBoundingBox.top*pageSize.height, width:transformedBoundingBox.width*pageSize.width, height:transformedBoundingBox.height*pageSize.height},//item.block.boundingBox,
                    overlapPercentage: item.block.overlapPercentage
                }
                return retValue;
            })
            .sort((a, b) => a.boundingBox.y - b.boundingBox.y);

            // Pass the zoomFactor to _mergeCells to scale the tolerance
            return this._mergeCells(overlapRegions, 3 * zoomFactor);
    }

    _transformBoundingBox({ top, left, width, height }, rotation, canvasWidth = 1, canvasHeight = 1) {
        const rot = ((rotation % 360) + 360) % 360;
        let newLeft, newTop;
        switch (rot) {
          case 0:
            newLeft = left;
            newTop  = top;
            break;
          case 90:
            newLeft = canvasHeight - top - height;
            newTop  = left;
            break;
          case 180:
            newLeft = canvasWidth  - left  - width;
            newTop  = canvasHeight - top   - height;
            break;
          case 270:
            newLeft = top;
            newTop  = canvasWidth  - left  - width;
            break;
          default:
            throw new Error('Rotation must be a multiple of 90°');
        }
        return { left: newLeft, top: newTop, width, height };
      }

    /**
     * Merge cells that are at the same y-position (within tolerance)
     * @private
     * @param {Array} items - Array of text items to merge
     * @param {number} tolerance - Tolerance for y-position difference (scaled by zoomFactor)
     * @returns {Array} - Array of merged text items
     */
    _mergeCells(items, tolerance = 2) {
        if (!items || items.length === 0) return [];

        // Use the tolerance parameter which is already scaled by zoomFactor
        const scaledTolerance = tolerance;

      
        const merged = [];
        let row = [];
        row.push(items[0]);

        for (let i = 1; i < items.length; i++) {
          const item = items[i];
          const dy = Math.abs(item.boundingBox.y - items[i-1].boundingBox.y);

          if (dy <= scaledTolerance) {
            row.push(item);
          } else {
           row.sort((a, b) => a.boundingBox.x - b.boundingBox.x);
           let mergedText = '';
           const mergedBoundingBox = {x:row[0].boundingBox.x, y:row[0].boundingBox.y, width:0, height:row[0].boundingBox.height};
           let overlapPercentage = 0;
           row.forEach((cell) => {
            mergedText += cell.text + ' ';
            mergedBoundingBox.x = Math.min(mergedBoundingBox.x, cell.boundingBox.x);
            mergedBoundingBox.width += cell.boundingBox.width;
            overlapPercentage += cell.overlapPercentage;
           });
           overlapPercentage = overlapPercentage / row.length;
           merged.push({text:mergedText.trim(), boundingBox:mergedBoundingBox, overlapPercentage:overlapPercentage});
           row = [];
           row.push(item);
          }
        }

        // Push the final group
        if(row.length > 0){
          row.sort((a, b) => a.boundingBox.x - b.boundingBox.x);
          let mergedText = '';
          const mergedBoundingBox = {x:row[0].boundingBox.x, y:row[0].boundingBox.y, width:0, height:row[0].boundingBox.height};
          let overlapPercentage = 0;
          row.forEach((cell) => {
            mergedText += cell.text + ' ';
            mergedBoundingBox.x = Math.min(mergedBoundingBox.x, cell.boundingBox.x);
            mergedBoundingBox.width += cell.boundingBox.width;
            overlapPercentage += cell.overlapPercentage;
          });
          overlapPercentage = overlapPercentage / row.length;
          merged.push({text:mergedText.trim(), boundingBox:mergedBoundingBox, overlapPercentage:overlapPercentage});
        }
        return merged;
      }


    /**
     * Calculate the intersection area between two rectangles
     * @private
     * @param {Object} rect1 - First rectangle in RBush format
     * @param {Object} rect2 - Second rectangle in RBush format
     * @returns {number} - Area of intersection
     */
    _calculateIntersectionArea(rect1, rect2) {
        const xOverlap = Math.max(
            0,
            Math.min(rect1.maxX, rect2.maxX) - Math.max(rect1.minX, rect2.minX)
        );

        const yOverlap = Math.max(
            0,
            Math.min(rect1.maxY, rect2.maxY) - Math.max(rect1.minY, rect2.minY)
        );

        return xOverlap * yOverlap;
    }

    /**
     * Extract text from a block, including text from child blocks if necessary
     * @private
     * @param {Object} block - The TextExtract block
     * @returns {string} - The extracted text
     */
    _getTextFromBlock(block) {
        // If the block has text, return it
        if (block.Text) {
            return block.Text;
        }

        // If the block has child relationships, get text from children
        let text = '';
        if (block.Relationships) {
            for (const rel of block.Relationships) {
                if (rel.Type === 'CHILD') {
                    for (const childId of rel.Ids) {
                        const childBlock = this.blockMap[childId];
                        if (childBlock && childBlock.Text) {
                            text += childBlock.Text + ' ';
                        }
                    }
                }
            }
        }

        return text.trim();
    }

    /**
     * Check if a block type should be indexed
     * @private
     * @param {string} blockType - The TextExtract block type
     * @returns {boolean} - Whether the block should be indexed
     */
    _isIndexableBlockType(blockType) {
        const indexableTypes = [
            'LINE', 'WORD', 'SELECTION_ELEMENT',
            'TABLE', 'CELL', 'TABLE_TITLE', 'KEY_VALUE_SET'
        ];
        return indexableTypes.includes(blockType);
    }

    /**
     * Get the bounding box of columns based on a selection rectangle
     * @param {number} x - The x coordinate of the selection rectangle
     * @param {number} y - The y coordinate of the selection rectangle
     * @param {number} width - The width of the selection rectangle
     * @param {number} height - The height of the selection rectangle
     * @param {number} overlapRatio - The minimum overlap ratio required (default: 0.5)
     * @param {number} page - Optional page number to filter results
     * @param {number} zoomFactor - Optional zoom factor (zoom/100) to scale tolerances
     * @returns {Object} - The column bounding boxes with additional column data
     */
    getColumnBoundingBox(x, y, width, height, overlapRatio = 0.5, page = null, zoomFactor = 1) {
        // 1. Find all cells under the selection rectangle
        const pointRect = { minX: x, minY: y, maxX: x + width, maxY: y + height };
        let candidates = this.rbush.search(pointRect);
        if (page !== null) candidates = candidates.filter(i => i.block.page === page);

        let overlapRegions = candidates.filter(item => {
          const intersection = this._calculateIntersectionArea(pointRect, {
              minX: item.minX,
              minY: item.minY,
              maxX: item.maxX,
              maxY: item.maxY
          });

          const itemArea = (item.maxX - item.minX) * (item.maxY - item.minY);
          const overlapPercentage = intersection / itemArea;

          // Store the overlap percentage for reference
          item.block.overlapPercentage = overlapPercentage;
          return overlapPercentage >= overlapRatio;
        });

        // Filter to get only CELL type items
        const cellItems = overlapRegions.filter(i => i.block.type === 'CELL');
        if (cellItems.length === 0) return null;

        // 2. Find column boundaries based on all selected cells
        const columnBoundaries = this._findColumnBoundaries(cellItems, page, overlapRatio, zoomFactor);
        if (columnBoundaries.length === 0) return null;

        // 3. Identify the table that contains the selected cells
        // Use the first cell to find the table bounds
        const tableBounds = this._findTableBounds(cellItems[0], page);

        // 4. Create a selection rectangle that spans the identified columns
        const expandedRect = {
            minX: Math.min(...columnBoundaries.map(col => col.minX)),
            minY: tableBounds ? tableBounds.minY : 0,
            maxX: Math.max(...columnBoundaries.map(col => col.maxX)),
            maxY: tableBounds ? tableBounds.maxY : 1
        };

        // 5. Find all cells in the page
        let allPageCells = this.rbush.search({
            minX: 0,
            minY: 0,
            maxX: 1,
            maxY: 1
        });
        if (page !== null) allPageCells = allPageCells.filter(i => i.block.page === page);
        allPageCells = allPageCells.filter(item => item.block.type === 'CELL');

        // 6. Find all cells that belong to the selected columns AND are within the target table
        const selectedColumnCells = allPageCells.filter(cell => {
            // Check if this cell is within the target table bounds
            const isInTargetTable = tableBounds ? (
                cell.minX >= tableBounds.minX - 0.01 &&
                cell.maxX <= tableBounds.maxX + 0.01 &&
                cell.minY >= tableBounds.minY - 0.01 &&
                cell.maxY <= tableBounds.maxY + 0.01
            ) : true;

            if (!isInTargetTable) return false;

            // Check if this cell overlaps with any of the selected columns
            return columnBoundaries.some(column => {
                const cellCenterX = (cell.minX + cell.maxX) / 2;
                return cellCenterX >= column.minX && cellCenterX <= column.maxX;
            });
        });

        // 7. Group cells by column for processing
        const cellsByColumn = {};
        selectedColumnCells.forEach(cell => {
            // Use the cell's center X as the column identifier
            const cellCenterX = Math.round(((cell.minX + cell.maxX) / 2) * 1000);
            if (!cellsByColumn[cellCenterX]) {
                cellsByColumn[cellCenterX] = [];
            }
            cellsByColumn[cellCenterX].push(cell);
        });

        // 8. For each column, sort cells by Y position and exclude the first cell (header)
        let filteredColumnCells = [];
        Object.values(cellsByColumn).forEach(columnCells => {
            // Sort cells by Y position (top to bottom)
            columnCells.sort((a, b) => a.minY - b.minY);

            // Skip the first cell (header) if there are multiple cells in the column
            if (columnCells.length > 1) {
                filteredColumnCells = filteredColumnCells.concat(columnCells.slice(1));
            } else {
                // If there's only one cell, keep it (edge case)
                filteredColumnCells = filteredColumnCells.concat(columnCells);
            }
        });

        // 9. Group cells by row for better organization
        const cellsByRow = {};
        // Use a tolerance that scales with zoom factor for row grouping
        const rowTolerance = 0.001 * zoomFactor; // Scale the tolerance with zoom factor
        filteredColumnCells.forEach(cell => {
            // Round to nearest multiple of tolerance to group rows
            const rowKey = Math.round(cell.minY / rowTolerance) * rowTolerance;
            const rowKeyStr = rowKey.toString();
            if (!cellsByRow[rowKeyStr]) {
                cellsByRow[rowKeyStr] = [];
            }
            cellsByRow[rowKeyStr].push(cell);
        });

        // 10. Sort rows by Y position (top to bottom)
        const sortedRows = Object.keys(cellsByRow)
            .sort((a, b) => parseInt(a) - parseInt(b))
            .map(key => cellsByRow[key]);

        // 11. Compute aggregate top and bottom
        let top = Infinity, bottom = -Infinity;
        selectedColumnCells.forEach(i => {
            top = Math.min(top, i.minY);
            bottom = Math.max(bottom, i.maxY);
        });

        // 12. Create individual column data for each detected column
        const columns = columnBoundaries.map(column => {
            // Find cells that belong to this specific column
            const columnCells = selectedColumnCells.filter(cell => {
                const cellCenterX = (cell.minX + cell.maxX) / 2;
                return cellCenterX >= column.minX && cellCenterX <= column.maxX;
            });

            // Calculate column-specific top and bottom
            let columnTop = Infinity, columnBottom = -Infinity;
            columnCells.forEach(cell => {
                columnTop = Math.min(columnTop, cell.minY);
                columnBottom = Math.max(columnBottom, cell.maxY);
            });

            // Return column data
            return {
                left: column.minX,
                top: columnTop,
                width: column.maxX - column.minX,
                height: columnBottom - columnTop,
                cells: columnCells
            };
        });

        // 13. Return the normalized column bounding box with additional data
        return {
            // Overall bounding box that encompasses all columns
            left: expandedRect.minX,
            top,
            width: expandedRect.maxX - expandedRect.minX,
            height: bottom - top,

            // Additional data
            expandedRect,
            columnBoundaries,
            selectedColumnCells,
            filteredColumnCells,
            sortedRows,
            tableBounds,

            // Individual column data
            columns
        };
    }

    /**
     * Find column boundaries for a set of cells
     * @private
     * @param {Array} cells - Array of cells to analyze
     * @param {number} page - Optional page number to filter results
     * @param {number} overlapRatio - The minimum overlap ratio required (default: 0.5)
     * @param {number} zoomFactor - Optional zoom factor (zoom/100) to scale tolerances
     * @returns {Array} - Array of column boundaries
     */
    _findColumnBoundaries(cells, page = null, overlapRatio = 0.5, zoomFactor = 1) {
        if (!cells || cells.length === 0) return [];

        // Filter cells by page number
        const pageCells = page !== null ? cells.filter(item => item.block.page === page) : cells;
        if (pageCells.length === 0) return [];

        // Extract X-axis boundaries for each cell
        const columnBoundaries = pageCells.map(item => ({
            minX: item.minX,
            maxX: item.maxX
        }));

        // Group overlapping columns
        const mergedColumns = [];
        // Use overlapRatio as a base for the column overlap threshold
        // For column merging, we typically want a lower threshold than for cell selection
        // Scale the threshold with the zoom factor
        const overlapThreshold = Math.min(overlapRatio, 0.3) * zoomFactor; // Scale with zoom factor

        // Sort by minX
        columnBoundaries.sort((a, b) => a.minX - b.minX);

        if (columnBoundaries.length === 0) return [];

        // Merge overlapping columns
        let currentColumn = columnBoundaries[0];

        for (let i = 1; i < columnBoundaries.length; i++) {
            const nextColumn = columnBoundaries[i];

            // Calculate overlap
            const overlapStart = Math.max(currentColumn.minX, nextColumn.minX);
            const overlapEnd = Math.min(currentColumn.maxX, nextColumn.maxX);
            const overlapWidth = Math.max(0, overlapEnd - overlapStart);

            // Calculate column widths
            const currentWidth = currentColumn.maxX - currentColumn.minX;
            const nextWidth = nextColumn.maxX - nextColumn.minX;

            // Check if overlap is significant
            if (overlapWidth / currentWidth > overlapThreshold || overlapWidth / nextWidth > overlapThreshold) {
                // Merge columns
                currentColumn = {
                    minX: Math.min(currentColumn.minX, nextColumn.minX),
                    maxX: Math.max(currentColumn.maxX, nextColumn.maxX)
                };
            } else {
                // Add current column and start a new one
                mergedColumns.push(currentColumn);
                currentColumn = nextColumn;
            }
        }

        // Add the last column
        mergedColumns.push(currentColumn);

        return mergedColumns;
    }

    /**
     * Find the table bounds that contain a cell
     * @private
     * @param {Object} cell - The cell to find the table for
     * @param {number} page - Optional page number to filter results
     * @returns {Object} - The table bounds
     */
    _findTableBounds(cell, page = null) {
        if (!cell) return null;

        // Calculate the center point of the cell
        const cellCenterX = (cell.minX + cell.maxX) / 2;
        const cellCenterY = (cell.minY + cell.maxY) / 2;

        // Find all table blocks on the page
        const tableBlocks = this.rbush.search({
            minX: 0,
            minY: 0,
            maxX: 1,
            maxY: 1
        }).filter(item =>
            (page === null || item.block.page === page) &&
            item.block.type === 'TABLE'
        );

        // Find which table contains our cell
        for (const tableBlock of tableBlocks) {
            if (
                cellCenterX >= tableBlock.minX &&
                cellCenterX <= tableBlock.maxX &&
                cellCenterY >= tableBlock.minY &&
                cellCenterY <= tableBlock.maxY
            ) {
                return {
                    minX: tableBlock.minX,
                    minY: tableBlock.minY,
                    maxX: tableBlock.maxX,
                    maxY: tableBlock.maxY
                };
            }
        }

        // If we couldn't find a specific table, try to infer table boundaries from the cells
        // Find cells that are aligned with the selected cell (likely in the same table)
        const selectionMinY = cell.minY;
        const selectionMaxY = cell.maxY;

        // Find all cells that might be in the same table based on Y-position overlap
        const potentialTableCells = this.rbush.search({
            minX: 0,
            minY: selectionMinY - 0.1, // Add some margin
            maxX: 1,
            maxY: selectionMaxY + 0.1 // Add some margin
        }).filter(item =>
            (page === null || item.block.page === page) &&
            item.block.type === 'CELL'
        );

        // Group cells by their vertical alignment to identify rows
        const rowGroups = {};
        potentialTableCells.forEach(cell => {
            // Use the cell's center Y as the row identifier with some tolerance
            const rowKey = Math.round(((cell.minY + cell.maxY) / 2) * 100);
            if (!rowGroups[rowKey]) {
                rowGroups[rowKey] = [];
            }
            rowGroups[rowKey].push(cell);
        });

        // Find cells that are in the same row as our selected cell
        const selectedRowKey = Math.round(((cell.minY + cell.maxY) / 2) * 100);
        const tableRow = rowGroups[selectedRowKey] || [];

        if (tableRow.length > 0) {
            // Find the table boundaries based on this row
            // Expand search to find cells above and below this row
            const rowMinX = Math.min(...tableRow.map(cell => cell.minX));
            const rowMaxX = Math.max(...tableRow.map(cell => cell.maxX));

            // Search for cells that might be in the same table
            const tableAreaCells = this.rbush.search({
                minX: rowMinX - 0.05,
                minY: 0,
                maxX: rowMaxX + 0.05,
                maxY: 1
            }).filter(item =>
                (page === null || item.block.page === page) &&
                item.block.type === 'CELL'
            );

            if (tableAreaCells.length > 0) {
                return {
                    minX: Math.min(...tableAreaCells.map(cell => cell.minX)),
                    minY: Math.min(...tableAreaCells.map(cell => cell.minY)),
                    maxX: Math.max(...tableAreaCells.map(cell => cell.maxX)),
                    maxY: Math.max(...tableAreaCells.map(cell => cell.maxY))
                };
            }
        }

        // If we still couldn't determine table boundaries, use a reasonable default
        // based on the cell with some margins
        return {
            minX: cell.minX - 0.05,
            minY: cell.minY - 0.05,
            maxX: cell.maxX + 0.05,
            maxY: cell.maxY + 0.2 // Add more margin below
        };
    }
}

// Create a factory function to create new instances
export default function createTextractRBush() {
    return new TextractRBush();
}
