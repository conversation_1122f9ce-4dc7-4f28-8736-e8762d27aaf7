/**
 * PdfjsSpatialIndex - A library for creating a spatial index from PDF.js text content
 * and querying text by coordinates in a normalized 0–1 space.
 */

export default class PdfjsSpatialIndex {
    constructor() {
      this.pdfDoc = null;
      this.items = [];
      this.initialized = false;
    }
  
    /**
     * Initialize the index by extracting text items from each page via PDF.js
     * @param {Object} pdfDoc - The loaded PDF.js document instance
     * @returns {Promise<PdfjsSpatialIndex>} - The instance for chaining
     */
    async initialize(pdfDoc) {
      if (!pdfDoc || typeof pdfDoc.getPage !== 'function') {
        throw new Error('Invalid PDF document: must be a PDF.js Document');
      }
      this.pdfDoc = pdfDoc;
      this.items = [];
  
      const numPages = pdfDoc.numPages;
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const viewport = page.getViewport({ scale: 1 });
        const textContent = await page.getTextContent();
  
        textContent.items.forEach(item => {
          const [a, b, c, d, e, f] = item.transform;
          // transform: [scaleX, skewX, skewY, scaleY, translateX, translateY]
          const width = item.width;
          const height = item.height;
          // PDF.js origin is bottom-left; normalize to 0–1 top-left origin
          const x = e / viewport.width;
          const y = (viewport.height - f) / viewport.height;
          const w = width / viewport.width;
          const h = height / viewport.height;
  
          this.items.push({
            page: pageNum,
            text: item.str,
            boundingBox: { left: x, top: y, width: w, height: h }
          });
        });
      }
  
      this.initialized = true;
      return this;
    }
  
    /**
     * Query indexed text items by normalized rectangle
     * @param {Object} rect - { left, top, width, height }
     * @param {number} overlapRatio - 0–1 minimum overlap
     * @param {Object} pageSize - { width, height } in pixels
     * @param {number|null} page - optional page filter
     * @param {number} rotation - 0/90/180/270 rotation
     * @param {number} zoomFactor - zoom scale multiplier
     * @returns {Array} - merged text items
     */
    query(rect, overlapRatio = 0.5, pageSize, page = null, rotation = 0, zoomFactor = 1) {
      if (!this.initialized) {
        throw new Error('Not initialized: call initialize() first');
      }
  
      const candidates = this.items.filter(item => {
        if (page !== null && item.page !== page) return false;
        const inter = this._calculateIntersectionArea(
          { minX: rect.left, minY: rect.top, maxX: rect.left + rect.width, maxY: rect.top + rect.height },
          { minX: item.boundingBox.left, minY: item.boundingBox.top, maxX: item.boundingBox.left + item.boundingBox.width, maxY: item.boundingBox.top + item.boundingBox.height }
        );
        const area = item.boundingBox.width * item.boundingBox.height;
        item.overlapPercentage = area > 0 ? inter / area : 0;
        return item.overlapPercentage >= overlapRatio;
      });
  
      const transformed = candidates.map(item => {
        const tb = this._transformBoundingBox(item.boundingBox, rotation);
        return {
          text: item.text,
          page: item.page,
          boundingBox: {
            x: tb.left * pageSize.width,
            y: tb.top * pageSize.height,
            width: tb.width * pageSize.width,
            height: tb.height * pageSize.height
          },
          overlapPercentage: item.overlapPercentage
        };
      }).sort((a, b) => a.boundingBox.y - b.boundingBox.y);
  
      return this._mergeCells(transformed, 3 * zoomFactor);
    }
  
    /**
     * Get column bounding boxes under a selection rectangle
     * (simplified grouping by x-centers)
     */
    getColumnBoundingBox(x, y, width, height, overlapRatio = 0.5, page = null, zoomFactor = 1) {
      const matches = this.query({ left: x, top: y, width, height }, overlapRatio, { width: 1, height: 1 }, page, 0, zoomFactor);
      if (matches.length === 0) return null;
  
      // group by approximate center X
      const cols = {};
      matches.forEach(item => {
        const centerX = Math.round((item.boundingBox.x + item.boundingBox.width / 2) * 1000) / 1000;
        if (!cols[centerX]) cols[centerX] = [];
        cols[centerX].push(item);
      });
  
      const columnData = Object.keys(cols).sort().map(key => {
        const cells = cols[key];
        const top = Math.min(...cells.map(c => c.boundingBox.y));
        const bottom = Math.max(...cells.map(c => c.boundingBox.y + c.boundingBox.height));
        const left = Math.min(...cells.map(c => c.boundingBox.x));
        const right = Math.max(...cells.map(c => c.boundingBox.x + c.boundingBox.width));
        return { left, top, width: right - left, height: bottom - top, cells };
      });
  
      // overall bounding box
      const allLeft = Math.min(...columnData.map(c => c.left));
      const allTop = Math.min(...columnData.map(c => c.top));
      const allRight = Math.max(...columnData.map(c => c.left + c.width));
      const allBottom = Math.max(...columnData.map(c => c.top + c.height));
  
      return { left: allLeft, top: allTop, width: allRight - allLeft, height: allBottom - allTop, columns: columnData };
    }
  
    _calculateIntersectionArea(r1, r2) {
      const xOverlap = Math.max(0, Math.min(r1.maxX, r2.maxX) - Math.max(r1.minX, r2.minX));
      const yOverlap = Math.max(0, Math.min(r1.maxY, r2.maxY) - Math.max(r1.minY, r2.minY));
      return xOverlap * yOverlap;
    }
  
    _transformBoundingBox({ left, top, width, height }, rotation) {
      const rot = ((rotation % 360) + 360) % 360;
      let nl = left, nt = top;
      switch (rot) {
        case 0: break;
        case 90: nl = 1 - top - height; nt = left; break;
        case 180: nl = 1 - left - width; nt = 1 - top - height; break;
        case 270: nl = top; nt = 1 - left - width; break;
        default: throw new Error('Rotation must be multiple of 90');
      }
      return { left: nl, top: nt, width, height };
    }
  
    _mergeCells(items, tolerance = 2) {
      if (!items.length) return [];
      const merged = [];
      let row = [items[0]];
      for (let i = 1; i < items.length; i++) {
        const dy = Math.abs(items[i].boundingBox.y - items[i-1].boundingBox.y);
        if (dy <= tolerance) {
          row.push(items[i]);
        } else {
          merged.push(this._collapseRow(row)); row = [items[i]];
        }
      }
      if (row.length) merged.push(this._collapseRow(row));
      return merged;
    }
  
    _collapseRow(row) {
      row.sort((a, b) => a.boundingBox.x - b.boundingBox.x);
      const x = row[0].boundingBox.x;
      const y = row[0].boundingBox.y;
      const h = row[0].boundingBox.height;
      let w = 0, text = '', ov = 0;
      row.forEach(cell => {
        text += cell.text + ' ';
        w += cell.boundingBox.width;
        ov += cell.overlapPercentage || 0;
      });
      return { text: text.trim(), boundingBox: { x, y, width: w, height: h }, overlapPercentage: ov / row.length };
    }
  }
  