import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import * as pdfjs from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.min?url'
import { v4 as uuidv4 } from 'uuid';
import './styles/App.css';
// Import box types configuration
import {
  BOX_TYPES,
  getBoxStyles,
  createEmptyDataArrays,
} from './config/boxTypes';
// Import TextractRBush
import TextractRBush from './textract-rbush';

pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
// pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`
function logBox(...args) {
}
function logPdf(...args) {
}
const logRender=logBox
const logError=logBox
const  logWarning = logBox
// Props:
// - pdfFile: The PDF file to display
// - onError: Function to handle errors
// - onBoxesChange: Function to notify parent of boxes state
// - onExtractedDataChange: Function to notify parent of extracted data state
// - currentBoxType: The current box type selected in the parent
const PdfTextExtractor = React.forwardRef((props, ref) => {
  const {
    pdfFile: propsPdfFile,
    onError,
    onBoxesChange,
    onExtractedDataChange,
    currentBoxType: parentBoxType,
    snapToGrid = true, // Always true now
    gridOpacity = 0.7, // Default grid opacity
    showMagnifyingGlass: parentShowMagnifyingGlass = true,
  } = props;

  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.5);
  const [boxes, setBoxes] = useState([]);
  const [currentBoxType, setCurrentBoxType] = useState('description');
  const [isDrawing, setIsDrawing] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null); // 'tl', 'tr', 'bl', 'br', 'left', 'right', 'top', 'bottom'
  const [resizingBoxId, setResizingBoxId] = useState(null);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [currentRect, setCurrentRect] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [hoveredBox, setHoveredBox] = useState(null);
  const [extractedData, setExtractedData] = useState({});
  const [allExtractedData, setAllExtractedData] = useState({});
  const [debugMode, setDebugMode] = useState(false);
  const [textPositions, setTextPositions] = useState([]);
  const [hasExtractedData, setHasExtractedData] = useState(false);
  const [rotation, setRotation] = useState(0); // 0, 90, 180, 270 degrees
  const [pageRotations, setPageRotations] = useState({});

  const pdfContainerRef = useRef(null);
  const canvasRef = useRef(null);
  const overlayCanvasRef = useRef(null);
  const renderTaskRef = useRef(null);
  // Use the box styles from our configuration
  const boxStyles = getBoxStyles();
  const [zoomPercentage, setZoomPercentage] = useState(100);
  const zoomIncrement = 10;
  const minZoom = 20;
  const maxZoom = 200;

  const regExs = {
    // New box types
    quantity: /^([^ ]+)/g,
    specification: /^([^ ]+)/g,
    grade: /^([^ ]+)/g,
    weight_per_quantity:/\b(?:(?:\d{1,3}(?:,\d{3})+)(?:\.\d+)?|\d+\.\d+)\b/g,//extract numbers with commas and decimals
    // Legacy box types for backward compatibility
    Qty: /^([^ ]+)/g,
    'Spec-Grade': /^([^ ]+)/g,

  };

  // Ref to track PDF rendering state
  const renderRef = useRef({
    isFirstRender: true,
    lastPageNumber: null,
    lastScale: null,
    lastRotation: null,
    lastPdfFile: null,
    lastZoom:100,
  });

  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });


  // Render PDF page onto canvas
  useEffect(() => {
    const renderPage = async () => {
      // Check if this is a significant change that warrants logging
      const currentRotation = pageRotations[`page${pageNumber}`] || 0;
      const isSignificantChange =
        renderRef.current.isFirstRender ||
        renderRef.current.lastPageNumber !== pageNumber ||
        renderRef.current.lastScale !== scale ||
        renderRef.current.lastRotation !== currentRotation ||
        renderRef.current.lastZoom !== zoomPercentage ||
        renderRef.current.lastPdfFile !== pdfFile;

      // Update the ref values
      renderRef.current.lastPageNumber = pageNumber;
      renderRef.current.lastScale = scale;
      renderRef.current.lastRotation = currentRotation;
      renderRef.current.lastPdfFile = pdfFile;
      renderRef.current.lastZoom = zoomPercentage;

      if (isSignificantChange) {
        const renderStartTime = performance.now();
        logRender('PDF rendering process started', {
          pageNumber,
          scale,
          rotation: currentRotation,
          pdfFilename: pdfFile?.name,
          pdfFileSize: pdfFile?.size,
          isFirstRender: renderRef.current.isFirstRender,
        });

        // Set first render to false after logging
        renderRef.current.isFirstRender = false;
      }

      // Cancel any ongoing render task
      if (renderTaskRef.current) {
        if (isSignificantChange) {
          logRender('Cancelling previous render task');
        }
        try {
          renderTaskRef.current.cancel();
        } catch (error) {
          logWarning('Error cancelling previous render task', {
            errorMessage: error.message,
            errorName: error.name,
          });
        }
        renderTaskRef.current = null;
      }

      if (!pdfFile) {
        if (isSignificantChange) {
          logRender('No PDF file available, clearing canvas');
        }
        const canvas = canvasRef.current;
        if (!canvas) return;

        // Ensure canvas has dimensions before clearing
        if (canvas.width === 0 || canvas.height === 0) {
          canvas.width = 800; // Default width
          canvas.height = 600; // Default height
          if (isSignificantChange) {
            logRender('Canvas initialized with default dimensions', {
              width: 800,
              height: 600,
            });
          }
        }

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Also clear overlay canvas
        const overlayCanvas = overlayCanvasRef.current;
        if (overlayCanvas) {
          overlayCanvas.width = canvas.width;
          overlayCanvas.height = canvas.height;
          setCanvasSize({ width: overlayCanvas.width, height: overlayCanvas.height });
          const overlayCtx = overlayCanvas.getContext('2d');
          overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
          if (isSignificantChange) {
            logRender('Overlay canvas cleared');
          }
        }

        return; // Exit early if no PDF
      }

      if (!pdfFile || !canvasRef.current) return;

      try {
        let renderStartTime;
        if (isSignificantChange) {
          renderStartTime = performance.now();
          logRender('Creating PDF array buffer');
        }

        const arrayBuffer = await pdfFile.arrayBuffer();

        if (isSignificantChange) {
          logRender('PDF array buffer created', {
            bufferSizeBytes: arrayBuffer.byteLength,
          });
        }

        if (isSignificantChange) {
          logRender('Loading PDF document from array buffer');
        }
        const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;

        setNumPages(pdf.numPages);
        if (isSignificantChange) {
          logRender('PDF document loaded', {
            numPages: pdf.numPages,
            fingerprint: pdf.fingerprint,
            pdfVersion: pdf._pdfInfo?.version,
            isEncrypted: pdf._pdfInfo?.encrypted,
          });
        }

        if (isSignificantChange) {
          logRender(`Loading page ${pageNumber}`);
        }
        const page = await pdf.getPage(pageNumber);

        // Get the current rotation for this page
        const currentPageRotation = pageRotations[`page${pageNumber}`] || 0;

        if (isSignificantChange) {
          logRender(`Page ${pageNumber} loaded`, {
            pageIndex: page._pageIndex,
            pageSize: {
              width: page.view[2],
              height: page.view[3],
            },
            rotation: currentPageRotation,
          });
        }

        // Apply rotation to viewport
        const viewport = page.getViewport({
          scale: scale * (zoomPercentage / 100),
          rotation: currentPageRotation,
        });

        if (isSignificantChange) {
          logRender('Viewport created', {
            width: viewport.width,
            height: viewport.height,
            scale,
            rotation: currentPageRotation,
          });
        }

        const canvas = canvasRef.current;
        if (!canvas) {
          logError('Canvas reference is null, cannot render PDF');
          return;
        }

        // Create a new canvas element for rendering
        // This avoids the "Cannot use the same canvas during multiple render() operations" error
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = viewport.width;
        tempCanvas.height = viewport.height;

        // Set canvas dimensions after creating the temp canvas
        canvas.width = viewport.width;
        canvas.height = viewport.height;

        // Update overlay canvas dimensions and trigger redraw
        const overlayCanvas = overlayCanvasRef.current;
        if (overlayCanvas) {
          overlayCanvas.width = viewport.width;
          overlayCanvas.height = viewport.height;

          // Update canvas size state to trigger redraw of boxes and grid
          setCanvasSize({
            width: viewport.width,
            height: viewport.height
          });
        }

        if (isSignificantChange) {
          logRender('Canvas dimensions set', {
            width: viewport.width,
            height: viewport.height,
          });
        }

        // Set the width of the pdf-content div to match the canvas width
        if (pdfContainerRef.current) {
          const contentDiv =
            pdfContainerRef.current.querySelector(styles.pdfContent);
          if (contentDiv) {
            contentDiv.style.width = `${viewport.width}px`;
            if (isSignificantChange) {
              logRender('PDF content container width adjusted', {
                width: viewport.width,
              });
            }
          }
        }

        const tempCtx = tempCanvas.getContext('2d');
        if (isSignificantChange) {
          logRender('Starting PDF page rendering to temporary canvas');
        }

        // Store the render task in the ref so we can cancel it if needed
        const renderTask = page.render({ canvasContext: tempCtx, viewport });
        renderTaskRef.current = renderTask;

        await renderTask.promise;

        if (isSignificantChange) {
          logRender('PDF page rendering completed');
        }

        // Copy the rendered content to the main canvas
        const ctx = canvas.getContext('2d');
        ctx.drawImage(tempCanvas, 0, 0);

        if (isSignificantChange) {
          logRender('Rendered content copied to main canvas');
        }

        // Clear the reference since rendering is complete
        renderTaskRef.current = null;

        if (isSignificantChange && renderStartTime) {
          const totalRenderTime = performance.now() - renderStartTime;
          logRender('PDF rendering process completed', {
            totalTimeMs: Math.round(totalRenderTime),
            pageNumber,
            scale,
          });
        }
      } catch (error) {
        // Check if this is a cancelled render task
        if (error.name === 'RenderingCancelledException') {
          if (isSignificantChange) {
            logRender('Rendering was cancelled', {
              reason: 'User initiated or component update',
            });
          }
        } else {
          logError('Error rendering PDF', {
            errorMessage: error.message,
            errorName: error.name,
            errorStack: error.stack,
            pageNumber,
            scale,
          });
          setErrorMessage('Error rendering PDF. Please try again.');
        }
      }
    };

    renderPage();

    // Cleanup function to cancel any ongoing render task when the component unmounts
    // or when the dependencies change
    return () => {
      if (renderTaskRef.current) {
        // Only log on component unmount, not on every dependency change
        const isUnmounting = !document.body.contains(canvasRef.current);
        if (isUnmounting) {
          logRender('Cleanup: Cancelling render task on unmount');
        }

        try {
          renderTaskRef.current.cancel();
        } catch (error) {
          if (isUnmounting) {
            logWarning('Error cancelling render task during cleanup', {
              errorMessage: error.message,
              errorName: error.name,
            });
          }
        }
        renderTaskRef.current = null;
      }
    };
  }, [
    pdfFile,
    pageNumber,
    scale,
    pageRotations,
    logRender,
    logError,
    logWarning,
    zoomPercentage,
  ]);

  // PDF loading is now handled by the parent component

  // Grid size constant (6px)
  const GRID_SIZE = 6;

  // Function to draw grid on the overlay canvas
  const drawGrid = (ctx, canvasWidth, canvasHeight) => {
    // Always draw grid (snapToGrid is always true now)
    if (!canvasWidth || !canvasHeight) {
      logWarning('Cannot draw grid: Invalid canvas dimensions', {
        canvasWidth,
        canvasHeight
      });
      return;
    }

    ctx.save();
    ctx.strokeStyle = `rgba(200, 200, 200, ${gridOpacity})`; // Light grey with configurable opacity
    ctx.lineWidth = 0.5;

    // Use fixed grid size that doesn't scale with zoom
    const gridSize = GRID_SIZE;

    // Draw vertical lines every GRID_SIZE pixels
    for (let x = 0; x <= canvasWidth; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvasHeight);
      ctx.stroke();
    }

    // Draw horizontal lines every GRID_SIZE pixels
    for (let y = 0; y <= canvasHeight; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvasWidth, y);
      ctx.stroke();
    }

    ctx.restore();

    // Log grid drawing only occasionally to avoid flooding logs
    if (Math.random() < 0.1) {
      logRender('Grid drawn', {
        canvasWidth,
        canvasHeight,
        gridSize,
        gridOpacity
      });
    }
  };

  // Function to snap a value to the grid
  const snapToGridValue = (value) => {
    if (!snapToGrid) return value;
    return Math.round(value / GRID_SIZE) * GRID_SIZE;
  };

  // Function to check if a point is inside a circle
  const isPointInCircle = (x, y, cx, cy, radius) => {
    const dx = x - cx;
    const dy = y - cy;
    return dx * dx + dy * dy <= radius * radius;
  };

  // Function to check if a point is inside a rectangle
  const isPointInRect = (x, y, rx, ry, rw, rh) => {
    return x >= rx && x <= rx + rw && y >= ry && y <= ry + rh;
  };

  // Function to check if mouse is over a resize handle or delete button
  const getResizeHandleAtPoint = (x, y, box) => {
    if (!box || !box.rect) return null;

    const { rect } = box;
    const handleRadius = 4; // Radius of corner circles (reduced by half)
    const edgeHandleWidth = 20; // Doubled from 10px
    const edgeHandleHeight = 8; // Doubled from 4px

    // Scale the box coordinates
    const boxX = rect.x * (scale*zoomPercentage/100);
    const boxY = rect.y * (scale*zoomPercentage/100);
    const boxWidth = rect.width * (scale*zoomPercentage/100);
    const boxHeight = rect.height * (scale*zoomPercentage/100);

    // Check if point is in delete button (top-right corner)
    const deleteButtonSize = 16;
    const padding = 4;
    const deleteButtonX = boxX + boxWidth - deleteButtonSize/2 - padding;
    const deleteButtonY = boxY + deleteButtonSize/2 + padding;

    if (isPointInCircle(x, y, deleteButtonX, deleteButtonY, deleteButtonSize/2)) {
      return 'delete';
    }

    // Check corner circles (top-left, top-right, bottom-left, bottom-right)
    if (isPointInCircle(x, y, boxX, boxY, handleRadius)) {
      return 'tl'; // top-left
    }
    if (isPointInCircle(x, y, boxX + boxWidth, boxY, handleRadius)) {
      return 'tr'; // top-right
    }
    if (isPointInCircle(x, y, boxX, boxY + boxHeight, handleRadius)) {
      return 'bl'; // bottom-left
    }
    if (isPointInCircle(x, y, boxX + boxWidth, boxY + boxHeight, handleRadius)) {
      return 'br'; // bottom-right
    }

    // Check edge handles (middle of each edge)
    // Left edge
    if (isPointInRect(
      x, y,
      boxX - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    )) {
      return 'left';
    }

    // Right edge
    if (isPointInRect(
      x, y,
      boxX + boxWidth - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    )) {
      return 'right';
    }

    // Top edge
    if (isPointInRect(
      x, y,
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    )) {
      return 'top';
    }

    // Bottom edge
    if (isPointInRect(
      x, y,
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY + boxHeight - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    )) {
      return 'bottom';
    }

    return null;
  };

  // Function to draw resize handles for a box
  const drawResizeHandles = (ctx, rect, isHovered) => {
    const handleRadius = 4; // Radius of corner circles (reduced by half)
    const edgeHandleWidth = 20; // Doubled from 10px
    const edgeHandleHeight = 8; // Doubled from 4px

    // Scale the box coordinates
    const boxX = rect.x * (scale*zoomPercentage/100);
    const boxY = rect.y * (scale*zoomPercentage/100);
    const boxWidth = rect.width * (scale*zoomPercentage/100);
    const boxHeight = rect.height * (scale*zoomPercentage/100);

    // Only draw handles if the box is hovered
    if (!isHovered) return;

    // Draw delete button (X) in top-right corner
    const deleteButtonSize = 16;
    const padding = 4;

    // Draw button background
    ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
    ctx.beginPath();
    ctx.arc(
      boxX + boxWidth - deleteButtonSize/2 - padding,
      boxY + deleteButtonSize/2 + padding,
      deleteButtonSize/2,
      0,
      2 * Math.PI
    );
    ctx.fill();

    // Draw X
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    const offset = deleteButtonSize/4;

    // Draw X lines
    ctx.beginPath();
    ctx.moveTo(
      boxX + boxWidth - deleteButtonSize - padding + offset,
      boxY + padding + offset
    );
    ctx.lineTo(
      boxX + boxWidth - padding - offset,
      boxY + deleteButtonSize - padding - offset
    );
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(
      boxX + boxWidth - padding - offset,
      boxY + padding + offset
    );
    ctx.lineTo(
      boxX + boxWidth - deleteButtonSize - padding + offset,
      boxY + deleteButtonSize - padding - offset
    );
    ctx.stroke();

    // Draw corner circles
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.lineWidth = 1;

    // Top-left corner
    ctx.beginPath();
    ctx.arc(boxX, boxY, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Top-right corner
    ctx.beginPath();
    ctx.arc(boxX + boxWidth, boxY, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Bottom-left corner
    ctx.beginPath();
    ctx.arc(boxX, boxY + boxHeight, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Bottom-right corner
    ctx.beginPath();
    ctx.arc(boxX + boxWidth, boxY + boxHeight, handleRadius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Draw edge handles
    // Left edge
    ctx.fillRect(
      boxX - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );
    ctx.strokeRect(
      boxX - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );

    // Right edge
    ctx.fillRect(
      boxX + boxWidth - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );
    ctx.strokeRect(
      boxX + boxWidth - edgeHandleHeight / 2,
      boxY + boxHeight / 2 - edgeHandleWidth / 2,
      edgeHandleHeight,
      edgeHandleWidth
    );

    // Top edge
    ctx.fillRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );
    ctx.strokeRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );

    // Bottom edge
    ctx.fillRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY + boxHeight - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );
    ctx.strokeRect(
      boxX + boxWidth / 2 - edgeHandleWidth / 2,
      boxY + boxHeight - edgeHandleHeight / 2,
      edgeHandleWidth,
      edgeHandleHeight
    );
  };

  // Ref to track overlay drawing state
  const overlayRenderRef = useRef({
    isFirstRender: true,
    lastCanvas: null,
    lastPdfCanvas: null,
  });

  // Draw boxes & debug overlay
  useEffect(() => {
    const drawOverlay = () => {
      const canvas = overlayCanvasRef.current;
      const pdfCanvas = canvasRef.current;

      if (!canvas || !pdfContainerRef.current || !pdfCanvas) {
        // Only log this on the first occurrence
        if (overlayRenderRef.current.isFirstRender) {
          logRender('Skipping overlay drawing - missing canvas references', {
            overlayCanvasAvailable: !!canvas,
            pdfContainerAvailable: !!pdfContainerRef.current,
            pdfCanvasAvailable: !!pdfCanvas,
          });
        }
        return;
      }

      // Check if PDF canvas dimensions have changed
      if (canvas.width !== pdfCanvas.width || canvas.height !== pdfCanvas.height) {
        logRender('Overlay canvas dimensions updated to match PDF canvas', {
          oldWidth: canvas.width,
          oldHeight: canvas.height,
          newWidth: pdfCanvas.width,
          newHeight: pdfCanvas.height
        });

        // Set overlay canvas dimensions to match PDF canvas
        canvas.width = pdfCanvas.width;
        canvas.height = pdfCanvas.height;

        // Update canvas size state
        setCanvasSize({
          width: canvas.width,
          height: canvas.height
        });
      }

      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Only log on first render or significant changes
      if (overlayRenderRef.current.isFirstRender) {
        logRender('Drawing overlay canvas', {
          canvasWidth: canvas.width,
          canvasHeight: canvas.height,
          boxCount: boxes?.length || 0,
          hasCurrentRect: !!currentRect,
          debugMode,
          textPositionsCount: textPositions.length,
          scale,
          snapToGrid,
        });

        // Set first render to false after logging
        overlayRenderRef.current.isFirstRender = false;
      }

      // Always draw grid (snapToGrid is always true now)
      drawGrid(ctx, canvas.width, canvas.height);

      // Draw boxes
      if (boxes && boxes.length > 0) {
        boxes.forEach(({ type, rect, id }) => {
          const style = boxStyles[type];
          const isBoxHovered = hoveredBox === id;
          const isBoxResizing = resizingBoxId === id;

          ctx.fillStyle = style.fill;
          ctx.strokeStyle = style.stroke;
          ctx.lineWidth = 2;
          ctx.fillRect(
            rect.x * (scale*zoomPercentage/100),
            rect.y * (scale*zoomPercentage/100),
            rect.width * (scale*zoomPercentage/100),
            rect.height * (scale*zoomPercentage/100)
          );
          ctx.strokeRect(
            rect.x * (scale*zoomPercentage/100),
            rect.y * (scale*zoomPercentage/100),
            rect.width * (scale*zoomPercentage/100),
            rect.height * (scale*zoomPercentage/100)
          );

          // Draw resize handles if box is hovered or being resized
          if (isBoxHovered || isBoxResizing) {
            drawResizeHandles(ctx, rect, true);
          }
        });
      }

      // Draw current rectangle being drawn
      if (currentRect) {
        const style = boxStyles[currentBoxType];
        ctx.fillStyle = style.fill;
        ctx.strokeStyle = style.stroke;
        ctx.lineWidth = 2;
        ctx.fillRect(
          currentRect.x * (scale*zoomPercentage/100),
          currentRect.y * (scale*zoomPercentage/100),
          currentRect.width * (scale*zoomPercentage/100),
          currentRect.height * (scale*zoomPercentage/100)
        );
        ctx.strokeRect(
          currentRect.x * (scale*zoomPercentage/100),
          currentRect.y * (scale*zoomPercentage/100),
          currentRect.width * (scale*zoomPercentage/100),
          currentRect.height * (scale*zoomPercentage/100)
        );
      }

      // Draw debug text positions if debug mode is enabled
      if (debugMode && textPositions.length > 0) {
        textPositions.forEach((pos) => {
          ctx.fillStyle = 'rgba(255,0,255,0.5)';
          ctx.beginPath();
          ctx.arc(pos.x, pos.y, 4, 0, 2 * Math.PI);
          ctx.fill();
          ctx.fillStyle = '#000000';
          ctx.font = '10px sans-serif';
          ctx.fillText(pos.text.slice(0, 5), pos.x + 5, pos.y + 5);
        });
      }
    };

    drawOverlay();
  }, [
    boxes,
    currentRect,
    debugMode,
    textPositions,
    scale,
    snapToGrid,
    gridOpacity, // Add dependency on grid opacity to redraw when it changes
    logRender,
    zoomPercentage,
    canvasSize, // Add dependency on zoom percentage to redraw when zoom changes
  ]);

  // Convert mouse event to canvas coords, accounting for scrolling and centering
  const toCanvasCoords = (e) => {
    const container = pdfContainerRef.current;

    // Get the content div that contains the canvas
    const contentDiv = container.querySelector(styles.pdfContent);
    if (!contentDiv) {
      console.error('PDF content div not found');
      return { x: 0, y: 0 };
    }

    // Get the content div's bounding rectangle
    const contentRect = contentDiv.getBoundingClientRect();

    // Account for horizontal scrolling
    const scrollLeft = container.scrollLeft;

    // Calculate coordinates relative to the canvas
    return {
      x: e.clientX - contentRect.left + scrollLeft,
      y: e.clientY - contentRect.top,
    };
  };

  // Function to check if a point is inside a box
  const isPointInBox = (x, y, rect) => {
    const boxX = rect.x * (scale*zoomPercentage/100);
    const boxY = rect.y * (scale*zoomPercentage/100);
    const boxWidth = rect.width * (scale*zoomPercentage/100);
    const boxHeight = rect.height * (scale*zoomPercentage/100);

    return x >= boxX && x <= boxX + boxWidth && y >= boxY && y <= boxY + boxHeight;
  };

  // Function to find the box and handle at a point
  const findBoxAndHandleAtPoint = (x, y) => {
    if (!boxes || boxes.length === 0) return { boxId: null, handle: null };

    // Check boxes in reverse order (last drawn on top)
    for (let i = boxes.length - 1; i >= 0; i--) {
      const box = boxes[i];
      if (!box.rect) continue;

      // First check if we're over a resize handle
      const handle = getResizeHandleAtPoint(x, y, box);
      if (handle) {
        return { boxId: box.id, handle };
      }

      // Then check if we're over the box itself
      if (isPointInBox(x, y, box.rect)) {
        return { boxId: box.id, handle: null };
      }
    }

    return { boxId: null, handle: null };
  };

  // Handle mouse move for hover effects
  const handleMouseMoveForHover = (e) => {
    if (isDrawing || isResizing) return; // Skip hover detection during drawing or resizing

    const pt = toCanvasCoords(e);
    const { boxId, handle } = findBoxAndHandleAtPoint(pt.x, pt.y);

    // Update cursor based on what we're hovering over
    if (handle) {
      // Set cursor based on handle type
      let cursor = 'default';
      switch (handle) {
        case 'tl':
        case 'br':
          cursor = 'nwse-resize';
          break;
        case 'tr':
        case 'bl':
          cursor = 'nesw-resize';
          break;
        case 'left':
        case 'right':
          cursor = 'ew-resize';
          break;
        case 'top':
        case 'bottom':
          cursor = 'ns-resize';
          break;
      }
      pdfContainerRef.current.style.cursor = cursor;
    }
    //  else if (boxId) {
    //   // Over a box but not a handle
    //   pdfContainerRef.current.style.cursor = 'move';
    // }
     else {
      // Not over anything
      pdfContainerRef.current.style.cursor = 'default';
    }

    // Update hovered box state
    setHoveredBox(boxId);
  };

  // Mouse handlers
  const handleMouseDown = (e) => {
    if (!pdfFile) {
      logBox('Box drawing attempted but no PDF is loaded');
      return;
    }

    const pt = toCanvasCoords(e);

    // Check if we're clicking on a resize handle or box
    const { boxId, handle } = findBoxAndHandleAtPoint(pt.x, pt.y);

    // If we're clicking on the delete button, remove the box
    if (boxId && handle === 'delete') {
      // Find the box we're deleting
      const boxToDelete = boxes.find(box => box.id === boxId);
      if (!boxToDelete) return;

      // Remove the box
      setBoxes(prev => prev.filter(box => box.id !== boxId));

      // Update extracted data to remove data associated with this box
      // This is a simplified version compared to OCR since the data structure is different
      // We'll need to re-extract text from the remaining boxes

      logBox(`Box removed from page ${pageNumber}`, {
        boxId,
        boxType: boxToDelete.type,
        position: boxToDelete.rect,
        remainingBoxes: boxes.length - 1,
        timestamp: new Date().toISOString(),
      });

      // Re-extract text from remaining boxes
      setTimeout(() => {
        extractText();
      }, 10);

      return;
    }

    // If we're clicking on a resize handle, start resizing
    if (boxId && handle) {
      // Start resizing
      setIsResizing(true);
      setResizingBoxId(boxId);
      setResizeHandle(handle);
      setStartPoint({ x: pt.x, y: pt.y });

      // Find the box we're resizing
      const boxToResize = boxes.find(box => box.id === boxId);
      if (boxToResize) {
        // Store the original rect for reference during resize
        setCurrentRect({ ...boxToResize.rect });
      }

      logBox('Box resize started', {
        boxId,
        handle,
        startPoint: { x: pt.x, y: pt.y },
        pageNumber,
      });

      return;
    }

    // If we're not resizing, proceed with normal box drawing
    logBox('Mouse down event detected', {
      originalCoordinates: { x: e.clientX, y: e.clientY },
      canvasCoordinates: { x: pt.x, y: pt.y },
      pageNumber,
      scale,
    });

    // Snap to grid if enabled
    const x = snapToGrid ? snapToGridValue(pt.x) : pt.x;
    const y = snapToGrid ? snapToGridValue(pt.y) : pt.y;

    if (snapToGrid) {
      logBox('Coordinates snapped to grid', {
        original: { x: pt.x, y: pt.y },
        snapped: { x, y },
        gridSize: GRID_SIZE,
      });
    }

    setIsDrawing(true);
    setStartPoint({ x, y });
    setCurrentRect({ x: x / (scale*zoomPercentage/100), y: y / (scale*zoomPercentage/100), width: 0, height: 0 });

    logBox('Box drawing started', {
      startPoint: { x: x / (scale*zoomPercentage/100), y: y / (scale*zoomPercentage/100) },
      boxType: currentBoxType,
      boxStyle: boxStyles[currentBoxType],
      pageNumber,
    });
  };

  const handleMouseMove = (e) => {
    // Handle hover effects if not drawing or resizing
    if (!isDrawing && !isResizing) {
      handleMouseMoveForHover(e);
      return;
    }

    try {
      const pt = toCanvasCoords(e);

      // Handle resizing
      if (isResizing && resizingBoxId && currentRect) {
        // Find the box we're resizing
        const boxToResize = boxes.find(box => box.id === resizingBoxId);
        if (!boxToResize) return;

        // Snap to grid if enabled
        const x = snapToGrid ? snapToGridValue(pt.x) : pt.x;
        const y = snapToGrid ? snapToGridValue(pt.y) : pt.y;

        // Calculate the new rect based on which handle is being dragged
        let newRect = { ...currentRect };

        switch (resizeHandle) {
          case 'tl': // Top-left
            newRect = {
              x: Math.min(x / (scale*zoomPercentage/100), (currentRect.x + currentRect.width)),
              y: Math.min(y / (scale*zoomPercentage/100), (currentRect.y + currentRect.height)),
              width: Math.abs((currentRect.x + currentRect.width) - x / (scale*zoomPercentage/100)),
              height: Math.abs((currentRect.y + currentRect.height) - y / (scale*zoomPercentage/100)),
            };
            break;
          case 'tr': // Top-right
            newRect = {
              x: currentRect.x,
              y: Math.min(y / (scale*zoomPercentage/100), (currentRect.y + currentRect.height)),
              width: Math.max(5 / (scale*zoomPercentage/100), x / (scale*zoomPercentage/100) - currentRect.x),
              height: Math.abs((currentRect.y + currentRect.height) - y / (scale*zoomPercentage/100)),
            };
            break;
          case 'bl': // Bottom-left
            newRect = {
              x: Math.min(x / (scale*zoomPercentage/100), (currentRect.x + currentRect.width)),
              y: currentRect.y,
              width: Math.abs((currentRect.x + currentRect.width) - x / (scale*zoomPercentage/100)),
              height: Math.max(5 / (scale*zoomPercentage/100), y / (scale*zoomPercentage/100) - currentRect.y),
            };
            break;
          case 'br': // Bottom-right
            newRect = {
              x: currentRect.x,
              y: currentRect.y,
              width: Math.max(5 / (scale*zoomPercentage/100), x / (scale*zoomPercentage/100) - currentRect.x),
              height: Math.max(5 / (scale*zoomPercentage/100), y / (scale*zoomPercentage/100) - currentRect.y),
            };
            break;
          case 'left': // Left edge
            newRect = {
              x: Math.min(x / (scale*zoomPercentage/100), (currentRect.x + currentRect.width)),
              y: currentRect.y,
              width: Math.abs((currentRect.x + currentRect.width) - x / (scale*zoomPercentage/100)),
              height: currentRect.height,
            };
            break;
          case 'right': // Right edge
            newRect = {
              x: currentRect.x,
              y: currentRect.y,
              width: Math.max(5 / (scale*zoomPercentage/100), x / (scale*zoomPercentage/100) - currentRect.x),
              height: currentRect.height,
            };
            break;
          case 'top': // Top edge
            newRect = {
              x: currentRect.x,
              y: Math.min(y / (scale*zoomPercentage/100), (currentRect.y + currentRect.height)),
              width: currentRect.width,
              height: Math.abs((currentRect.y + currentRect.height) - y / (scale*zoomPercentage/100)),
            };
            break;
          case 'bottom': // Bottom edge
            newRect = {
              x: currentRect.x,
              y: currentRect.y,
              width: currentRect.width,
              height: Math.max(5 / (scale*zoomPercentage/100), y / (scale*zoomPercentage/100) - currentRect.y),
            };
            break;
        }

        // Ensure minimum size
        if (newRect.width < 5 / (scale*zoomPercentage/100)) newRect.width = 5 / (scale*zoomPercentage/100);
        if (newRect.height < 5 / (scale*zoomPercentage/100)) newRect.height = 5 / (scale*zoomPercentage/100);

        // Update current rect
        setCurrentRect(newRect);

        // Update the box in the boxes array (temporary update during resize)
        setBoxes(prev => prev.map(box =>
          box.id === resizingBoxId ? { ...box, rect: newRect } : box
        ));

        return;
      }

      // Handle drawing
      if (isDrawing) {
        // Snap to grid if enabled
        const x = snapToGrid ? snapToGridValue(pt.x) : pt.x;
        const y = snapToGrid ? snapToGridValue(pt.y) : pt.y;

        const dx = x - startPoint.x;
        const dy = y - startPoint.y;

        const newRect = {
          x: dx < 0 ? x / (scale*zoomPercentage/100) : startPoint.x / (scale*zoomPercentage/100),
          y: dy < 0 ? y / (scale*zoomPercentage/100) : startPoint.y / (scale*zoomPercentage/100),
          width: Math.abs(dx) / (scale*zoomPercentage/100),
          height: Math.abs(dy) / (scale*zoomPercentage/100),
        };

        setCurrentRect(newRect);

        // Only log every 10 mouse move events to avoid flooding the debug panel
        if (Math.random() < 0.1) {
          logBox('Box being drawn', {
            currentSize: {
              width: Math.round(newRect.width),
              height: Math.round(newRect.height),
            },
            position: {
              x: Math.round(newRect.x),
              y: Math.round(newRect.y),
            },
          });
        }
      }
    } catch (error) {
      console.error('Error during mouse move:', error);
      setIsDrawing(false);
      setIsResizing(false);
    }
  };

  const handleMouseUp = () => {
    // Handle resizing completion
    if (isResizing && resizingBoxId) {
      try {
        // Find the box we're resizing
        const boxIndex = boxes.findIndex(box => box.id === resizingBoxId);
        if (boxIndex === -1) {
          throw new Error(`Box with ID ${resizingBoxId} not found`);
        }

        const oldBox = boxes[boxIndex];

        // Ensure the new rect has minimum dimensions
        if (currentRect && currentRect.width > 5 / (scale*zoomPercentage/100) && currentRect.height > 5 / (scale*zoomPercentage/100)) {
          // Generate a new UUID for the resized box
          const newId = uuidv4();

          // Create a new box with the new dimensions and ID
          const newBox = {
            ...oldBox,
            id: newId,
            rect: currentRect,
          };

          // Update the boxes array
          const updatedBoxes = [...boxes];
          updatedBoxes[boxIndex] = newBox;
          setBoxes(updatedBoxes);

          logBox('Box resized successfully', {
            oldBoxId: oldBox.id,
            newBoxId: newId,
            boxType: oldBox.type,
            oldRect: oldBox.rect,
            newRect: currentRect,
            pageNumber,
          });
        } else {
          // Revert to original dimensions if too small
          setBoxes(prev => prev.map(box =>
            box.id === resizingBoxId ? oldBox : box
          ));

          logBox('Box resize cancelled - too small', {
            minimumSize: { width: 5 / (scale*zoomPercentage/100), height: 5 / (scale*zoomPercentage/100) },
            actualSize: currentRect
              ? {
                  width: Math.round(currentRect.width),
                  height: Math.round(currentRect.height),
                }
              : null,
          });
        }
      } catch (error) {
        logError('Error during box resize completion', {
          errorMessage: error.message,
          errorStack: error.stack,
        });
      } finally {
        setIsResizing(false);
        setResizingBoxId(null);
        setResizeHandle(null);
        setCurrentRect(null);
      }

      return;
    }

    // Handle drawing completion
    if (isDrawing) {
      if (currentRect && currentRect.width > 5 && currentRect.height > 5) {
        const newBox = {
          type: currentBoxType,
          id: uuidv4(), // Use UUID for consistent ID generation
          rect: currentRect
        };
        setBoxes((prev) => [...prev, newBox]);

        logBox('Box created successfully', {
          boxType: currentBoxType,
          boxId: newBox.id,
          position: {
            x: Math.round(currentRect.x),
            y: Math.round(currentRect.y),
          },
          size: {
            width: Math.round(currentRect.width),
            height: Math.round(currentRect.height),
          },
          pageNumber,
          totalBoxes: boxes.length + 1,
          boxStyle: boxStyles[currentBoxType],
        });
      } else {
        logBox('Box creation cancelled - too small', {
          minimumSize: { width: 5, height: 5 },
          actualSize: currentRect
            ? {
                width: Math.round(currentRect.width),
                height: Math.round(currentRect.height),
              }
            : null,
        });
      }

      setIsDrawing(false);
      setCurrentRect(null);
    }
  };

  // Rectangle intersection
  const intersects = (a, b) => {
    //adding height correction on a.height.
    const heightCorrection = 20;
    return !(
      a.x * (scale*zoomPercentage/100) + a.width * (scale*zoomPercentage/100) < b.x ||
      b.x + b.width < a.x * (scale*zoomPercentage/100) ||
      a.y * (scale*zoomPercentage/100) + a.height * (scale*zoomPercentage/100) + heightCorrection < b.y ||
      b.y + b.height < a.y * (scale*zoomPercentage/100)
    );
  };

  // Function to compile data by Y position, similar to the OCR version
  function compileDataByYForTextPDF(inputData, tolerance = 8) {
    // Reduced tolerance for finer line detection
    const allYPositions = [];

    // Step 1: Collect all Y positions
    inputData.forEach((block) => {
      block.textRows.forEach((row) => {
        allYPositions.push(row.y);
      });
    });

    // Step 2: Create sorted master Y array (deduplicated with tolerance)
    allYPositions.sort((a, b) => a - b); // Sort in ascending order (top to bottom)

    const masterY = [];
    for (let i = 0; i < allYPositions.length; i++) {
      const y = allYPositions[i];
      if (
        masterY.length === 0 ||
        Math.abs(y - masterY[masterY.length - 1]) > tolerance
      ) {
        masterY.push(y);
      }
    }
    logExtract('Master Y positions array created', {
      positions: masterY,
      count: masterY.length,
      minY: masterY[0],
      maxY: masterY[masterY.length - 1],
      tolerance,
    });

    // Step 3: Prepare empty arrays for each type using our configuration
    const dataArrays = createEmptyDataArrays(masterY.length);

    // Helper to find nearest masterY index
    function findClosestIndex(y) {
      let closestIndex = -1;
      let closestDiff = Infinity;
      for (let i = 0; i < masterY.length; i++) {
        const diff = Math.abs(masterY[i] - y);
        if (diff <= tolerance && diff < closestDiff) {
          closestDiff = diff;
          closestIndex = i;
        }
      }
      return closestIndex;
    }

    // Step 4: Fill arrays
    inputData.forEach((block) => {
      block.textRows.forEach((row) => {
        const idx = findClosestIndex(row.y);
        if (idx !== -1) {
          // Get the array name for this box type
          const boxType = block.type.toLowerCase();
          const arrayName = `${boxType}Arr`;

          // If this array exists in our data arrays, fill it
          if (dataArrays[arrayName]) {
            dataArrays[arrayName][idx] = row.text;
          }

          // Handle legacy box types
          if (block.type === 'Description') {
            dataArrays.descriptionArr[idx] = row.text;
          } else if (block.type === 'Length') {
            dataArrays.lengthArr[idx] = row.text;
          } else if (block.type === 'Spec-Grade') {
            dataArrays.specGradeArr[idx] = row.text;
            dataArrays.specificationArr[idx] = row.text; // Map to new field
          } else if (block.type === 'Qty') {
            dataArrays.qtyArr[idx] = row.text;
            dataArrays.quantityArr[idx] = row.text; // Map to new field
          }
        }
      });
    });

    // Step 5: Return the data arrays
    return dataArrays;
  }

  // Extract text from boxes
  const extractText = async () => {
    logExtract('Starting text extraction process for all pages', {
      currentPageNumber: pageNumber,
      currentPageBoxCount: boxes.length,
      boxTypes: boxes.reduce((acc, box) => {
        acc[box.type] = (acc[box.type] || 0) + 1;
        return acc;
      }, {}),
    });

    if (!pdfFile) {
      const errorMsg = 'No PDF loaded';
      logError(`Text extraction failed: ${errorMsg}`);
      return;
    }

    // Save current page boxes to allBoxes before starting extraction
    const currentPageKey = `page${pageNumber}`;
    const tempBoxes = {};
    tempBoxes[currentPageKey] = boxes;

    logBox(`Saving current page boxes before extraction`, {
      pageNumber,
      boxCount: boxes.length,
    });

    // Update allBoxes with current page boxes
    const updatedAllBoxes = { ...allBoxes, ...tempBoxes };
    setAllBoxes(updatedAllBoxes);

    // Check if there are any boxes across all pages
    const totalBoxCount = Object.values(updatedAllBoxes).reduce(
      (count, pageBoxes) => count + pageBoxes.length,
      0
    );

    if (totalBoxCount === 0) {
      const errorMsg = 'No boxes defined on any page';
      logError(`Text extraction failed: ${errorMsg}`);
      return;
    }

    // Show processing message
    setErrorMessage('Processing Document... This may take a moment.');

    logExtract('Loading PDF document for text extraction');

    try {
      const startTime = performance.now();
      const arrayBuffer = await pdfFile.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
      const totalPages = pdf.numPages;

      logExtract('PDF document loaded', {
        loadTime: Math.round(performance.now() - startTime) + 'ms',
        numPages: totalPages,
        fingerprints: pdf.fingerprints || [],
      });

      // Process each page that has boxes
      for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
        const pageKey = `page${pageNum}`;
        const pageBoxes = updatedAllBoxes[pageKey] || [];

        if (pageBoxes.length === 0) {
          logExtract(`Skipping page ${pageNum} - no boxes defined`);
          continue;
        }

        logExtract(`Processing page ${pageNum} with ${pageBoxes.length} boxes`);

        // Load the page
        const page = await pdf.getPage(pageNum);
        logExtract(`Page ${pageNum} loaded for extraction`, {
          pageSize: {
            width: page.view[2],
            height: page.view[3],
          },
          rotation: page.rotate,
        });

        // Get the current rotation for this page
        const currentPageRotation = pageRotations[pageKey] || 0;

        // Create viewport with proper rotation
        const viewport = page.getViewport({
          scale: scale * (zoomPercentage / 100),
          rotation: currentPageRotation
        });

        logExtract('Viewport created', {
          width: viewport.width,
          height: viewport.height,
          scale: scale * (zoomPercentage / 100),
          rotation: currentPageRotation
        });

        // Extract text content
        const textExtractionStart = performance.now();
        const content = await page.getTextContent({ normalizeWhitespace: true });

        logExtract('Text content extracted', {
          extractionTime: Math.round(performance.now() - textExtractionStart) + 'ms',
          itemCount: content.items ? content.items.length : 0,
          styles: content.styles ? Object.keys(content.styles).length : 0,
        });

        if (!content.items || content.items.length === 0) {
          logWarning(`No text layer detected on page ${pageNum}. Skipping.`, {
            suggestion: 'This page may be image-based. Try using OCR extraction instead.',
          });
          continue;
        }

        // Build word items
        logExtract('Building word items from text content');
        const words = content.items.map((item) => {
          const [, , , , x, y] = item.transform;
          const w = item.width;
          const h = item.height || 0;
          const pdfRect = [x, y - h, x + w, y];
          const vp = viewport.convertToViewportRectangle(pdfRect);
          return {
            rect: {
              x: vp[0],
              y: vp[1],
              width: vp[2] - vp[0],
              height: vp[3] - vp[1],
            },
            text: item.str.trim(),
          };
        });

        // Collect words per box type
        const collected = {};

        // Initialize collection objects for all box types
        Object.keys(BOX_TYPES).forEach((type) => {
          collected[type] = [];
        });

        // Also add legacy types for backward compatibility
        collected.Description = [];
        collected.Length = [];
        collected['Spec-Grade'] = [];
        collected.Qty = [];

        pageBoxes.forEach((box) => {
          words.forEach((w) => {
            if (w.text && intersects(box.rect, w.rect)) {
              const text = regExs[box.type]
                ? w.text.match(regExs[box.type])
                : w.text;
              collected[box.type].push({
                x: w.rect.x + w.rect.width / 2,
                y: w.rect.y + w.rect.height / 2,
                text: text,
              });
            }
          });
        });

        // Convert collected data to a format similar to OCR's inputData
        const inputData = [];

        // Process each box type
        Object.keys(collected).forEach((type) => {
          // Group words by Y position with a smaller tolerance
          const SNAP = 2; // Reduced from 5 to 2 for finer granularity
          const yGroups = {};

          collected[type].forEach((word) => {
            const key = Math.round(word.y / SNAP) * SNAP;
            if (!yGroups[key]) yGroups[key] = [];
            yGroups[key].push(word);
          });

          // Create a single block for each box type with all text rows
          const textRows = [];

          // For each Y group, create a text row
          Object.entries(yGroups).forEach(([y, words]) => {
            // Sort words by X position (left to right)
            words.sort((a, b) => a.x - b.x);

            // Create a text row
            const text = words
              .map((w) => w.text)
              .join(' ')
              .trim();
            if (text) {
              textRows.push({ text, y: parseFloat(y) });
            }
          });

          // Only add this type if it has text rows
          if (textRows.length > 0) {
            inputData.push({
              type: type,
              textRows: textRows,
            });
          }
        });

        // Log the input data for debugging
        logExtract(`Preparing data for compilation for page ${pageNum}`, {
          inputDataSize: inputData.length,
          boxTypesWithData: inputData.map((item) => item.type),
          totalTextRows: inputData.reduce(
            (sum, item) => sum + item.textRows.length,
            0
          ),
        });

        // Use a compileDataByY function similar to the OCR version
        const compilationStart = performance.now();
        const compiledData = compileDataByYForTextPDF(inputData);

        logExtract(`Data compilation complete for page ${pageNum}`, {
          compilationTime: Math.round(performance.now() - compilationStart) + 'ms',
          rowCount: compiledData.descriptionArr ? compiledData.descriptionArr.length : 0,
          columnCount: Object.keys(compiledData).length,
        });

        // Also fill legacy arrays for backward compatibility
        compiledData.specGradeArr = compiledData.specificationArr || [];
        compiledData.qtyArr = compiledData.quantityArr || [];

        // Store the compiled data in allExtractedData
        setAllExtractedData((prev) => ({
          ...prev,
          [pageKey]: compiledData,
        }));

        // If this is the current page, also update the current page data
        if (pageNum === pageNumber) {
          setExtractedData(compiledData);
        }

        logExtract(`Extraction complete for page ${pageNum}`, {
          extractedDataSize: JSON.stringify(compiledData).length,
          rowCount: compiledData.descriptionArr ? compiledData.descriptionArr.length : 0,
          pageKey,
        });
      }

      // Update completion message
      setErrorMessage('Processing completed, scroll down to view extracted text');

      // Set a timeout to clear the message after 10 seconds
      setTimeout(() => {
        setErrorMessage('');
      }, 10000);

      logExtract(`All pages extraction complete`, {
        totalPages: totalPages,
        pagesWithBoxes: Object.keys(updatedAllBoxes).length,
        totalProcessingTime: Math.round(performance.now() - startTime) + 'ms',
      });

      setHasExtractedData(true);
    } catch (error) {
      const errorMsg = `Error during text extraction: ${error.message}`;
      logError(errorMsg, {
        errorName: error.name,
        errorStack: error.stack,
        pageNumber,
      });
      setErrorMessage(errorMsg);
    }
  };

  // Export CSV
  const exportCsv = () => {
    logExtract('Starting CSV export process', {
      timestamp: new Date().toISOString(),
      pagesWithData: Object.keys(allExtractedData).length,
      boxTypes: Object.keys(BOX_TYPES),
    });
    const startTime = performance.now();

    // Function to escape CSV values (wrap in quotes if contains comma or quotes)
    const escapeCSV = (value) => {
      if (!value) return '';
      // Replace double quotes with two double quotes (CSV escaping standard)
      const escaped = value.replace(/"/g, '""');
      // If value contains comma, newline, or quotes, wrap in quotes
      return escaped.includes(',') ||
        escaped.includes('"') ||
        escaped.includes('\n')
        ? `"${escaped}"`
        : escaped;
    };

    // Log detailed information about the data being exported
    const dataPreparationStartTime = performance.now();

    // Analyze the data structure
    const pagesWithData = {};
    Object.entries(allExtractedData).forEach(([pageKey, data]) => {
      if (data && data.descriptionArr && data.descriptionArr.length > 0) {
        pagesWithData[pageKey] = {
          rowCount: data.descriptionArr.length,
          boxTypes: Object.keys(data)
            .filter((key) => key.endsWith('Arr'))
            .map((key) => key.replace('Arr', '')),
        };
      }
    });

    // Check if current page data exists and isn't in allExtractedData
    const currentPageKey = `page${pageNumber}`;
    const currentPageDataExists = extractedData?.descriptionArr?.length > 0;
    const isCurrentPageInAll =
      allExtractedData[currentPageKey] &&
      JSON.stringify(allExtractedData[currentPageKey]) ===
        JSON.stringify(extractedData);

    logExtract('CSV export: Data analysis', {
      pagesWithData: Object.keys(pagesWithData).length,
      totalRowsInAllPages: Object.values(pagesWithData).reduce(
        (sum, page) => sum + page.rowCount,
        0
      ),
      currentPageHasData: currentPageDataExists,
      currentPageAlreadyInAllData: isCurrentPageInAll,
      dataAnalysisTimeMs: Math.round(
        performance.now() - dataPreparationStartTime
      ),
    });

    // Add headers row using our configuration
    const headersStartTime = performance.now();
    const headers =
      'Page,' +
      Object.values(BOX_TYPES)
        .map((type) => type.label)
        .join(',') +
      '\n';

    logExtract('CSV export: Headers created', {
      headerColumns: 1 + Object.keys(BOX_TYPES).length, // Page + box types
      boxTypeLabels: Object.values(BOX_TYPES).map((type) => type.label),
      timeMs: Math.round(performance.now() - headersStartTime),
    });

    // Collect rows from all pages
    const rowCollectionStartTime = performance.now();
    let allRows = [];

    // First add current page data if it exists and isn't in allExtractedData
    if (currentPageDataExists && !isCurrentPageInAll) {
      const currentPageRowsStartTime = performance.now();
      const currentPageRows = [];

      extractedData.descriptionArr.forEach((_, index) => {
        const row = { page: pageNumber };

        // Add data for each box type
        Object.keys(BOX_TYPES).forEach((boxType) => {
          const arrayName = `${boxType}Arr`;
          row[boxType] = extractedData[arrayName]?.[index] || '';
        });

        currentPageRows.push(row);
      });

      allRows = [...currentPageRows];

      logExtract('CSV export: Current page data added', {
        pageNumber,
        rowsAdded: currentPageRows.length,
        timeMs: Math.round(performance.now() - currentPageRowsStartTime),
      });
    }

    // Then add data from all pages
    const allPagesRowsStartTime = performance.now();
    const pageRowCounts = {};

    Object.entries(allExtractedData).forEach(([pageKey, data]) => {
      if (!data || !data.descriptionArr || data.descriptionArr.length === 0)
        return;

      const pageNum = pageKey.replace('page', '');
      const pageRowsStartTime = performance.now();
      const pageRows = [];

      data.descriptionArr.forEach((_, index) => {
        const row = { page: pageNum };

        // Add data for each box type
        Object.keys(BOX_TYPES).forEach((boxType) => {
          const arrayName = `${boxType}Arr`;
          row[boxType] = data[arrayName]?.[index] || '';
        });

        pageRows.push(row);
      });

      allRows = [...allRows, ...pageRows];
      pageRowCounts[pageKey] = pageRows.length;

      logExtract(`CSV export: Page ${pageNum} data added`, {
        rowsAdded: pageRows.length,
        timeMs: Math.round(performance.now() - pageRowsStartTime),
      });
    });

    logExtract('CSV export: All pages data collected', {
      totalRows: allRows.length,
      pageCount: Object.keys(pageRowCounts).length,
      pageRowCounts,
      timeMs: Math.round(performance.now() - allPagesRowsStartTime),
    });

    // Convert rows to CSV format
    const csvFormattingStartTime = performance.now();
    const dataRows = allRows
      .map((row) => {
        // Start with the page number
        let csvRow = escapeCSV(row.page);

        // Add each box type in the same order as the headers
        Object.keys(BOX_TYPES).forEach((boxType) => {
          csvRow += ',' + escapeCSV(row[boxType] || '');
        });

        return csvRow;
      })
      .join('\n');

    const csvContent = headers + dataRows;

    logExtract('CSV export: CSV content formatted', {
      headerLength: headers.length,
      dataRowsLength: dataRows.length,
      totalCsvLength: csvContent.length,
      timeMs: Math.round(performance.now() - csvFormattingStartTime),
    });

    // Create and download the file
    const fileCreationStartTime = performance.now();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted_data.csv';

    logExtract('CSV export: File created', {
      blobSize: blob.size,
      mimeType: blob.type,
      fileName: 'extracted_data.csv',
      timeMs: Math.round(performance.now() - fileCreationStartTime),
    });

    // Trigger download
    const downloadStartTime = performance.now();
    a.click();
    URL.revokeObjectURL(url);

    const totalTime = performance.now() - startTime;
    logExtract('CSV export complete', {
      totalRows: allRows.length,
      totalTimeMs: Math.round(totalTime),
      dataPreparationTimeMs: Math.round(
        rowCollectionStartTime - dataPreparationStartTime
      ),
      rowCollectionTimeMs: Math.round(
        csvFormattingStartTime - rowCollectionStartTime
      ),
      csvFormattingTimeMs: Math.round(
        fileCreationStartTime - csvFormattingStartTime
      ),
      fileCreationTimeMs: Math.round(downloadStartTime - fileCreationStartTime),
      downloadTriggerTimeMs: Math.round(performance.now() - downloadStartTime),
      fileName: 'extracted_data.csv',
      fileSize: Math.round(csvContent.length / 1024) + ' KB',
      timestamp: new Date().toISOString(),
    });
  };

  const [allBoxes, setAllBoxes] = useState({});

  // Handle rotation
  const handleRotateClockwise = () => {
    // Check if there are boxes or extracted data
    const hasBoxes = boxes.length > 0;
    const hasData = extractedData && Object.keys(extractedData).length > 0;

    if (hasBoxes || hasData) {
      // Show confirmation dialog
      if (
        !window.confirm('Rotating PDF will clear any extracted data. Continue?')
      ) {
        return;
      }

      // Clear boxes and extracted data for this page
      setBoxes([]);
      setExtractedData({});

      // Remove this page's data from allExtractedData
      const currentPageKey = `page${pageNumber}`;
      setAllExtractedData((prev) => {
        const newData = { ...prev };
        delete newData[currentPageKey];
        return newData;
      });

      // Remove this page's boxes from allBoxes
      setAllBoxes((prev) => {
        const newBoxes = { ...prev };
        delete newBoxes[currentPageKey];
        return newBoxes;
      });
    }

    // Update rotation for this page (add 90 degrees, keep within 0-270 range)
    const currentPageKey = `page${pageNumber}`;
    const currentRotation = pageRotations[currentPageKey] || 0;
    const newRotation = (currentRotation + 90) % 360;

    setPageRotations((prev) => ({
      ...prev,
      [currentPageKey]: newRotation,
    }));

    // Force re-render of the PDF
    if (renderTaskRef.current) {
      try {
        renderTaskRef.current.cancel();
      } catch (error) {
        console.warn('Error cancelling render task:', error);
      }
      renderTaskRef.current = null;
    }
  };

  const handleRotateCounterClockwise = () => {
    // Check if there are boxes or extracted data
    const hasBoxes = boxes.length > 0;
    const hasData = extractedData && Object.keys(extractedData).length > 0;

    if (hasBoxes || hasData) {
      // Show confirmation dialog
      if (
        !window.confirm('Rotating PDF will clear any extracted data. Continue?')
      ) {
        return;
      }

      // Clear boxes and extracted data for this page
      setBoxes([]);
      setExtractedData({});

      // Remove this page's data from allExtractedData
      const currentPageKey = `page${pageNumber}`;
      setAllExtractedData((prev) => {
        const newData = { ...prev };
        delete newData[currentPageKey];
        return newData;
      });

      // Remove this page's boxes from allBoxes
      setAllBoxes((prev) => {
        const newBoxes = { ...prev };
        delete newBoxes[currentPageKey];
        return newBoxes;
      });
    }

    // Update rotation for this page (subtract 90 degrees, keep within 0-270 range)
    const currentPageKey = `page${pageNumber}`;
    const currentRotation = pageRotations[currentPageKey] || 0;
    const newRotation = (currentRotation - 90 + 360) % 360; // Add 360 to handle negative values

    setPageRotations((prev) => ({
      ...prev,
      [currentPageKey]: newRotation,
    }));

    // Force re-render of the PDF
    if (renderTaskRef.current) {
      try {
        renderTaskRef.current.cancel();
      } catch (error) {
        console.warn('Error cancelling render task:', error);
      }
      renderTaskRef.current = null;
    }
  };

  const handlePageChange = (newPage) => {
    logPdf(
      `Page navigation initiated from page ${pageNumber} to page ${newPage}`,
      {
        currentPage: pageNumber,
        targetPage: newPage,
        totalPages: numPages,
        timestamp: new Date().toISOString(),
      }
    );

    const navigationStartTime = performance.now();

    // Save current page boxes
    const currentPageKey = `page${pageNumber}`;
    const tempBoxes = {};
    tempBoxes[currentPageKey] = boxes;

    logBox(`Saving boxes for page ${pageNumber}`, {
      pageNumber,
      boxCount: boxes.length,
      boxTypes: boxes.reduce((acc, box) => {
        acc[box.type] = (acc[box.type] || 0) + 1;
        return acc;
      }, {}),
    });

    setAllBoxes((prev) => ({ ...prev, ...tempBoxes }));

    // Save current page extracted data if it exists and isn't already saved
    if (extractedData && Object.keys(extractedData).length > 0) {
      const hasData = Object.keys(extractedData).some(
        (key) =>
          Array.isArray(extractedData[key]) && extractedData[key].length > 0
      );

      if (hasData) {
        logExtract(`Saving extracted data for page ${pageNumber}`, {
          pageNumber,
          dataSize: JSON.stringify(extractedData).length,
          arrayKeys: Object.keys(extractedData).filter((key) =>
            Array.isArray(extractedData[key])
          ),
          rowCount: extractedData.descriptionArr?.length || 0,
        });

        setAllExtractedData((prev) => {
          // Only update if the current page data isn't already in allExtractedData
          // or if it has changed
          if (
            !prev[currentPageKey] ||
            JSON.stringify(prev[currentPageKey]) !==
              JSON.stringify(extractedData)
          ) {
            return {
              ...prev,
              [currentPageKey]: extractedData,
            };
          }
          return prev;
        });
      } else {
        logExtract(`No significant data to save for page ${pageNumber}`);
      }
    }

    // Change to new page
    logPdf(`Changing to page ${newPage}`);
    setPageNumber(newPage);

    // Load boxes for the new page
    const newPageKey = `page${newPage}`;
    const newPageBoxes = allBoxes[newPageKey] || [];

    logBox(`Loading boxes for page ${newPage}`, {
      pageNumber: newPage,
      boxCount: newPageBoxes.length,
      boxTypes: newPageBoxes.reduce((acc, box) => {
        acc[box.type] = (acc[box.type] || 0) + 1;
        return acc;
      }, {}),
    });

    setBoxes(newPageBoxes);

    // Load extracted data for the new page if it exists
    const newPageData = allExtractedData[newPageKey] || {};
    const hasNewPageData = Object.keys(newPageData).some(
      (key) => Array.isArray(newPageData[key]) && newPageData[key].length > 0
    );

    if (hasNewPageData) {
      logExtract(`Loading extracted data for page ${newPage}`, {
        pageNumber: newPage,
        dataSize: JSON.stringify(newPageData).length,
        arrayKeys: Object.keys(newPageData).filter((key) =>
          Array.isArray(newPageData[key])
        ),
        rowCount: newPageData.descriptionArr?.length || 0,
      });
    } else {
      logExtract(`No existing extracted data for page ${newPage}`);
    }

    setExtractedData(newPageData);

    const navigationEndTime = performance.now();
    logPdf(
      `Page navigation complete from page ${pageNumber} to page ${newPage}`,
      {
        timeMs: Math.round(navigationEndTime - navigationStartTime),
        currentPage: newPage,
        previousPage: pageNumber,
        boxesLoaded: newPageBoxes.length,
        hasExtractedData: hasNewPageData,
      }
    );
  };

  // Reset functionality is now handled by the parent component

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    handleUndoBox: () => {
      if (boxes.length > 0) {
        setBoxes((prev) => prev.slice(0, -1));
      }
    },
    extractImages: extractText, // Map to extractText for compatibility with OCR component
    exportCSV: exportCsv, // Map to exportCsv for compatibility with OCR component
    setCurrentBoxType: (type) => setCurrentBoxType(type),

    // Methods for AWS Textract integration
    getBoxes: () => boxes,
    getPageNumber: () => pageNumber,
    getCanvasWidth: () => canvasRef.current?.width || 0,
    getCanvasHeight: () => canvasRef.current?.height || 0,
    updateExtractedData: (textractExtractedData) => {
      // Convert Textract extracted data to our format
      const pageKey = `page${pageNumber}`;

      // Initialize arrays for each box type
      const newPageData = {};
      Object.keys(BOX_TYPES).forEach(type => {
        newPageData[`${type}Arr`] = [];
        newPageData[`${type}Ids`] = [];
      });

      // Add data from each box
      Object.entries(textractExtractedData).forEach(([boxId, data]) => {
        const boxType = data.type;

        // Add to the appropriate arrays
        if (boxType && BOX_TYPES[boxType]) {
          newPageData[`${boxType}Arr`].push(data.text || '');
          newPageData[`${boxType}Ids`].push(boxId);
        }
      });

      // Update extracted data for this page
      setExtractedData(prev => ({
        ...prev,
        [pageKey]: newPageData
      }));

      // Update all extracted data
      setAllExtractedData(prev => ({
        ...prev,
        [pageKey]: newPageData
      }));

      // Notify parent that we have extracted data
      if (onExtractedDataChange) {
        onExtractedDataChange(true);
      }
    }
  }));

  // Use the PDF file from props
  useEffect(() => {
    if (propsPdfFile) {
      setPdfFile(propsPdfFile);
    }
  }, [propsPdfFile]);

  // Sync with parent's currentBoxType if provided
  useEffect(() => {
    if (parentBoxType) {
      setCurrentBoxType(parentBoxType);
    }
  }, [parentBoxType]);

  // Notify parent when boxes change
  useEffect(() => {
    if (onBoxesChange) {
      onBoxesChange(boxes.length > 0);
    }
  }, [boxes, onBoxesChange]);

  // Notify parent when extracted data changes
  useEffect(() => {
    if (onExtractedDataChange) {
      onExtractedDataChange(hasExtractedData);
    }
  }, [hasExtractedData, onExtractedDataChange]);

  // Forward error messages to parent
  useEffect(() => {
    if (onError && errorMessage) {
      onError(errorMessage);
    }
  }, [errorMessage, onError]);

  const handleZoomOut = ()=>{
    setZoomPercentage(prev => {
      const newZoom = Math.max(prev - zoomIncrement, minZoom);
      return newZoom;
    });
  }
  const handleZoomIn = ()=>{
    setZoomPercentage(prev => {
      const newZoom = Math.min(prev + zoomIncrement, maxZoom);
      return newZoom;
    });
  }

  // Force redraw of boxes when zoom changes
  useEffect(() => {
    // This will trigger a redraw of the overlay canvas with the boxes at the new zoom level
    const overlayCanvas = overlayCanvasRef.current;
    if (overlayCanvas && canvasRef.current) {
      // Ensure overlay canvas dimensions match PDF canvas
      overlayCanvas.width = canvasRef.current.width;
      overlayCanvas.height = canvasRef.current.height;

      // Update canvas size state to trigger redraw
      setCanvasSize({
        width: overlayCanvas.width,
        height: overlayCanvas.height
      });

      logRender('Zoom changed, forcing redraw of boxes', {
        newZoom: zoomPercentage,
        canvasWidth: overlayCanvas.width,
        canvasHeight: overlayCanvas.height
      });
    }
  }, [zoomPercentage, logRender]);


  return (
    <div>
      {/* Toolbar is now handled by the parent component */}

      {errorMessage && <p className={clsx(styles.alert, styles.alertError)}>{errorMessage}</p>}
      {pdfFile && (
        <div
          className="pdf-controls-row"
          style={{
            margin: '15px 0',
            textAlign: 'center',
            padding: '10px',
            backgroundColor: '#f5f5f5',
            borderRadius: '5px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {/* Page Navigation Controls */}
          {numPages > 1 && (
            <div className={styles.pagination} style={{ display: 'flex', alignItems: 'center' }}>
              <button
                className={styles.button}
                disabled={pageNumber <= 1}
                onClick={() => handlePageChange(pageNumber - 1)}
                style={{
                  padding: '5px 12px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  backgroundColor: '#e0e0e0',
                  border: '1px solid #ccc',
                  borderRadius: '3px',
                  color: '#333',
                }}
              >
                Prev
              </button>
              <span style={{ margin: '0 10px', color: '#444' }}>
                Page {pageNumber} of {numPages}
              </span>
              <button
                className={styles.button}
                disabled={pageNumber >= numPages}
                onClick={() => handlePageChange(pageNumber + 1)}
                style={{
                  padding: '5px 12px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  backgroundColor: '#e0e0e0',
                  border: '1px solid #ccc',
                  borderRadius: '3px',
                  color: '#333',
                }}
              >
                Next
              </button>
            </div>
          )}

          {/* Rotation Controls */}
          <div className={styles.rotationControls} style={{ display: 'flex', alignItems: 'center' }}>
            <button
              className={styles.button}
              onClick={handleRotateCounterClockwise}
              title="Rotate 90° Counter-Clockwise"
              style={{
                padding: '5px 12px',
                fontSize: '14px',
                cursor: 'pointer',
                backgroundColor: '#e0e0e0',
                border: '1px solid #ccc',
                borderRadius: '3px',
                color: '#333',
                marginRight: '5px',
              }}
            >
              ↺ Rotate Left
            </button>
            <button
              className={styles.button}
              onClick={handleRotateClockwise}
              title="Rotate 90° Clockwise"
              style={{
                padding: '5px 12px',
                fontSize: '14px',
                cursor: 'pointer',
                backgroundColor: '#e0e0e0',
                border: '1px solid #ccc',
                borderRadius: '3px',
                color: '#333',
                marginRight: '15px',
              }}
            >
              ↻ Rotate Right
            </button>
          </div>

          {/* Zoom Controls */}
          <div className="zoom-controls" style={{ display: 'flex', alignItems: 'center' }}>
            {/* Zoom out button */}
            <button
              className={styles.button}
              onClick={handleZoomOut}
              style={{
                padding: '5px 12px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer',
                backgroundColor: '#e0e0e0',
                border: '1px solid #ccc',
                borderRadius: '3px',
                color: '#333',
              }}
              disabled={zoomPercentage <= 20}
              title="Zoom Out"
            >
              −
            </button>

            {/* Current zoom percentage display */}
            <div
              style={{
                margin: '0 10px',
                fontWeight: 'bold',
                color: '#444',
                minWidth: '60px',
                textAlign: 'center',
              }}
            >
              {zoomPercentage}%
            </div>

            {/* Zoom in button */}
              <button
              className={styles.button}
              onClick={handleZoomIn}
              style={{
                padding: '5px 12px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer',
                backgroundColor: '#e0e0e0',
                border: '1px solid #ccc',
                borderRadius: '3px',
                color: '#333',
              }}
              disabled={zoomPercentage >= 200}
              title="Zoom In"
            >
              +
            </button>
          </div>
        </div>
      )}
      <div
        ref={pdfContainerRef}
        className={styles.pdfContainer}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{
          textAlign: 'center', // Center the content horizontally
        }}
      >
        <div className={styles.pdfContent} style={{
          display: 'inline-block',
          position: 'relative',
          textAlign: 'left' // Reset text alignment for the content
        }}>
          <canvas
            ref={canvasRef}
            style={{
              display: 'block' // Remove any extra space below the canvas
            }}
          />
          <canvas
            ref={overlayCanvasRef}
            className={styles.pdfOverlay}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              pointerEvents: 'none',
              width: '100%',
              height: '100%'
            }}
          />
        </div>
      </div>

      <div className={styles.pdfControls}>
        {numPages > 1 && (
          <div className={styles.pagination}>
            <button
              className={styles.button}
              disabled={pageNumber <= 1}
              onClick={() => handlePageChange(pageNumber - 1)}
            >
              Prev
            </button>
            <span>
              Page {pageNumber} of {numPages}
            </span>
            <button
              className={styles.button}
              disabled={pageNumber >= numPages}
              onClick={() => handlePageChange(pageNumber + 1)}
            >
              Next
            </button>
          </div>
        )}

        {pdfFile && (
          <div className={styles.rotationControls}>
            <button
              className={styles.button}
              onClick={handleRotateCounterClockwise}
              title="Rotate 90° Counter-Clockwise"
            >
              ↺ Rotate Left
            </button>
            <button
              className={styles.button}
              onClick={handleRotateClockwise}
              title="Rotate 90° Clockwise"
            >
              ↻ Rotate Right
            </button>
          </div>
        )}
      </div>

      {Object.keys(allExtractedData).length > 0 ? (
        <div>
          <h3>Extracted Data</h3>

          {/* All pages data with page headers */}
          <div>
            <table
              className={styles.table}
              style={{
                width: '100%',
                borderCollapse: 'collapse',
                color: '#444444', // Dark grey text color for the entire table
              }}
            >
              <thead>
                <tr>
                  {Object.values(BOX_TYPES).map((type) => (
                    <th
                      key={type.id}
                      style={{
                        padding: '8px',
                        backgroundColor: '#f2f2f2',
                        border: '1px solid #ddd',
                      }}
                    >
                      {type.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {Object.entries(allExtractedData).map(([pageKey, data]) => {
                  // Skip if no data or empty arrays
                  if (
                    !data ||
                    !data.descriptionArr ||
                    data.descriptionArr.length === 0
                  ) {
                    return null;
                  }

                  // Extract page number from pageKey (e.g., "page1" -> "1")
                  const pageNum = pageKey.replace('page', '');

                  // Calculate the number of columns (number of box types)
                  const columnCount = Object.keys(BOX_TYPES).length;

                  return (
                    <React.Fragment key={pageKey}>
                      {/* Page header row with merged cells */}
                      <tr style={{ backgroundColor: '#e6f2ff' }}>
                        <td
                          colSpan={columnCount}
                          style={{
                            padding: '8px',
                            fontWeight: 'bold',
                            textAlign: 'center',
                            border: '1px solid #ddd',
                            color: '#333333', // Slightly darker for emphasis
                          }}
                        >
                          Page {pageNum}
                        </td>
                      </tr>

                      {/* Data rows for this page */}
                      {data.descriptionArr.map((_, rowIndex) => (
                        <tr
                          key={`${pageKey}-${rowIndex}`}
                          style={{
                            backgroundColor:
                              rowIndex % 2 === 0 ? '#fff' : '#f9f9f9',
                          }}
                        >
                          {Object.keys(BOX_TYPES).map((boxType) => {
                            const arrayName = `${boxType}Arr`;
                            return (
                              <td
                                key={boxType}
                                style={{
                                  padding: '8px',
                                  border: '1px solid #ddd',
                                }}
                              >
                                {data?.[arrayName]?.[rowIndex] || ''}
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Export button for all data */}
          <div style={{ marginTop: '15px' }}>
            <button
              className={styles.button}
              onClick={exportCsv}
              disabled={Object.keys(allExtractedData).length === 0}
              style={{
                padding: '8px 16px',
                backgroundColor: '#444444',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontWeight: 'bold',
                opacity: Object.keys(allExtractedData).length === 0 ? 0.5 : 1,
              }}
            >
              Export All Data to CSV
            </button>
          </div>
        </div>
      ) : null}
    </div>
  );
});

export default PdfTextExtractor;
