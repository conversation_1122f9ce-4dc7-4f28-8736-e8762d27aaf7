import React, { useCallback, useState, KeyboardEvent, useEffect, useRef } from 'react';
import styles from './BomUpload.module.scss';
import SearchHeader from '../../SearchHeader';
import BomUploadImage from "../../../assets/New-images/BomUploadImg.png";
import { ReactComponent as BomCrossIcon } from '../../../assets/New-images/bom-icon-close.svg';
import { useDropzone } from 'react-dropzone';
import Game from 'src/renderer2/component/Game/Game';
import { useCreatePoStore, useGlobalStore } from '@bryzos/giss-ui-library';
import useGetGameScore from 'src/renderer2/hooks/useGetGameScore';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import BomProcessingWindow from 'src/renderer2/component/BomProcessingWindow/BomProcessingWindow';
import { useNavigate } from 'react-router-dom';
import useSnackbarStore from 'src/renderer2/component/Snackbar/snackbarStore';

type FileStatus = 'idle' | 'uploading' | 'success' | 'error'; 

const BomUpload: React.FC = () => {
    const {userData} = useGlobalStore();
    const navigate = useNavigate();
    const { deviceId } = useGlobalStore();
    const {showToastSnackbar, resetSnackbarStore} = useSnackbarStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData,bomProgressSocketData,setBomProgressSocketData, bomProcessingError, setBomProcessingError } = useCreatePoStore();
    const [file, setFile] = useState<File | null>(null);
    const [filePreview, setFilePreview] = useState<string | null>(null);
    const [fileStatus, setFileStatus] = useState<FileStatus>('idle');
    const [errorMessage, setErrorMessage] = useState<string>('');
    const [isDragActive, setIsDragActive] = useState<boolean>(false);
    const { setLoadComponent, setShowVideo, setProps, setBomProcessingWindowProps, bomProcessingWindowProps, isCancelUpload, setIsCancelUpload } = useRightWindowStore();
    const [uploadProgress, setUploadProgress] = useState<number>(0);
    const [currentGameScore, setCurrentGameScore] = useState<number>(0);
    
    // Reference to abort controller for cancelling uploads
    const uploadAbortController = useRef<AbortController | null>(null);
    const { data: getGameScore } = useGetGameScore();
    
    // Handle score changes from the Game component
    const handleScoreChange = (score: number) => {
        if (score !== currentGameScore) {
            setCurrentGameScore(score);
        }
    };
    
    // Handle Game component unmounting
    const handleGameUnmount = (finalScore: number, consecutiveCatches: number) => {
        setCurrentGameScore(finalScore);
    };
    
    // Function to update the processing window with current score
    const updateProcessingWindow = () => {
        
        // Use a fixed height container to prevent window size jumping
        // Add a key prop based on file name and timestamp to force a full component reset when needed
        const componentKey = file ? `${file.name}-${Date.now()}` : 'no-file';
        
        setLoadComponent(<BomProcessingWindow/>);
    };

    useEffect(()=>{
        if(getGameScore){
            setBomProcessingWindowProps({
                gameScoreData: getGameScore
            });
        }
    },[getGameScore]);

    useEffect(()=>{
        if(bomProcessingWindowProps?.isProcessing) {
            setFileStatus('uploading');
        }
    },[]);

    useEffect(()=>{
        if(getGameScore){
            setBomProcessingWindowProps({
                gameScoreData: getGameScore
            });
        }
    },[getGameScore]);

    // useEffect(() => {
    //     if (userData.data.email_id === bomProductMappingSocketData?.email_id && bomProductMappingSocketData?.device_id === deviceId && bomProductMappingSocketData?.id === bomProcessingWindowProps?.bomUploadId ) {
    //         //showToastSnackbar('BOM uploaded successfully', snackbarSeverityType.success, null, resetSnackbarStore, 3000);
    //         setBomProcessingWindowProps({isCompleted:true,generatingPercent:100});
    //     }
        
    // }, [bomProductMappingSocketData]);

    // Add this useEffect to watch all progress states
    useEffect(() => {
        
        if (fileStatus === 'uploading' && bomProcessingWindowProps && bomProcessingWindowProps.isProcessing) {
            setBomProcessingWindowProps({
                ...bomProcessingWindowProps,
                uploadingPercent: uploadProgress,
                currentScore: currentGameScore
            })
        }
    }, [uploadProgress]);

    useEffect(() => {
        // Cleanup function to abort any in-progress uploads when component unmounts

        return () => {
            // Cancel any in-progress uploads
            if (uploadAbortController.current) {
                uploadAbortController.current.abort();
                uploadAbortController.current = null;
            }

            setBomProcessingWindowProps({currentScore:0});
        };
    }, []);

    
    // Update processing window when game score changes
    useEffect(() => {
        if (fileStatus === 'uploading') {
            setBomProcessingWindowProps({
                currentScore: currentGameScore
            })
        }
    }, [currentGameScore]);

    useEffect(()=>{
        if( 
            (bomProcessingError?.bom_upload_id === bomProcessingWindowProps?.bomUploadId && 
            bomProcessingError?.email_id === userData.data.email_id && 
            bomProcessingError?.device_id === deviceId)){
            showErrorMessage("Something went wrong, please try again.");
            setBomProcessingError(null);
        } else if(bomProcessingWindowProps?.bomUploadError){
            showErrorMessage('Failed to upload file. Please try again.');
        }
    },[bomProcessingError]);

    const showErrorMessage = (message: string) => {
        setFileStatus('error');
        setErrorMessage(message);
        setBomProcessingWindowProps({
            uploadingPercent: 0,
            readingPercent: 0,
            thinkingPercent: 0,
            generatingPercent: 0,
            stopProgress: true,
            isProcessing: false,
            isCompleted: false,
            bomUploadError: false
        })
        setLoadComponent(null);
    }

    const validateFile = (file: File): boolean => {
        const ext = file.name.substring(file.name.lastIndexOf(".")).toLowerCase();
        const allowedExtensions = ['.xlsx', '.xls', '.csv', '.pdf'];
        
        if (allowedExtensions.indexOf(ext) === -1) {
            setErrorMessage(`${file.name} is not supported. Please upload an Excel, CSV, or PDF file.`);
            return false;
        }
        
        // Check file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            setErrorMessage(`File size exceeds 10MB limit.`);
            return false;
        }
        
        return true;
    };

    const onDrop = useCallback((acceptedFiles: File[]) => {
        if (acceptedFiles.length) {
            const file = acceptedFiles[0];
            
            if (!validateFile(file)) {
                setFileStatus('error');
                return;
            }
            
            setFile(file);
            setErrorMessage('');
            setFileStatus('success');
            
            // Create file preview
            const reader = new FileReader();
            reader.onload = () => {
                setFilePreview(file.name);
            };
            reader.readAsDataURL(file);
        }
    }, []);

    const handleUpload = async () => {
        if (!file) {
            setErrorMessage('Please select a file first.');
            return;
        }
        
        // Reset all progress data before starting a new upload
        setBomProcessingWindowProps({
            uploadingPercent: 0,
            readingPercent: 0,
            thinkingPercent: 0,
            generatingPercent: 0,
            currentScore: 0,
            fileToUpload: file,
        })
        setUploadProgress(0);
        setBomProgressSocketData(null);

        setBomProcessingWindowProps({
            uploadingPercent: 0,
            readingPercent: 0,
            thinkingPercent: 0,
            generatingPercent: 0
        })
        
        setFileStatus('uploading');
        setBomProcessingWindowProps({isProcessing: true});
        const fileName = file.name.substring(0, file.name.lastIndexOf(".")) || file.name;
        
        try {
            // Show right side window with BomProcessingWindow
            updateProcessingWindow();
            
            setShowVideo(false);
            
        } catch (error) {
            setFileStatus('error');
            setErrorMessage('Failed to upload file. Please try again.');
            setBomProcessingWindowProps({
                uploadingPercent: 0,
                readingPercent: 0,
                thinkingPercent: 0,
                generatingPercent: 0,
                isProcessing: false,
                stopProgress: true
            })
        }   
    };


    const handleErrorClose = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the dropzone
        setFileStatus('idle');
        setFile(null);
        setErrorMessage('');
    }

    const handleKeyDown = (e: KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            open();
        }
    };

    // Add a ref for the button
    const browseButtonRef = useRef<HTMLButtonElement>(null);
    
    // Add useEffect to focus the button when component mounts
    useEffect(() => {
        // Using setTimeout to delay focus until after component is fully mounted
        const timer = setTimeout(() => {
            if (browseButtonRef.current && !file) {
                browseButtonRef.current.focus();
            }
        }, 0);
        
        return () => clearTimeout(timer);
    }, [file]);
    
    // Handle button click with proper event typing
    const handleButtonClick = () => {
        open();
    };

    // Add function to clear the uploaded file and reset the form
    const handleClearFile = (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the dropzone
        
        setErrorMessage('');
        setFileStatus('idle');
        setFile(null);
        setBomProcessingError(null);

        // Cancel any in-progress uploads
        if (uploadAbortController.current) {
            uploadAbortController.current.abort();
            uploadAbortController.current = null;
        }
        
        navigate(-1)
    };

    const {
        getRootProps,
        getInputProps,
        open,
        isDragAccept,
        isDragReject
    } = useDropzone({
        onDrop,
        noClick: true,
        accept: {
            'application/vnd.ms-excel': ['.xls'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'text/csv': ['.csv'],
            'application/pdf': ['.pdf']
        },
        onDragEnter: () => setIsDragActive(true),
        onDragLeave: () => setIsDragActive(false),
        onDropAccepted: () => setIsDragActive(false),
        onDropRejected: () => setIsDragActive(false)
    });

    const renderUploadStatus = () => {
        switch (fileStatus) {
            case 'uploading':
                return <div className={styles.statusMessage}>Uploading file...</div>;
            case 'error':
                return (
                    <div className={styles.errorMessage}>
                        {errorMessage}
                        <button 
                            className={styles.closeButton} 
                            onClick={handleErrorClose}
                            aria-label="Clear error"
                        >
                            ✕
                        </button>
                    </div>
                );
            case 'success':
                if (file) {
                    return (
                        <div className={styles.filePreview}>
                            <div className={styles.fileName}>
                                {file.name}
                            </div>
                            <button 
                                className={styles.uploadButton} 
                                onClick={handleUpload}
                            >
                                <span>UPLOAD</span>
                            </button>
                            <button 
                                className={styles.uploadDifferentFileLink}
                                onClick={open}
                            >
                                Upload a Different File
                            </button>
                        </div>
                    );
                }
                return null;
            default:
                return null;
        }
    };

    return (
        <div>
            <div>
            </div>
            <div className={styles.bomUploadContainer}>
                {(fileStatus !== 'uploading') &&
                    <span onClick={(handleClearFile)} className={styles.bomUploadCrossIcon}>
                        <BomCrossIcon />
                    </span>
                }
                {(fileStatus === 'uploading') ?
                    <div>
                        <div>
                            <Game 
                                gameScore={getGameScore} 
                                bomUploadId={bomProcessingWindowProps?.bomUploadId || ''} 
                                onScoreChange={handleScoreChange}
                            />
                        </div>
                    </div>
                :
                    <div
                        className={`${styles.bomUploadContent} ${isDragActive ? styles.dragActive : ''} ${isDragReject ? styles.dragReject : ''}`}
                        {...getRootProps()}
                        role="button"
                        aria-label="Drop zone for BOM files"
                    >
                        {/* Show upload image and text only when no file is selected */}
                        { (
                            <>
                                <span className={styles.bomUploadImage}>
                                    <img src={BomUploadImage} alt="Upload BOM file" />
                                </span>
                                <span className={styles.bomUploadText}>
                                    DRAG ‘N DROP YOUR <br />
                                    BOM ANYWHERE
                                </span>
                            </>
                        )}

                        {renderUploadStatus()}

                        {/* Show browse button only when no file is selected */}
                        {!file && (
                            <div className={styles.buttonContainer}>
                                <button
                                    className={styles.bomUploadButton}
                                    onClick={handleButtonClick}
                                    onKeyDown={handleKeyDown}
                                    ref={browseButtonRef}
                                >
                                    <span>BROWSE YOUR FILES</span>
                                </button>
                            </div>
                        )}

                        <input {...getInputProps()} multiple={false} />
                    </div>
                }
            </div>
        </div>
    );
};

export default BomUpload;
