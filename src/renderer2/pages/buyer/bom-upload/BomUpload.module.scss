.bomUploadContainer {
    width: 800px;
    height: 880px;
    background-image: linear-gradient(162deg, #0f0f14 -26%, #2f343b 226%);
    position: relative;

    .bomUploadCrossIcon {
        position: absolute;
        right: 41px;
        top: 40px; 
        cursor: pointer;
        z-index: 2;
    }

    .bomUploadContent {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: start;
        padding: 120px 0px 0px 0px;
        position: relative;
        border: 2px dashed transparent;
        transition: all 0.3s ease;
        outline: none;

        &.dragActive {
            border: 2px dashed #1fbbfe;
            background-color: rgba(31, 187, 254, 0.05);
        }

        &.dragReject {
            border: 2px dashed #ff5252;
            background-color: rgba(255, 82, 82, 0.05);
        }

        &:focus-visible {
            outline: 2px solid #1fbbfe;
            box-shadow: 0 0 8px rgba(31, 187, 254, 0.5);
        }

        .bomUploadImage {
            height: 320px;
            width: 300px;
            img {
                height: 100%;
                width: 100%;
                object-fit: contain;
            }
        }

        .bomUploadText {
            background-image: linear-gradient(133deg, #191a20 -45%, rgba(221, 221, 221, 0.77) 60%);
            font-family: Inter;
            font-size: 38px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: 1.52px;
            text-align: center;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 48px;
        }

        .filePreview {
            margin: 0 auto;
            background-color: transparent;
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            text-align: center;

            .fileIcon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 76px;
                height: 76px;
                font-size: 40px;
                font-weight: bold;
                border-radius: 8px;
                margin-bottom: 20px;
                position: relative;
            }
            
            .pdfIcon, .excelIcon, .csvIcon, .defaultIcon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 76px;
                height: 76px;
                font-size: 24px;
                font-weight: bold;
                border-radius: 8px;
                position: relative;
                
                &::after {
                    content: '';
                    position: absolute;
                    width: 70px;
                    height: 86px;
                    border-radius: 8px;
                    z-index: -1;
                    right: -10px;
                    bottom: -10px;
                }
            }
            
            .pdfIcon {
                background-color: #333;
                color: #fff;
                
                &::after {
                    background-color: #222;
                }
            }
            
            .excelIcon {
                background-color: #ccc;
                color: #333;
                
                &::after {
                    background-color: #919191;
                }
            }
            
            .csvIcon {
                background-color: #ddd;
                color: #333;
                
                &::after {
                    background-color: #aaa;
                }
            }
            
            .defaultIcon {
                background-color: #999;
                color: #fff;
                
                &::after {
                    background-color: #666;
                }
            }

            .fileName {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.15;
                letter-spacing: 0.98px;
                text-align: center;
                color: #fff;
                padding-bottom: 16px;
                padding-top: 24px;
            }

            .uploadButton {
                border-radius: 16px;
                border: solid 1px rgba(255, 255, 255, 0.5);
                width: 307px;
                height: 54px;

                span {
                    background-image: linear-gradient(140deg, #191a20 -60%, #dcdcdc 63%);
                    font-family: Inter;
                    font-size: 24px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: 0.96px;
                    text-align: center;
                    -webkit-background-clip: text;
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
                
                &:hover {
                    -webkit-backdrop-filter: blur(21.3px);
                    backdrop-filter: blur(21.3px);
                    background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
                }
                &:focus-visible {
                    -webkit-backdrop-filter: blur(21.3px);
                    backdrop-filter: blur(21.3px);
                    background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
                }
            }
            
            .uploadDifferentFileLink {
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.15;
                letter-spacing: 0.98px;
                text-align: center;
                color: #8b8d91;
                padding-top: 24px;
                
                &:hover {
                    color: #1fbbff;
                }
            }
        }

        .statusMessage {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: rgba(31, 187, 254, 0.1);
            border-radius: 4px;
            color: #1fbbfe;
            font-size: 16px;
        }

        .errorMessage {
            margin-top: 20px;
            padding: 10px 40px 10px 20px;
            background-color: rgba(255, 82, 82, 0.1);
            border-radius: 4px;
            color: #ff5252;
            font-size: 16px;
            text-align: center;
            max-width: 80%;
            position: relative;

            .closeButton {
                position: absolute;
                top: 13px;
                right: 12px;
                width: 20px;
                height: 20px;
                background-color: rgba(255, 82, 82, 0.2);
                color: #ff5252;
                border: none;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 10px;
                padding: 0;
                transition: all 0.2s ease;

                &:hover {
                    background-color: rgba(255, 82, 82, 0.3);
                    transform: scale(1.1);
                }

                &:focus-visible {
                    outline: 2px solid #ff5252;
                    box-shadow: 0 0 4px rgba(255, 82, 82, 0.5);
                }
            }
        }

        .buttonContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 56px;
        }

        .bomUploadButton {
            border-radius: 16px;
            border: solid 1px rgba(255, 255, 255, 0.5);
            height: 54px;
            width: 307px;
            &:hover {
                -webkit-backdrop-filter: blur(21.3px);
                backdrop-filter: blur(21.3px);
                background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
            }
            &:focus-visible {
                -webkit-backdrop-filter: blur(21.3px);
                backdrop-filter: blur(21.3px);
                background-image: linear-gradient(335deg, rgba(255, 255, 255, 0.2) 46%, #1c1c21 161%);
            }
            span {
                font-family: Inter;
                font-size: 24px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: normal;
                letter-spacing: 0.96px;
                text-align: center;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                background-image: linear-gradient(115deg, #191a20 -23%, #dcdcdc 56%);
            }
        }

        .uploadNowButton {
            border-radius: 16px;
            border: solid 1px #1fbbfe;
            background-color: #1fbbfe;
            color: white;
            font-family: Inter;
            font-size: 24px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: 0.96px;
            text-align: center;
            height: 54px;
            width: 307px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background-color: #1ca9e2;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }

            &:focus-visible {
                outline: 2px solid white;
                box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
            }
        }

        .clearButton {
            border-radius: 16px;
            border: solid 1px rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.7);
            font-family: Inter;
            font-size: 16px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: 0.64px;
            text-align: center;
            height: 44px;
            width: 307px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.9);
            }

            &:focus-visible {
                outline: 2px solid #1fbbfe;
                box-shadow: 0 0 8px rgba(31, 187, 254, 0.5);
            }
        }

        .supportedFormats {
            margin-top: 30px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            text-align: center;
        }
    }
}