import React, { useEffect, useState } from 'react'
import SearchHeader from '../../SearchHeader'
import styles from './ViewPoHistory.module.scss'
import { getFloatRemainder, getValUsingUnitKey, newPricingPrefix, orderIncrementPrefix, priceUnits, useCreatePoStore, useGlobalStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import { useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import PoHistoryRightWindow from 'src/renderer2/component/PoHistoryRightWindow/PoHistoryRightWindow';
import PoHistoryEditTile from 'src/renderer2/component/PoHistoryTile/PoHistoryEditTile';
import SavedBomPreviewTile from 'src/renderer2/component/SavedBomPreview/SavedBomPreviewTile';
import useGetUserPartData from 'src/renderer2/hooks/useGetUserPartData';
import { useDebouncedValue } from '@mantine/hooks';
import { calculateTotalWeightForProduct } from 'src/renderer2/utility/pdfUtils';
import { getPriceExample } from 'src/renderer2/utility/priceIntegratorExample';
import { useCalculatePoPrices } from 'src/renderer2/hooks/useCalculatePoPrices';
import usePostRemovePoHistoryLine from 'src/renderer2/hooks/usePostRemovePoHistoryLine';
import usePostAddPoHistoryLine from 'src/renderer2/hooks/usePostAddPoHistoryLine';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import dayjs from 'dayjs';

let cartItem = {
    descriptionObj: '',
    description: '',
    qty: '',
    qty_unit: '',
    price_unit: '',
    product_tag: '',
    domesticMaterialOnly: false,
    qtyUnitM: [],
    priceUnitM: [],
    line_session_id: '',
}

const instantPricingSchema = yup.object().shape({
    id: yup.string(),
    buyer_internal_po: yup.string().trim().required('Po Job Name is not valid'),
    buyer_po_number: yup.string().trim().required('Po Number is not valid'),
    cart_items: yup.array().of(
        yup.object().shape({
            unique_id: yup.string(),
            line_id: yup.number(),
            product_id: yup.number(),
            reference_product_id: yup.number(),
            buyer_pricing_lb: yup.string(),
            description: yup.string().required(),
            domesticMaterialOnly: yup.boolean().oneOf([true, false, undefined]).default(null).nullable(),
            extended: yup.number(),
            price: yup.number(),
            price_unit: yup.string().required(),
            product_tag: yup.string().default(null).nullable().defined(),
            qty: yup.string().required(),
            qty_unit: yup.string().required(),
            shape: yup.string(),
            buyer_calculation_price: yup.number(),
            descriptionObj: yup.object().required(),
            sessionId: yup.string().default('').defined(),
            qty_um: yup.array(),
            price_um: yup.array(),
            line_session_id: yup.string(),
            extendedValue: yup.number()
        })
    ),
    material_total: yup.string(),
    sales_tax: yup.number(),
    delivery_date: yup.string().required(),
    totalPurchase: yup.number(),
    deposit: yup.number(),
    payment_method: yup.string(),
    freight_term: yup.string(),
    shipping_details: yup.object().shape({
        line1: yup.string().trim().required('Address is not valid'),
        city: yup.string().trim().required('City is not valid'),
        state_code: yup.string().required('State is not valid'),
        state_id: yup.number().required('State is not valid'),
        zip: yup.string().trim().required('Zip is not valid'),
        validating_state_id_zip: yup.boolean().default(false).defined()
    }),
    claimed_by: yup.string().nullable().defined(),
    fulfilled_by: yup.string().nullable().defined(),
    lastIndex: yup.number().nullable().defined(),
    subscription: yup.string(),
    is_closed: yup.boolean().default(false).defined(),
    is_closed_buyer: yup.boolean().default(false).defined(),
    is_closed_seller: yup.boolean().default(false).defined()
});

const ViewPoHistory = () => {


    const {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        formState: { errors, isValid }
    } = useForm({
        resolver: yupResolver(instantPricingSchema),
        mode: 'onChange',
        defaultValues: {
            'cart_items': [],
            'freight_term': "Delivered"
        }
    });

    const { fields, append, remove,  } = useFieldArray({
        control,
        name: "cart_items"
    });
    const showCommonDialog = useDialogStore((state: any) => state.showCommonDialog);
    const resetDialogStore = useDialogStore((state: any) => state.resetDialogStore);
    const viewPoHistoryData = useCreatePoStore((state: any) => state.viewPoHistoryData);
    const setViewPoHistoryData = useCreatePoStore((state: any) => state.setViewPoHistoryData);
    const productMapping = useGlobalStore((state: any) => state.productMapping);
    const setShowLoader = useGlobalStore((state: any) => state.setShowLoader);
    const { data: userPartData } = useGetUserPartData();
    const { setLoadComponent, setProps, props, isAddLineBtnClicked, setIsAddLineBtnClicked } = useRightWindowStore();
    const [showNoRecords, setShowNoRecords] = useState(true);
    const [initialLastIndex, setInitialLastIndex] = useState<number | null>(null);
    const [isRemoveLineBtnClicked, setIsRemoveLineBtnClicked] = useState(false);
    const [showSuccessBox, setShowSuccessBox] = useState(false);
    const [showConfirmationBox, setShowConfirmationBox] = useState(false);
    const [handleSubmitValidation, setHandleSubmitValidation] = useState(false);

    const { mutateAsync: removePoHistoryLine } = usePostRemovePoHistoryLine();
    const { mutateAsync: addPoHistoryLine } = usePostAddPoHistoryLine();
    const {
        quantitySizeValidator,
        updateLineProduct,
        calculateMaterialTotalPrice
    } = useCalculatePoPrices({watch, setValue, getValues, register, clearErrors, trigger, setError, userPartData, setHandleSubmitValidation, isAddLineBtnClicked})

    useEffect(() => {
        if (viewPoHistoryData) {
            setLoadComponent(<PoHistoryRightWindow />);
        }
    }, [viewPoHistoryData])

    
    const saveAddedLine = async () => {
        try{
            const cart_items = watch('cart_items')?.slice(watch('lastIndex'), watch('cart_items')?.length) || [];
            console.log("cart_items", cart_items, watch('lastIndex'), watch('cart_items')?.length, watch('cart_items'))
            const payload = {
                purchase_order_id: viewPoHistoryData?.id,
                cart_items: cart_items.map(item => ({
                    line_id: item.line_id,
                    product_id: item.product_id,
                    qty_unit: item.qty_unit.toUpperCase(),
                    price_unit: item.price_unit.toUpperCase(), 
                    qty: parseFloat(item.qty),
                    domestic_material_only: item.domesticMaterialOnly || false,
                    product_tag: item.product_tag || null
                }))
            }
            const response = await addPoHistoryLine(payload);
            console.log("response", response, payload);
            if(response.error_message){
                console.log("error", response.error_message);
                showCommonDialog(null, response.error_message, null, resetDialogStore, [
                    { name: 'OK', action: resetDialogStore }
                  ]);
                return;
            }
            setShowConfirmationBox(false);
            setShowSuccessBox(true);
            setIsAddLineBtnClicked(false);
        } catch(error: any){
            console.log("error", error);
            showCommonDialog(null, error.message, null, resetDialogStore, [
                { name: 'OK', action: resetDialogStore }
              ]);   
        }
    }

    const handleSuccessBoxClose = () => {
        setShowSuccessBox(false);
        setIsAddLineBtnClicked(false);
    }

    const handleConfirmationBoxClose = () => {
        setShowConfirmationBox(false);
        setValue('lastIndex', initialLastIndex);
    }

    const resetForm = () => {
        reset();
        setIsAddLineBtnClicked(false);
        setIsRemoveLineBtnClicked(false);
        setShowConfirmationBox(false);
        setShowSuccessBox(false);
    }
    
    const handleAddLineBtnClick = () => {
        if(isAddLineBtnClicked) return;
        setIsAddLineBtnClicked(true);
        setIsRemoveLineBtnClicked(false);
        append({ ...cartItem, line_id: watch('cart_items').length + 1 })
    }

    useEffect(() => {
        setProps({
            watch,
            control,
            setValue,
            getValues,
            setIsRemoveLineBtnClicked,
            setShowSuccessBox,
            setShowConfirmationBox,
            saveAddedLine,
            isRemoveLineBtnClicked,
            showSuccessBox,
            showConfirmationBox,
            handleSuccessBoxClose,
            handleConfirmationBoxClose,
            handleAddLineBtnClick
        });
    }, [viewPoHistoryData,
        watch('material_total'),
        watch('sales_tax'),
        watch('deposit'),
        watch('subscription'),
        watch('totalPurchase'),
        isRemoveLineBtnClicked,
        showSuccessBox,
        showConfirmationBox,
        isAddLineBtnClicked
    ])

    useEffect(() => {
        return () => {
            setProps(null);
            setLoadComponent(null);
            setViewPoHistoryData(null);
            resetForm();
        }
    }, [])

    useEffect(() => {
        if (viewPoHistoryData) {
            resetForm();
            const cartItem = viewPoHistoryData?.items?.map((item: any, i: any) => {
                const product = productMapping[item.product_id];
                return {
                    unique_id: item.id,
                    line_id: item.line_id,
                    product_tag: item.product_tag,
                    description: item.description,
                    qty: item.qty,
                    qty_unit: item.qty_unit,
                    price_unit: item.price_unit,
                    price: item.price_per_unit,
                    extended: item?.buyer_line_total ? parseFloat(parseFloat(item.buyer_line_total).toFixed(2)) : 0,
                    domesticMaterialOnly: item?.domestic_material_only || false,
                    product_id: item.product_id,
                    descriptionObj: product
                }
            });
            
            const totalPurchase = parseFloat(viewPoHistoryData.material_total)  + parseFloat(viewPoHistoryData.sales_tax) + parseFloat(viewPoHistoryData.deposit) + parseFloat(viewPoHistoryData.subscription);
            const lastIndex = viewPoHistoryData?.items?.length-1;
            console.log("cehck data ddd ", watch('cart_items'), lastIndex, cartItem)
            setInitialLastIndex(cartItem.length - 1);
            setValue('lastIndex', cartItem.length - 1);
            setValue('id', viewPoHistoryData?.id);
            setValue('cart_items', cartItem);
            setValue('delivery_date', viewPoHistoryData?.delivery_date);
            setValue('shipping_details', viewPoHistoryData?.shipping_details);
            setValue('claimed_by', viewPoHistoryData?.claimed_by);
            setValue('fulfilled_by', viewPoHistoryData?.fulfilled_by);
            setValue('buyer_internal_po', viewPoHistoryData?.buyer_internal_po);
            setValue('buyer_po_number', viewPoHistoryData?.buyer_po_number);
            setValue('sales_tax', viewPoHistoryData?.sales_tax);
            setValue('totalPurchase', totalPurchase);
            setValue('deposit', viewPoHistoryData?.deposit);
            setValue('payment_method', viewPoHistoryData?.payment_method);
            setValue('subscription', viewPoHistoryData?.subscription);
            setValue('material_total', viewPoHistoryData?.material_total);
            setValue('is_closed', Boolean(viewPoHistoryData?.is_closed) || false);
            setValue('is_closed_buyer', Boolean(viewPoHistoryData?.is_closed_buyer) || false);
            setValue('is_closed_seller', Boolean(viewPoHistoryData?.is_closed_seller) || false);
            // setValue('total_weight', viewPoHistoryData?.total_weight);

            setShowNoRecords(false);
            setShowLoader(false);
        } else {
            setShowNoRecords(true);
        }
    }, [viewPoHistoryData]);

    useEffect(() => {
        if(isRemoveLineBtnClicked) {
            setIsAddLineBtnClicked(false);
            // remove(editLineIndex);
            handleCancel()
        }
    }, [isRemoveLineBtnClicked])


    if (showNoRecords) {
        return (
            <>
                <div className={styles.savedBomPreviewContainer}>
                    <div className={styles.noOrderDetailsContainer}>
                        <p>NO ORDER DETAILS TO DISPLAY</p>
                        <p>SELECT SAVED PO FROM THE LEFT</p>
                    </div>
                </div>
            </>

        )
    }

    const handleSaveOrderChanges = (data: any) => {
        setValue('lastIndex', watch('cart_items').length-1);
        if(data.fulfilled_by){
            setShowConfirmationBox(true);
        }else{
            saveAddedLine();
        }
    }
console.log("cehck dae error ", errors)

    const handleRemoveLineBtnClick = async (index: number) => {
        try{
            const lineData = watch('cart_items')[index];
            const payload = {
                po_number: viewPoHistoryData?.buyer_po_number,
                type: "BUYER_ORDER_LINE_CANCEL",
                purchase_order_line_id: [lineData?.unique_id]
            }
            
            const response = await removePoHistoryLine(payload);
            console.log("response", response);
            if(response.error_message){
                console.log("error", response.error_message);
                return;
            }
            remove(index);
            const total_weight = calculateTotalWeightForProduct(watch(`cart_items`));
            setValue('total_weight', total_weight);
            setIsRemoveLineBtnClicked(false);
            setValue('lastIndex', watch('cart_items').length-1);
            setInitialLastIndex(watch('cart_items').length-1);
        }
        catch(error: any){
            console.log("error", error);
        }
    }


    const handleCancel = () => {
        setIsAddLineBtnClicked(false);
        if(watch('lastIndex') !== null && watch('cart_items').length){
            const addedItemList = watch('cart_items')?.slice(watch('lastIndex')+1, watch('cart_items').length) || [];
            for(let i = 0; i < addedItemList.length; i++){
                remove(watch('lastIndex') + i + 1);
            }
            calculateMaterialTotalPrice();
        }
    }

    return (
        (
            <>
                <div className={styles.savedBomPreviewContainer}>
                    <div className={styles.viewPoHistoryDetailsBg}>
                        <div className={styles.viewPoHistDetailsLeftSection}>
                            <div className={styles.detailRow}>
                                <span>JOB / PO#:</span>
                                <span>{viewPoHistoryData?.buyer_internal_po} ({viewPoHistoryData?.buyer_po_number})</span>
                            </div>
                             <div className={styles.detailRow}>
                                <span>DELIVERY DATE:</span>
                                <span>{dayjs(viewPoHistoryData?.delivery_date).format('ddd, MMM D, YYYY')}</span>
                            </div>
                             <div className={styles.detailRow}>
                                <span>DELIVERY DESTINATION:</span>
                                <span>{viewPoHistoryData?.shipping_details?.line1}, {viewPoHistoryData?.shipping_details?.line2}, {viewPoHistoryData?.shipping_details?.city}, {viewPoHistoryData?.shipping_details?.state_code}, {viewPoHistoryData?.shipping_details?.zip}</span>
                            </div>
                            <div className={styles.detailRow}>
                                <span>ORDER FULFILLED BY:</span>
                                <span>{viewPoHistoryData?.fulfilled_by || 'Not yet claimed'}</span>
                             </div>
                        </div>
                        <div className={styles.orderDocumentsMain}>
                            <span className={styles.title}>ORDER DOCUMENTS</span>
                            <div className={styles.orderBtn}>
                                <button>OC</button>
                                <button>PO</button>
                                <button>MTR</button>
                                <button>INV</button>
                            </div>
                        </div>
                    </div>
                    <div className={clsx(styles.createPOContainer)}>
                        <div className={clsx(styles.addPoLineTableContainer,isAddLineBtnClicked && styles.addPoLineTableContainerMinHeight)}>
                            <table className={styles.addPoLineTable}>
                                <thead>
                                    <tr>
                                        <th>LN</th>
                                        <th>DESCRIPTION</th>
                                        <th>QTY</th>
                                        <th> $/UNIT</th>
                                        <th>EXT ($)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {
                                        watch('cart_items').map((item: any, index: any) => {
                                            if (watch('lastIndex') !== null && index > watch('lastIndex')) {
                                                return <PoHistoryEditTile
                                                    key={item.id}
                                                    index={index}
                                                    watch={watch}
                                                    getValues={getValues}
                                                    setValue={setValue}
                                                    errors={errors}
                                                    register={register}
                                                    control={control}
                                                    userPartData={userPartData}
                                                    updateLineProduct={updateLineProduct}
                                                    quantitySizeValidator={quantitySizeValidator}
                                                />
                                            } else {
                                                return <SavedBomPreviewTile key={item.id} index={index} watch={watch} getValues={getValues} isRemoveLineBtnClicked={isRemoveLineBtnClicked} handleRemoveLineBtnClick={handleRemoveLineBtnClick} />
                                            }
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                        {isAddLineBtnClicked &&
                            <div className={styles.footerMain}>
                                <button onClick={handleCancel} className={styles.cancelBtn}>CANCEL</button>
                                <button onClick={handleSubmit(handleSaveOrderChanges)} className={styles.saveOrderBtn} disabled={!isValid || !handleSubmitValidation}>SAVE ORDER CHANGES</button>
                            </div>
                        }
                    </div>
                </div>
            </>

        )
    )
}

export default ViewPoHistory