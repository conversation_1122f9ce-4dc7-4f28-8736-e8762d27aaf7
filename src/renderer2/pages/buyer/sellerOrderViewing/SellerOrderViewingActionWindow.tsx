import React from 'react'
import styles from './SellerOrderViewingActionWindow.module.scss';
import { routes } from 'src/renderer2/common';
import { useLocation } from 'react-router-dom';
import clsx from 'clsx';

const SellerOrderViewingActionWindow = () => {
  const location = useLocation();

  const handleAcceptOrder = () => {
    // Handle accept order action
    console.log('Accept Order clicked');
  };

  return (
    <div className={styles.actionWindow}>
      <div className={styles.summaryCard}>
        <div className={styles.summaryCardContent}>
          <div className={styles.summaryRow}>
            <span>Material Total</span>
            <span>$ 0.00</span>
          </div>
          <div className={clsx(styles.summaryRow, styles.summaryRowSalesTax)}>
            <span>Sales Tax</span>
            <span>Exempt</span>
          </div>
        </div>
        <div className={styles.summaryTotal}>
          <span >Total Sale</span>
          <span >Exempt</span>
        </div>
      </div>
      
      <div className={styles.infoBox}>
        <p className={styles.infoText}>
          {
            location.pathname === routes.orderPage ? (
              `After clicking “Accept Order,” the next screen will be your order confirmation. Additionally, we will send you a purchase order for your records.`
            ) : location.pathname === routes.deleteOrderPage ? (
              `To perform any additional actions for this order, you must first “Undelete” to return it to the relevant screen.`
            ) : (
              `Once Preview Mode expires, this order will be removed from this screen and be accessible on the Claim Orders (C) screen.`
            )
          }
        </p>
      </div>
      {
        (location.pathname === routes.orderPage || location.pathname === routes.deleteOrderPage ) &&  (
          <button className={styles.acceptButtonDummy} >
            {location.pathname === routes.orderPage ? 'ACCEPT ORDER' : 'UNDELETE'}
          </button>
        )
      }
      {/* {
        (location.pathname === routes.previewOrderPage) && (
          <div className={styles.availableToClaim}>AVAILABLE TO CLAIM @ 10:39AM</div>
        )
      } */}
    </div>
  )
}

export default SellerOrderViewingActionWindow
