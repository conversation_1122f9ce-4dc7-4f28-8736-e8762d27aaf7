import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const shipmentsSchema = yup.object({
  id: yup.string().optional(),
  locationNickName: yup.string().required("Required"),
  locationAddress: yup.object().shape({
    line1: yup.string().trim().required('Line 1 is required'),
    line2: yup.string().trim().optional(),
    city: yup.string().trim().required('City is required'),
    state: yup.number().nullable().required('State is required'),
    stateCode: yup.string().nullable().required('State code is required'),
    zip: yup.string().trim().required('Zip code is required')
  }).required('Location address is required'),
  dates: yup.array().of(yup.mixed()).optional(),
  deliveryApptRequired: yup.boolean().default(false),
  deliveryContact: yup.object().shape({
    firstName: yup.string().trim().required("Required"),
    lastName: yup.string().trim().required("Required"),
    phone:yup.string().test('phone-digits', 'Phone number must have at least 10 digits', function(value) {
      if (!value) return true; // Let required validation handle empty values
      const digitCount = (value.match(/\d/g) || []).length;
      return digitCount >= 10;
    }).required('Phone number is required'),
    email:yup.string().trim().required("Required").test('valid-emails', 'Enter valid email', value => {
      if (!value) return true;
      const emails = value.split(',');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    }),
  }).required("Required"),
  shippingDocsEmail:  yup.string().trim().required("Required").test('valid-emails', 'Enter valid email', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  isDefault: yup.boolean().default(false),
});

export type ShipmentsFormData = yup.InferType<typeof shipmentsSchema>; 