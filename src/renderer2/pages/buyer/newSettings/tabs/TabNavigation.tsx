import React from 'react';
import styles from './TabNavigation.module.scss';
import { newSettingTabs } from '../../../../common';

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, setActiveTab }) => {
  return (
    <div className={styles.tabNavigation}>
      {newSettingTabs.map(tab => (
        <button
          key={tab.tab}
          className={`${styles.tabButton} ${activeTab === tab.tab ? styles.active : ''}`}
          onClick={() => setActiveTab(tab.tab)}
        >
          {tab.displayValue}
        </button>
      ))}
    </div>
  );
};

export default TabNavigation; 