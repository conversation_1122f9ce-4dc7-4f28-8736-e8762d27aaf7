import React, { useState, useEffect, useRef } from 'react';
import styles from './TabContent.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  FieldErrors,
  FieldValues,
  useForm,
  UseFormSetError,
  UseFormSetValue,
} from 'react-hook-form';
import { userSchema } from '../schemas';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import MultiStateSelector from 'src/renderer2/component/MultiStateSelector/MultiStateSelector';
import clsx from 'clsx';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import ChangePassword from 'src/renderer2/component/changePassword/changePassword';
import { buyerSettingConst, commomKeys, useBuyerSettingStore, useGlobalStore, emojiRemoverRegex, usePostExpireOtherSession, usePostValidateEmail, getChannelWindow, decryptData, encryptData, formatPhoneNumber } from '@bryzos/giss-ui-library';
import {
  formatPhoneNumberRemovingCountryCode,
  formatPhoneNumberWithHyphen,
  navigatePage,
  unformatPhoneNumber,
} from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import usePostVerifyZipCode from 'src/renderer2/hooks/usePostVerifyZipCode';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { OTPInputComponent } from 'src/renderer2/component/OTPInput';
import { Auth } from 'aws-amplify';
import DropdownSave from '../components/DropdownSave';
import { useQueryClient } from '@tanstack/react-query';
import { reactQueryKeys, routes } from 'src/renderer2/common';
interface InputFocusState {
  userType: boolean;
  firstName: boolean;
  email: boolean;
  password: boolean;
  phoneNumber: boolean;
  searchZipcode: boolean;
  stateSubscription: boolean;
  lastName: boolean;
}

const UserTab: React.FC<{
  setActiveTab: any;
  setSaveFunctions: any;
  routerContainerRef?: React.RefObject<HTMLDivElement>;
}> = ({
  setActiveTab,
  setSaveFunctions,
  routerContainerRef,
}) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(userSchema),
    mode: 'onSubmit',
  });


  const [isInputFocused, setIsInputFocused] = useState<any>({
    userType: false,
    firstName: false,
    email: false,
    password: false,
    phoneNumber: false,
    searchZipcode: false,
    stateSubscription: false,
    lastName: false,
  });
  const [openChangePassPopup, setOpenChangePassPopup] = useState(false);
  const [states, setStates] = useState<any[]>([]);
  const [selectedStates, setSelectedStates] = useState<number[]>([]);
  const [showOtpDialog, setShowOtpDialog] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const { deviceId, isImpersonatedUserLoggedIn, userData, referenceData, setForceLogout, decryptionEntity , setShowLoader , showLoader }: any =
    useGlobalStore();
  const changePassPopupRef = useRef(null);
  const effectiveContainerRef = routerContainerRef || changePassPopupRef;
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();
  const buyerSetting = useBuyerSettingStore((state) => state.buyerSetting);
  // @ts-ignore - setBuyerSettingInfo is used in other files but has typing issues
  const setBuyerSettingInfo = useBuyerSettingStore((state) => state.setBuyerSettingInfo);
  const {showCommonDialog, resetDialogStore}: any = useDialogStore();
  const {mutateAsync: expireOtherSessions} = usePostExpireOtherSession();
  const {mutateAsync: validateEmail} = usePostValidateEmail();
  const queryClient = useQueryClient();
  const [isZipcodeValidated, setIsZipcodeValidated] = useState(false);
  const isButtonDisabled = !isDirty || isSubmitting;

  useEffect(() => {
    setSaveFunctions({
        onSave: () => handleSubmit(handleSaveUser)(),
        isDisabled: isButtonDisabled,
    });
}, [isButtonDisabled, handleSubmit]);


  // Function to handle when email is different from buyerSetting's email
  const handleEmailChange = (currentEmail: string) => {
    // Add any other logic you want to execute when email changes
    if(currentEmail !== buyerSetting?.email_id){
      //show a popup to confirm the email change
      // saveUserSettingsonBlur();
      showCommonDialog(null, 'Are you sure you want to change your email? You will be logged out from all devices.', null, resetDialogStore, [{name: commomKeys.yes, action: ()=>resetUserEmail(true)}, {name: commomKeys.no, action: ()=>resetUserEmail(false)}])
    }
  };

  const resetUserEmail = async (proceed: boolean) => {
    resetDialogStore();
    try {
        if (proceed) {
          const validateEmailPayload = {
            data: {
              email_id: watch('email'),
            }
          }
          const validateEmailResponse = await validateEmail(validateEmailPayload);
          if (validateEmailResponse) {
            const expireSessionPayload = {
              data: {
                device_id: deviceId,
              }
            }
          //API call to logout other sessions
          await expireOtherSessions(expireSessionPayload);
          // Store the new email that needs to be verified
          const newEmail = watch('email');
          setNewEmail(newEmail);
          const user = await Auth.currentAuthenticatedUser();
          await Auth.updateUserAttributes(user, { email: newEmail });
          // Show OTP dialog for email verification
          setShowOtpDialog(true);
          }
          // Make API call to send OTP to new email
          // TODO: Implement API call to send OTP
        }
        else {
          resetData()
        }
    } catch (error) {
      showCommonDialog(null, error?.message || 'Something went wrong', null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: () => {resetDialogStore(); resetData() }}]);
      console.error('error', error);
    }
  };

    const resetData = () => {
      reset({
        email: buyerSetting?.email_id,
        firstName: buyerSetting?.first_name,
        lastName: buyerSetting?.last_name,
        phoneNumber:  buyerSetting?.phone
        ? formatPhoneNumber(
            formatPhoneNumberRemovingCountryCode(buyerSetting?.phone)
          )
        : '',
        searchZipcode: buyerSetting?.price_search_zip,
      })
    }

  // Create a separate form for OTP
  const {
    register: otpRegister,
    handleSubmit: otpHandleSubmit,
    formState: { errors: otpErrors },
    watch: otpWatch,
    reset: otpReset,
  } = useForm({
    defaultValues: {
      otp: '',
    },
  });



  const handleOtpSubmit = async (data: any) => {
    try{
      // TODO: Verify OTP with server
      // If successful, update the email
      // If failed, show error
      setShowLoader(true)
      await Auth.verifyCurrentUserAttributeSubmit('email', data.otp); 
      const channelWindow = getChannelWindow();
      const cred = window.electron.sendSync({ channel: channelWindow.getLoginCredential });
      let password = null;
      if (cred) {
          const data = JSON.parse(await decryptData(cred, decryptionEntity.decryption_key, true));
          password = data.password;
      }
      document.cookie.split(";").forEach((c) => {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
      });
      await Auth.signIn(watch('email'), password);
      await handleSubmit((data) => handleSaveUser(data, true))();
      // await saveUserSettings({route: 'user/settings', data: {
      //   email_id: watch('email')
      // }});
      setShowOtpDialog(false);
      setForceLogout(true);
    }catch(error){
      showCommonDialog(null, error?.message || 'Something went wrong', null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: ()=>resetDialogStore()}]);
      console.error('error', error);
    }finally{
      otpReset();
      setShowLoader(false)
    }
  };

  const handleOtpCancel = () => {
    setShowOtpDialog(false);
    otpReset();
    resetData();
  };

  useEffect(() => {
    if(buyerSetting) {
      setValue('firstName', buyerSetting?.first_name || '');
      setValue('lastName', buyerSetting?.last_name || '');
      setValue('email', buyerSetting?.email_id || '');
      setValue(
        'phoneNumber',
        buyerSetting?.phone
          ? formatPhoneNumber(
              formatPhoneNumberRemovingCountryCode(buyerSetting?.phone)
            )
          : ''
      );
      setValue('searchZipcode', buyerSetting?.price_search_zip || '');
    
    }
}, [buyerSetting]);

  useEffect(() => {
    setTimeout(() => {
      const firstNameInput = document.getElementById('firstName');
      if (firstNameInput) {
        firstNameInput.focus();
      }
    }, 100)
  }, []);


  // Load user settings from localStorage on component mount
  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleZipValidation = async (): Promise<any> => {
    if(watch('searchZipcode') && watch('searchZipcode').trim() !== ''){
    try {
      const res = await verifyZipCode({ zip_code: watch('searchZipcode') });
      if (res) {
        return true;
      } else {
        setError('searchZipcode', { message: 'Invalid zipcode' });
        return false;
      }
    } catch (error) {
        setError('searchZipcode', { message: 'Invalid zipcode' });
        return false;
      }
    }
  };

  const handleInputBlur = async (
    inputName: keyof InputFocusState
  ): Promise<void> => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
    if (dirtyFields["searchZipcode"]) {
      if (inputName === 'searchZipcode') {
         const res = await handleZipValidation();
         setIsZipcodeValidated(res);
      }
    }
  };

  const userTypes = [
    { title: 'Buyer', value: 'buyer' },
    { title: 'Seller', value: 'seller' },
    { title: 'Admin', value: 'admin' },
  ];

  const changePassPopup = () => {
    if (!isImpersonatedUserLoggedIn) setOpenChangePassPopup(true);
  };

  // Initialize states from reference data
  useEffect(() => {
    if (referenceData?.ref_states) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData]);

  // Sync selectedStates with form data when stateSubscription changes
  useEffect(() => {
    const stateSubscription = watch('stateSubscription');
    if (
      stateSubscription &&
      Array.isArray(stateSubscription) &&
      states.length > 0
    ) {
      // Check if the data contains state codes (strings) or state IDs (numbers)
      if (
        stateSubscription.length > 0 &&
        typeof stateSubscription[0] === 'string'
      ) {
        // Convert state codes to state IDs
        const stateIds = stateSubscription
          .map((stateCode) => {
            const state = states.find((s) => s.code === stateCode);
            return state ? state.id : null;
          })
          .filter((id) => id !== null);
        setSelectedStates(stateIds);
        setValue('stateSubscription', stateIds); // Update form with IDs
      } else {
        // Data is already in ID format
        setSelectedStates(stateSubscription);
      }
    }
  }, [watch('stateSubscription'), states]);

  // Handle state selection changes (no auto-save)
  const handleStateSelectionChange = (newSelectedStates: number[]) => {
    setSelectedStates(newSelectedStates);
    setValue('stateSubscription', newSelectedStates);
  };

  // Handle update states button click (trigger blur to save everything)
  const handleUpdateStates = async (statesToSave: number[]) => {
    // Update the selected states
    setSelectedStates(statesToSave);
    setValue('stateSubscription', statesToSave);

    // Trigger the blur save which will now include the updated state subscription
    // await saveUserSettingsonBlur();
  };

  const handleSaveUser = async (data: any , emailChanged: boolean = false) => {
    try{
      if(watch('email') !== buyerSetting?.email_id && !emailChanged){
        handleEmailChange(watch('email'));
      }else{
        let isZipValid = isZipcodeValidated;
        if(!isZipValid){
          isZipValid = await handleZipValidation();
        }
          if(isZipValid){
              setShowLoader(true)
              const payload = {
                  first_name: data.firstName,
                  last_name: data.lastName,
                  email_id: data.email,
                  phone: unformatPhoneNumber(data.phoneNumber),
                  price_search_zip: data.searchZipcode,
              }
              await saveUserSettings({route: 'user/buyer/settings', data: payload})
              reset(data); 
              setShowLoader(false)
          }
      }
    }catch(err){
        console.error(err)
    }
  }

  return (
    <div className={clsx(styles.tabContent,styles.userTabContent)} ref={changePassPopupRef}>

      <div className={styles.formContainer}>
        {/* USER TYPE */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.userType && styles.focusLbl)}
              htmlFor='userType'
            >
              USER TYPE
            </label>
          </span>
          <span className={styles.col1}>
            <div className={styles.inputCreateAccount}>{userData?.data?.type === "BUYER" ? "Buyer" : userData?.data?.type || 'BUYER'}</div>
          </span>
        </div>

        {/* YOUR FIRST & LAST NAME */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx((isInputFocused.firstName || isInputFocused.lastName) && styles.focusLbl)}
              htmlFor='firstName'
            >
              YOUR FIRST & LAST NAME
            </label>
          </span>
          <span className={clsx(styles.col1,styles.colGap)}>
            <span className={clsx(styles.col1,styles.inputMain)}>
                 <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.firstName && styles.error
                )}
                id='firstName'
                type='text'
                register={register('firstName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('firstName').onBlur(e);
                  handleInputBlur('firstName');
                }}
                onFocus={() => handleInputFocus('firstName')}
                errorInput={errors?.firstName}
                onKeyDown={(e) => {
                  if(e.key === 'Tab'){
                    if(e.shiftKey){
                      setActiveTab('COMPANY');
                    }
                  }
                }}
              />
            </InputWrapper>
            </span>
          <span className={clsx(styles.col1,styles.inputMain)}>
                <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.lastName && styles.error
                )}
                id='lastName'
                type='text'
                register={register('lastName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('lastName').onBlur(e);
                  handleInputBlur('lastName');
                }}
                onFocus={() => handleInputFocus('lastName')}
                errorInput={errors?.lastName}
                onKeyDown={(e) => {
                  if(e.key === 'Tab'){
                    if(e.shiftKey){
                      setActiveTab('COMPANY');
                    }
                  }
                }}
              />
            </InputWrapper>

          </span>
           
          </span>
        </div>

        {/* YOUR EMAIL ADDRESS */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.email && styles.focusLbl)}
              htmlFor='email'
            >
              YOUR EMAIL ADDRESS
            </label>
          </span>
          <span className={clsx(styles.col1,styles.inputMain)}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.email && styles.error
                )}
                type='email'
                register={register('email')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('email').onBlur(e);
                  handleInputBlur('email');
                  // if (buyerSettingEmail &&   !== buyerSettingEmail) {
                  //   handleEmailChange(e.currentTarget.value);
                  // }
                }}
                onFocus={() => handleInputFocus('email')}
                errorInput={errors?.email}
              />
            </InputWrapper>
          </span>
        </div>

        {/* YOUR PASSWORD */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.password && styles.focusLbl)}
              htmlFor='password'
            >
              YOUR PASSWORD
            </label>
          </span>
          <span className={clsx(styles.col1)}>
            <span
              onClick={changePassPopup}
              className={clsx(styles.inputCreateAccount, styles.changePassword)}
              tabIndex={0}
              onKeyDown={(e) => {
                if(e.key === 'Enter'){
                  changePassPopup();
                }
              }}
            >
              Change Password
            </span>
          </span>
        </div>

        {/* YOUR PHONE NUMBER */}
        <div className={styles.formGroupInput}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.phoneNumber && styles.focusLbl)}
              htmlFor='phoneNumber'
            >
              YOUR PHONE NUMBER
            </label>
          </span>
         <span className={clsx(styles.col1,styles.inputMain)}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.phoneNumber && styles.error
                )}
                type='tel'
                register={register('phoneNumber')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('phoneNumber').onBlur(e);
                  handleInputBlur('phoneNumber');
                }}
                onFocus={() => handleInputFocus('phoneNumber')}
                errorInput={errors?.phoneNumber}
                mode='phoneNumber'
              />
            </InputWrapper>
          </span>
        </div>

        {/* DEFAULT ZIPCODE FOR PRICE SEARCH */}
        <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
          <span className={styles.col1}>
            <label
              className={clsx(isInputFocused.searchZipcode && styles.focusLbl)}
              htmlFor='searchZipcode'
            >
              DEFAULT ZIPCODE FOR <br /> PRICE SEARCH
            </label>
          </span>
         <span className={clsx(styles.col1,styles.inputMain)}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.searchZipcode && styles.error
                )}
                type='text'
                register={register('searchZipcode')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('searchZipcode').onBlur(e);
                  handleInputBlur('searchZipcode');
                }}
                onFocus={() => handleInputFocus('searchZipcode')}
                errorInput={errors?.searchZipcode}
                maxLength={5}
                mode='wholeNumber'
                onKeyDown={(e) => {
                  if (e.key === 'Tab') {
                    if(!e.shiftKey){
                      e.preventDefault();
                    const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                    if (saveButton) {
                      if (saveButton.disabled) {
                        const companyButton = document.getElementById('COMPANY')
                        if (companyButton) {
                          (companyButton as HTMLElement).focus();
                        }
                      } else {
                        setTimeout(() => {
                          saveButton.focus();
                        }, 0);
                      }
                    }
                  }
                  }
                }}
              />
            </InputWrapper>
          </span>
        </div>

        {/* STATE SELECTION FOR PRICING */}
        <div
          className={clsx(
            styles.formGroupInput,
            styles.stateSelectionGroup,
            styles.bdrBtm0
          )}
        >
          <span className={styles.col1}>
            <label
              className={clsx(
                isInputFocused.stateSubscription && styles.focusLbl
              )}
              htmlFor='stateSubscription'
            >
              STATE SELECTION FOR PRICING
            </label>
          </span>
          <span className={clsx(styles.col1,styles.inputMain)}>
            <MultiStateSelector
              states={states}
              selectedStates={selectedStates}
              onSelectionChange={handleStateSelectionChange}
              onUpdateStates={handleUpdateStates}
              onFocus={() => handleInputFocus('stateSubscription')}
              onBlur={() => handleInputBlur('stateSubscription')}
              error={errors?.stateSubscription}
            />
          </span>
        </div>
      </div>
      
      {/* OTP Verification Dialog */}
      <Dialog
        open={showOtpDialog}
        onClose={handleOtpCancel}
        transitionDuration={100}
        container={effectiveContainerRef.current}
        disableScrollLock={true}
         style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(0, 0, 0, 0.23)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 10px 10px',
          opacity: showLoader ? 0 : 1
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={handleOtpCancel}
        >
          <CloseIcon />
        </button>
        
        <div className={styles.changePasswordWrapper}>
          <h2 className={styles.changePasswordTitle}>VERIFY YOUR EMAIL</h2>
          
          <div className={styles.changePasswordContent}>
            <p className={styles.verificationText}>
              We've sent a verification code to:
            </p>
            <p className={styles.emailDisplay}>{newEmail}</p>
            <p className={styles.instructionText}>
              Please enter the 6-digit code below:
            </p>
            
            <form onSubmit={otpHandleSubmit(handleOtpSubmit)}>
              <div className={styles.inputGroup}>
                <InputWrapper>
                  <CustomTextField 
                    register={otpRegister("otp")}
                    className={styles.otpInput}
                    onChange={(e: any) => {
                      e.target.value = e.target.value.replace(emojiRemoverRegex, '');
                      otpRegister("otp").onChange(e);
                    }}
                    placeholder='Enter 6-digit code' 
                    maxLength={6} 
                    mode="wholeNumber" 
                    errorInput={otpErrors.otp?.message}
                  />
                </InputWrapper>
              </div>
              
              <div className={styles.buttonGroup}>
                <button
                  type="button"
                  onClick={handleOtpCancel}
                  className={styles.cancelButton}
                >
                  CANCEL
                </button>
                <button
                  type="submit"
                  className={styles.verifyButton}
                >
                  VERIFY
                </button>
              </div>
            </form>
          </div>
        </div>
      </Dialog>

      <Dialog
        open={openChangePassPopup}
        onClose={(event) => setOpenChangePassPopup(false)}
        transitionDuration={100}
        container={effectiveContainerRef.current}
        disableScrollLock={true}
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(0, 0, 0, 0.23)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 10px 10px',
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={(event) => setOpenChangePassPopup(false)}
        >
          <CloseIcon />
        </button>
        <ChangePassword
          closeDialog={() => {
            setOpenChangePassPopup(false);
          }}
          deviceId={deviceId}
        />
      </Dialog>
    </div>
  );
};

export default UserTab;
