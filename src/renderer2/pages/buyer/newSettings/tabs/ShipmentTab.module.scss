.shipmentTabContentContainer{
    padding: 32px 0px 24px 0px;
    overflow-y: auto;
    &::-webkit-scrollbar {
        width: 5px;
        height: 6px;
      }
    
      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.32);
        border-radius: 50px;
      }
    
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    .shipmentTabContentHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 26px 20px 26px;
      h2 {
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: -0.72px;
        color: #fff;
      }
      button{
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        color: rgba(255, 255, 255, 0.85);
        display: flex;
        gap: 4px;
      }
    }
    .shipmentTabContentBody {
        padding: 0px 26px;
      .shipmentTabContent {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: solid 1px rgba(255, 255, 255, 0.07);
        .shipmentTabContentTitle {
          width: 50%;
          font-family: Syncopate;
          font-size: 18px;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: -0.72px;
          text-align: left;
          color: #71737f;
          text-transform: uppercase;
        }
        .focusLbl {
          color: #fff;
        }
        .shipmentTabContentValue {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 5px;
        }
        .locationAddressContainer {
          height: 100%;
          .addressDisplayContainer{
            height: 100%;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            width: 100%;
            .placeHolderDiv {
              padding: 13px 16px;
              font-family: Inter;
              font-size: 12px;
              font-weight: 300;
              font-stretch: normal;
              font-style: normal;
              line-height: 1;
              letter-spacing: -0.48px;
              text-align: left;
              color: #71737f;
              text-transform: uppercase;
              width: 100%;
              height: 100%;
              display: block;
            }
            .valueDiv {
              font-family: Inter;
              font-size: 14px;
              font-weight: normal;
              font-stretch: normal;
              font-style: normal;
              line-height: 1;
              letter-spacing: 0.56px;
              text-align: left;
              color: #fff;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              padding: 23px 15px;
              height: 100%;
              .lastAddressFiled {
                display: flex;
                gap: 50px;
                .addressInputsCol1 {
                  flex: 1;
                }
              }
            }
          }
        }
        .deliveryApptRequiredContainer{
          padding: 8px;
          border-radius: 13px;
          background-color: rgba(255, 255, 255, 0.04);
        }
      }
    }
  }
  .inputCreateAccount {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 6px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #fff;
    transition: background 0.1s;

    &.arBryzosCom{
      color: rgba(255, 255, 255, 0.4);
      &:focus{
         background: rgba(255, 255, 255, 0.04);
         color: rgba(255, 255, 255, 0.4);
      }
     }

    &.sendInvoiceEmailInput{
      text-overflow: ellipsis;
    }

    &.error {
      background: url(../../../../assets/New-images/Create-Account/error-input.svg)
        no-repeat;
      background-size: cover;
      box-shadow: none;

      &:focus {
        background-color: green;
        background: url(../../../../assets/New-images/Create-Account/error-input.svg)
          no-repeat;
        background-size: cover;
        color: #fff;
      }
    }

    &:focus {
      outline: none;
      color: #459fff;
      background: url(../../../../assets/New-images/Create-Account/input-active.svg)
        no-repeat;
      background-position: bottom right;
      background-size: cover;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }
  .stripePaymentGrid {
    column-gap: 12px;
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
  }
  .stripeElement {
    width: 100%;
    height: 40px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0 12px 0 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
  
    & > div {
      width: 100%;
    }
  
    /* Extra styling to ensure the iframe is visible */
    iframe {
      opacity: 1 !important;
      height: 24px !important;
      min-height: 24px !important;
      width: 100% !important;
    }
  }
  .companyHQAddressContainer{
    height: 160px;
    
    .customAddressContainer {
      width: 100%;
      display: flex;
      flex-direction: column;
      row-gap: 4px;
      padding: 8px;
      border-radius: 12px;
      box-shadow: inset 5px 5px 7.9px -2px #000;
      background-color: rgba(255, 255, 255, 0.04);
  
      input {
        width: 100%;
        font-size: 14px;
      }
  
      .zipInputContainer {
        display: flex;
        gap: 4px;
  
        .col1 {
          flex: 0 0 1;
        }
  
        .col2{
          flex: 0 0 77px;
        }
        .col3 {
         flex: 0 0 104px;
        }
      }
    }
}
.receivingHoursInput {
    height: 100px;
    padding: 8px 0px;
  
    .lblInput {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      border-right: solid 1px #000;
  
      .lblReceivingHours {
        font-size: 12px;
        padding-left: 6px;
      }
    }
  
    .inputSectionRecevingHours {
      width: 100%;
      height: 100%;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
  
      label {
        cursor: pointer;
  
        &:hover {
          color: #fff;
        }
      }
  
      input[type='checkbox'] {
        display: none;
      }
  
      .daylbl1 {
        font-family: Syncopate;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: center;
        color: #c3c4ca;
        text-transform: uppercase;
        margin-bottom: 4px;
      }
    }
  }
  .footerContainer {
    display: flex;
    padding: 32px 26px 0px;
    button{
        padding: 8px 42px 8px 41px;
        border-radius: 500px;
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
    }
    .saveBtn {
        color: rgba(255, 255, 255, 0.5);
        margin-left: auto;
        &:disabled{
            color: rgba(255, 255, 255, 0.5);
        }
        &:hover{
            color: #fff;
            &:disabled{
                color: rgba(255, 255, 255, 0.5);
            }
        }
    }
    .deleteBtn {
        color: #ff4848;
    }
  }

  .deleteDialogContainer {
    width: 504px;
    height: 411px;
    background-color: #0f0f14;
    border-radius: 50px;
    padding: 64px 64px 48px 64px;
    .deleteDialogTitle {
      font-family: Syncopate;
    font-size: 24px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.96px;
    text-align: center;
    color: #fff;
    text-transform: uppercase;
    padding-bottom: 48px;
    }
    .deleteBtnSection{
      display: flex
;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    justify-content: center;
    button{
      width: 352px;
    height: 69px;
    border-radius: 50px;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    text-transform: uppercase;
    }
    .submitYesBtn {
      background-color: #32ff6c;
      color: #000;
      &:hover{
        background-color: #2b2c32;
        color: #32ff6c;
      }
    }
    .submitNoBtn {
      background-color: #2b2c32;
      color: #fff;
      &:hover{
        color: #32ff6c;
      }
    }
    }
  }
  .Dropdownpaper.Dropdownpaper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    gap: 8px;
    padding: 4px;
    border-radius: 6.2px;
    -webkit-backdrop-filter: blur(15.4px);
    backdrop-filter: blur(15.4px);
    background-color: rgba(113, 115, 127, 0.7);
    padding: 0px;
    margin-top: 4px;
  
    .muiMenuList {
      padding: 4px;
  
      li {
        padding: 4px 16px;
        border-radius: 8px;
        font-family: Inter;
        font-size: 10.8px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.7);
        background-color: transparent;
  
        &:hover {
          border-radius: 4.6px;
          background-color: rgba(255, 255, 255, 0.4);
          color: #000;
          font-weight: bold;
        }
      }
    }
  
    &.Dropdownpaper1 {
      width: auto;
      overflow: hidden;
      padding-right: 5px;
      padding-top: 4px;
      padding-bottom: 4px;
  
      ul {
        max-height: 220px;
        overflow: auto;
  
        &::-webkit-scrollbar {
          width: 1.5px;
        }
  
        &::-webkit-scrollbar-track {
          background: transparent;
        }
  
        &::-webkit-scrollbar-thumb {
          border-radius: 8px;
          background-color: #8b91a6;
        }
  
        li {
          font-size: 14px;
          margin-right: 4px;
        }
      }
    }
  
    &.resaleCertdropdown {
      width: auto;
      overflow: hidden;
      padding-right: 5px;
      padding-top: 4px;
      padding-bottom: 4px;
  
      ul {
        max-height: 220px;
        overflow: auto;
  
        &::-webkit-scrollbar {
          width: 1.5px;
        }
  
        &::-webkit-scrollbar-track {
          background: transparent;
        }
  
        &::-webkit-scrollbar-thumb {
          border-radius: 8px;
          background-color: #8b91a6;        
        }
  
        li {
          font-size: 14px;
          margin-right: 4px;
        }
      }
    }
  
    &.receivingHoursTop {
      padding-right: 3px;
       margin-top:3px;
      border-radius: 6px 6px 6px 6px;
  
      ul {
        &::-webkit-scrollbar {
          width: 1.5px;
        }
  
        li {
          font-size: 12px;
          margin-right: 0px;
          padding: 4.6px 7px;
          border-radius: 4px;
        }
      }
    }
  
    &.receivingHoursBottom {
      padding-right: 3px;
      margin-top: 2px;
      border-radius: 6px 6px 6px 6px;
  
      ul {
        &::-webkit-scrollbar {
          width: 1.5px;
        }
  
        li {
          font-size: 12px;
          margin-right: 0px;
          padding: 4.6px 7px;
          border-radius: 4px;
        }
      }
    }
  }
  .dropDownBG.dropDownBG {
    width: 110px;
    z-index: 999;
    padding: 4px;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #9c9da5;
    margin-top: 10px;
    
        ul {
            padding: 0px;
    
            li {
            font-family: Inter;
            font-size: 14px;
            font-weight: 500;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: #191a20;
            margin-bottom: 2px;
            &[aria-selected="true"] {
                border-radius: 6px;
                background-color: #e0e0e0;
            }
            &:hover {
                border-radius: 6px;
                background-color: #e0e0e0;
            }
            }
        }
    }