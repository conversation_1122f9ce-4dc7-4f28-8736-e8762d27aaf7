// import { CheckIcon, ChevronDownIcon, PlusIcon } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import styles from "./ShipmentListing.module.scss";
import { Dialog } from "@mui/material";
import ShipmentsTab from "../ShipmentsTab";
import { ReactComponent as DefaultIcon } from '../../../../../assets/New-images/New-Image-latest/icon-check.svg';
import { RecevingHoursFrom, RecevingHoursTo } from 'src/renderer2/common';
import { useBuyerSettingStore } from "@bryzos/giss-ui-library";

const ShipmentListing = ({ shipmentListingRef }: { shipmentListingRef: React.RefObject<HTMLDivElement> }): JSX.Element => {
  
  const [isCreateNewAddress, setIsCreateNewAddress] = useState(false);
  const [addressDialogOpen, setAddressDialogOpen] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [shipmentList, setShipmentList] = useState([]);
  const { buyerSetting }: any = useBuyerSettingStore();

  useEffect(() => {
    if(buyerSetting){
      setShipmentList(buyerSetting.delivery_addresses || []);
    }
  }, [buyerSetting]);

  // Helper function to get display title for from time
  const getFromTimeTitle = (fromValue: string): string => {
    const found = RecevingHoursFrom.find((item: any) => 
      item.value === (fromValue === 'closed' ? 'closed' : parseInt(fromValue))
    );
    return found ? found.title : fromValue;
  };

  // Helper function to get display title for to time
  const getToTimeTitle = (toValue: string): string => {
    const found = RecevingHoursTo.find((item: any) => 
      item.value === (toValue === 'closed' ? 'closed' : parseInt(toValue))
    );
    return found ? found.title : toValue;
  };

  const handleAddressDialog = (isCreateNewAddress: boolean, shipmentDetails: any = null) => {
    setAddressDialogOpen(true);
    setIsCreateNewAddress(isCreateNewAddress);
    setSelectedShipment(shipmentDetails);
  }

  return (
    <div className={styles.container} >
      <div className={styles.navigationSection}>

        {/* Create New Ship-to button */}
        <button className={styles.createButton} onClick={() => handleAddressDialog(true)}>
          Create New Ship-to
          <div className={styles.createButtonIcon}>
            +
            {/* <PlusIcon className={styles.plusIcon} /> */}
          </div>
        </button>
      </div>

      {/* Shipments Table */}
      <div className={styles.tableCard}>
        <table className={styles.table}>
          <thead className={styles.tableHeader}>
            <tr className={styles.tableHeaderRow}>
              <th className={styles.tableHeaderCell}>
                LOCATION
                <br />
                NICKNAME
              </th>
              <th className={styles.tableHeaderCell}>
                ADDRESS
              </th>
              <th className={styles.tableHeaderCell}>
                HOURS
              </th>
              <th className={styles.tableHeaderCell}>
                APPT
                <br />
                REQ&apos;D
              </th>
              <th className={styles.tableHeaderCell}>
                DELIVERY
                <br />
                CONTACT
              </th>
              <th className={styles.tableHeaderCell}>
                EMAIL
                <br />
                DOCS TO
              </th>
              <th className={styles.tableHeaderCell}>
                SET AS
                <br />
                DEFAULT
              </th>
              <th className={styles.tableHeaderCell} style={{ width: '80px' }}></th>
            </tr>
          </thead>
          <tbody className={styles.tableBody}>
          {shipmentList.map((shipmentLocation: any, index: number) => (
              <React.Fragment key={shipmentLocation.location_nickname || index}>
                <tr className={styles.tableRow}>
                  <td className={styles.tableCell}>
                    {shipmentLocation.location_nickname}
                  </td>
                  <td className={`${styles.tableCell} ${styles.addressCell}`}>
                    {shipmentLocation?.line1}
                    {shipmentLocation?.line2 && <br />}
                    {shipmentLocation?.line2}
                    {shipmentLocation?.city && <br />}
                    {shipmentLocation?.city}
                    {shipmentLocation?.state_code && <span>,&nbsp;</span>}
                    {shipmentLocation?.state_code}
                    {shipmentLocation?.zip && <span>&nbsp;</span>}
                    {shipmentLocation?.zip}
                  </td>
                  <td className={`${styles.tableCell} ${styles.hoursCell}`}>
                    <div className={styles.hoursCellContent}>
                      {shipmentLocation?.user_delivery_receiving_availability_details?.map((hour: any, hourIndex: any) => (
                        <div key={hourIndex}>{hour.display_name} {getFromTimeTitle(hour.from)} - {getToTimeTitle(hour.to)}</div>
                      ))}
                    </div>
                  </td>
                  <td className={styles.tableCell}>
                    {Boolean(shipmentLocation.is_appt_required) ? "Yes" : "No"}
                  </td>
                  <td className={`${styles.tableCell} ${styles.contactCell}`}>
                    <div>{shipmentLocation.first_name} {shipmentLocation.last_name}</div>
                      {shipmentLocation.email_id}
                    <div>{shipmentLocation.phone}</div>
                  </td>
                  <td className={`${styles.tableCell} ${styles.emailCell}`}>
                      {shipmentLocation.shipping_docs_to}
                  </td>
                  <td className={`${styles.tableCell} ${styles.center}`}>
                    {Boolean(shipmentLocation.is_default) && (
                      <span className={styles.defaultIndicator}><DefaultIcon /></span>
                    )}
                  </td>
                  <td className={styles.tableCell}>
                    <button className={styles.editButton} onClick={() => handleAddressDialog(false , shipmentLocation)}>
                      Edit
                    </button>
                  </td>
                </tr>
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <Dialog
        open={addressDialogOpen}
        onClose={(event) =>  setAddressDialogOpen(false)}
        transitionDuration={100}
        disableScrollLock={true}
        container={shipmentListingRef.current}
        
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(0, 0, 0, 0.23)',
                    border: '1px solid transparent',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0,
                        width: '100%',
                        maxWidth: '735px',
                        borderRadius: '16px',
                        boxShadow: '0 0 67.4px 4px #000',
                        backgroundColor: '#222329',
                    }
                }}
                hideBackdrop
                classes={{
                    root: styles.customeAddressPopup,
                    paper: styles.dialogContent
                }}
      >
        <ShipmentsTab selectedShipment={selectedShipment} isCreate={isCreateNewAddress} closeDialog={() => setAddressDialogOpen(false)}/> 
      </Dialog>
      
    </div>
  );
};

export default ShipmentListing;