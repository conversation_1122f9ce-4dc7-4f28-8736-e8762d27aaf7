.container {
    width: 100%;
    height: 100%;
    background-color: #191a20;
    padding-top: 24px;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .saveButtonGroup {
    display: flex;
  }
  
  .saveButton {
    height: 36px;
    background-color: rgba(255, 255, 255, 0.04);
    color: #9b9eac;
    border-top-left-radius: 9999px;
    border-bottom-left-radius: 9999px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border: none;
    cursor: pointer;
    padding: 0 16px;
    transition: background-color 0.2s;
  
    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }
  
  .dropdownButton {
    height: 36px;
    width: 30px;
    padding: 0;
    background-color: rgba(255, 255, 255, 0.04);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 9999px;
    border-bottom-right-radius: 9999px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  
    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }
  
  .chevronIcon {
    height: 16px;
    width: 16px;
  }
  
  .settingsTitle {
    text-shadow: 0px 4.86px 42.15px -32.43px rgba(255, 0, 153, 0.12);
    -webkit-text-stroke: 0.81px rgba(255, 223, 205, 0.6);
    font-family: 'Syncopate', Helvetica;
    font-weight: bold;
    color: white;
    font-size: 18px;
    letter-spacing: -0.72px;
    line-height: 23.4px;
  }
  
  .navigationSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    position: absolute;
    right: 0;
    top: 43px;
  }
  
  .tabsList {
    background: transparent;
    height: auto;
    gap: 4px;
    display: flex;
  }
  
  .tabTrigger {
    padding: 0 12px;
    height: 36px;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
  
    &.active {
      background-color: #c3c4ca;
      color: #0f0f14;
    }
  
    &.inactive {
      background-color: rgba(255, 255, 255, 0.04);
      color: #71737f;
  
      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }
    }
  }
  
  .createButton {
    height: 36px;
    background-color: rgba(255, 255, 255, 0.04);
    color: #71737f;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    padding: 0 4px 0 16px;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
  
    &:hover {
      color: #fff;

    }
  }
  
  .createButtonIcon {
    margin-left: 8px;
    width: 26px;
    height: 26px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 25px;
    &:hover{
      color: #fff;
    }
  }
  
  .plusIcon {
    height: 12px;
    width: 12px;
  }
  
  .tableCard {
    border: 0;
    background: transparent;
    padding-bottom: 16px;
    overflow: auto;
    border-radius: 10px 10px 0px 0px;
    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background:
        #9da2b2;
      border-radius: 50px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
    
  }
  
  .tableHeader {
    background-color: rgba(255, 255, 255, 0.15);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  
  .tableHeaderRow {
    border: none;
  }
  
  .tableHeaderCell {
    height: 46px;
    font-family: 'Syncopate', Helvetica;
    font-weight: bold;
    color: white;
    font-size: 14px;
    letter-spacing: -0.56px;
    line-height: 18.2px;
    padding: 12px 16px;
    text-align: left;
    vertical-align: middle;
    padding: 6px 0px 4px 12px;
  }
  
  .tableBody {
  }
  
  .tableRow {
    height: 61px;
    background-color: transparent;
    border: none;
    &:nth-child(even) {
      background-color: #222329;
    }
  
    &.spacer {
      margin-top: 10px;
    }
  }
  
  .tableCell {
    padding: 6px 0px 6px 12px;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    &:last-child{
      padding-right: 8px;
    }
  
    &.center {
      text-align: center;
    }
    .hoursCellContent{
      overflow: auto;
      max-height: 48px;
      padding-right: 10px;
      width: 125px;
      &::-webkit-scrollbar {
        width: 2px;
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        background:
          #8b91a6;
        border-radius: 50px;
      }
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }
  }
  
  .addressCell {
    white-space: pre-line;
  }
  
  .hoursCell {
    div {
      margin-bottom: 2px;
    }
  }
  
  .contactCell {
    div {
      margin-bottom: 2px;
    }
  
    a {
      color: white;
      text-decoration: none;
  
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .emailCell {
    a {
      color: white;
      text-decoration: none;
  
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .checkIcon {
    width: 26px;
    height: 35px;
    color: #22c55e;
    margin: 0 auto;
  }
  
  .editButton {
    height: 23px;
    color: #fff;
    font-size: 12px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 25px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 4px 26px 3px 27px;
  
    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  }