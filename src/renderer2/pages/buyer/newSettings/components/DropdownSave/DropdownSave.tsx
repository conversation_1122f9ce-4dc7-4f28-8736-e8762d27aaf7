import React, { useState, useRef } from 'react';
import { ClickAwayListener } from '@mui/material';
import styles from './Dropdown.module.scss';
import { ReactComponent as DropdownIcon } from '../../../../../assets/New-images/StateIconDropDpown.svg';
import clsx from 'clsx';
import { navigatePage } from 'src/renderer2/helper';
import { routes } from 'src/renderer2/common';

interface DropdownSaveButtonProps {
    onSave: () => void | null | undefined;
    isDisabled?: boolean;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    className?: string;
    buttonText?: string;
}

const DropdownSave: React.FC<DropdownSaveButtonProps> = ({
    onSave,
    isDisabled = false,
    position = 'top-right',
    className = '',
    buttonText = 'Save',
}) => {


    const getPositionStyles = (): React.CSSProperties => {
        switch (position) {
            case 'top-left':
                return { position: 'absolute', top: 0, left: 0 };
            case 'bottom-right':
                return { position: 'absolute', bottom: 0, right: 0 };
            case 'bottom-left':
                return { position: 'absolute', bottom: 0, left: 0 };
            default:
                return { position: 'absolute', top: 0, left: 0 };
        }
    };

    return (
        <div style={getPositionStyles()} className={className}>
            <div className={styles.buttonContainer}>
                <button
                    id='settings-save-button'
                    disabled={isDisabled}
                    onClick={onSave}
                    className={styles.saveButton}
                >
                    {buttonText}
                </button>
            </div>
        </div>
    );
};

export default DropdownSave;
