import React, { useState, useEffect, useRef } from 'react';
import styles from './InteractiveStateSelector.module.scss';
import clsx from 'clsx';
import { Popover, Tooltip } from '@mui/material';
import { ReactComponent as Shape1 } from '../../../../../assets/New-images/New-Image-latest/state-select-multi1.svg';
import { ReactComponent as Shape2 } from '../../../../../assets/New-images/New-Image-latest/state-select-multi-right.svg';

import { ReactComponent as DropdownIcon } from '../../../../../assets/New-images/StateIconDropDpown.svg';
interface State {
  state_code: string;
}

interface MultiStateSelectorProps {
  states: State[];
  value?: string[];
  onChange: (stateCodes: string[]) => void;
  onBlur?: () => void;
  error?: boolean;
  placeholder?: string;
}

const MultiStateSelector: React.FC<MultiStateSelectorProps> = ({
  states,
  value = [],
  onChange,
  onBlur,
  error,
  placeholder = "State"
}) => {
  const [filterText, setFilterText] = useState('');
  const [filteredStates, setFilteredStates] = useState<State[]>(states);
  const [isFocused, setIsFocused] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  // Always show selected states + filter text, comma separated
  const getInputValue = () => {
    const selectedText = value.length > 0 ? value.join(', ') : '';
    if (isFocused && filterText) {
      return selectedText ? `${selectedText}, ${filterText}` : filterText;
    }
    return selectedText;
  };

  const inputValue = getInputValue();

  // Simple function to position cursor at end and scroll to show it
  const positionCursorAtEnd = () => {
    if (!inputRef.current) return;

    setTimeout(() => {
      if (inputRef.current) {
        const length = inputRef.current.value.length;
        inputRef.current.setSelectionRange(length, length);
        // Scroll to the end to show cursor
        inputRef.current.scrollLeft = inputRef.current.scrollWidth;
      }
    }, 0);
  };

  // Always show all states, filtering is handled by styling
  useEffect(() => {
    setFilteredStates(states);
  }, [states]);

  // Get navigable states (either all states or filtered states based on input)
  const getNavigableStates = () => {
    if (!filterText.trim()) {
      return states; // Include all states, including selected ones
    }
    return states.filter(state =>
      state.state_code.toLowerCase().startsWith(filterText.toLowerCase())
    );
  };

  // Get the current navigable states
  const navigableStates = getNavigableStates();

  // Get styling class for each state based on filter and selection
  const getStateClass = (stateCode: string) => {
    const isSelected = value.includes(stateCode);
    const isExactMatch = filterText && stateCode.toLowerCase() === filterText.toLowerCase();
    const isStartsWithMatch = filterText && stateCode.toLowerCase().startsWith(filterText.toLowerCase());
    
    // Check if this state is hovered by finding its index in navigableStates
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    const isHovered = navigableIndex === hoveredIndex && navigableIndex >= 0; // Only hover if state is navigable
    
    return clsx(
      styles.stateItem,
      isSelected && styles.selected,
      isHovered && styles.hovered,
      !filterText && styles.default, // grey by default when no filter
      isExactMatch && styles.exactMatch, // blue for exact match
      isStartsWithMatch && !isExactMatch && styles.startsWithMatch, // white for startsWith matches
      filterText && !isStartsWithMatch && styles.noMatch // grey for non-matches when filtering
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    const selectedText = value.length > 0 ? value.join(', ') : '';

    // If no selected states, handle normally
    if (!selectedText) {
      setFilterText(newValue);
    }
    // Character input is handled in keydown, this mainly handles paste operations

    setHoveredIndex(-1);
  };



  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle regular character input to prevent editing selected states
    if (e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey) {
      // User is typing a regular character
      e.preventDefault();

      // Always append to filter text regardless of cursor position
      const newFilterText = filterText + e.key;
      setFilterText(newFilterText);

      // Position cursor at the end
      positionCursorAtEnd();
      return;
    }

    // Handle backspace to delete specific state based on cursor position
    if (e.key === 'Backspace') {
      e.preventDefault();

      if (inputRef.current) {
        // If no selected states, just handle normal backspace for filter text
        if (value.length === 0) {
          if (filterText.length > 0) {
            const newFilterText = filterText.slice(0, -1);
            setFilterText(newFilterText);
          }
          return;
        }

        // Handle backspace when there are selected states
        const cursorPosition = inputRef.current.selectionStart || 0;
        const selectedText = value.join(', ');

        // If cursor is in the selected states area
        if (cursorPosition <= selectedText.length) {
          // Find which state to delete based on cursor position
          // We want to delete the state that the cursor is currently in or after
          let currentPos = 0;
          let stateToDelete = -1;

          for (let i = 0; i < value.length; i++) {
            const stateLength = value[i].length;
            const stateEndPos = currentPos + stateLength;
            const separatorEndPos = stateEndPos + (i < value.length - 1 ? 2 : 0); // +2 for ", "

            // If cursor is within this state or its separator, delete this state
            if (cursorPosition >= currentPos && cursorPosition <= separatorEndPos) {
              stateToDelete = i;
              break;
            }
            currentPos = separatorEndPos;
          }

          if (stateToDelete >= 0) {
            const newValue = value.filter((_, index) => index !== stateToDelete);
            onChange(newValue);

            // Position cursor appropriately after deletion
            setTimeout(() => {
              if (inputRef.current) {
                const newSelectedText = newValue.join(', ');
                // Position cursor at the start of where the deleted state was
                let newCursorPos = 0;
                for (let i = 0; i < stateToDelete && i < newValue.length; i++) {
                  newCursorPos += newValue[i].length + (i > 0 ? 2 : 0);
                }
                newCursorPos = Math.min(newCursorPos, newSelectedText.length);
                inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
                // Scroll to show cursor
                inputRef.current.scrollLeft = inputRef.current.scrollWidth;
              }
            }, 0);
          }
        } else {
          // Cursor is in filter text area, handle normal backspace
          const filterStart = selectedText.length + (selectedText.length > 0 ? 2 : 0);
          if (cursorPosition > filterStart && filterText.length > 0) {
            const newFilterText = filterText.slice(0, -1);
            setFilterText(newFilterText);
          }
        }
      }
      return;
    }

    if (e.key === 'Enter' || e.key === 'Tab') {
      e.preventDefault();
      
      // If there's a hovered state, select it
      if (hoveredIndex >= 0 && hoveredIndex < navigableStates.length) {
        const newStateCode = navigableStates[hoveredIndex].state_code;
        if (value.includes(newStateCode)) {
          // If already selected, unselect it
          const newValue = value.filter(code => code !== newStateCode);
          onChange(newValue);
        } else {
          // If not selected, add it
          const newValue = [...value, newStateCode];
          onChange(newValue);
        }
        setFilterText(''); // Clear filter text after selection
        setHoveredIndex(-1);
        // Keep dropdown open for multiple selections
        // Don't close dropdown or move to next input

        // Position cursor at end to show the new state
        positionCursorAtEnd();
        return;
      }
      
      // Find exact matches from navigable states
      const exactMatches = navigableStates.filter(state =>
        state.state_code.toLowerCase() === filterText.toLowerCase()
      );
      
      // If only one exact match exists, select it
      if (exactMatches.length === 1) {
        const newStateCode = exactMatches[0].state_code;
        if (value.includes(newStateCode)) {
          // If already selected, unselect it
          const newValue = value.filter(code => code !== newStateCode);
          onChange(newValue);
        } else {
          // If not selected, add it
          const newValue = [...value, newStateCode];
          onChange(newValue);
        }
        setFilterText(''); // Clear filter text after selection
        setHoveredIndex(-1);
        // Keep dropdown open for multiple selections
        // Don't close dropdown or move to next input

        // Position cursor at end to show the new state
        positionCursorAtEnd();
      }
    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' || e.key === 'Home' || e.key === 'End') {
      // Allow normal cursor navigation and scroll to keep cursor visible
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.scrollLeft = inputRef.current.scrollWidth;
        }
      }, 0);
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex < navigableStates.length - 1 ? hoveredIndex + 1 : 0;
        setHoveredIndex(newIndex);
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex > 0 ? hoveredIndex - 1 : navigableStates.length - 1;
        setHoveredIndex(newIndex);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setIsFocused(false);
      setFilterText('');
      setHoveredIndex(-1);
      onBlur?.();
    }
  };

  // Use mousedown instead of click to handle selection before blur
  const handleStateMouseDown = (stateCode: string) => {
    // Prevent the input from losing focus when clicking on state
    if (value.includes(stateCode)) {
      // If already selected, unselect it
      const newValue = value.filter(code => code !== stateCode);
      onChange(newValue);
    } else {
      // If not selected, add it
      const newValue = [...value, stateCode];
      onChange(newValue);
    }
    setFilterText(''); // Clear filter text after selection
    setHoveredIndex(-1);
    // Keep dropdown open and maintain focus for multiple selections
    // Don't close dropdown or move to next input
    inputRef.current?.focus(); // Ensure input stays focused

    // Position cursor at end to show the new state
    positionCursorAtEnd();
  };

  const handleStateMouseEnter = (stateCode: string) => {
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    // Only set hover if the state is navigable
    if (navigableIndex >= 0) {
      setHoveredIndex(navigableIndex);
    }
  };

  const handleStateMouseLeave = () => {
    setHoveredIndex(-1);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    setFilterText(''); // Clear input when opening dropdown for searching

    // Always position cursor at the end when opening dropdown
    positionCursorAtEnd();
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Check if the focus is moving to a state button within our dropdown
    const relatedTarget = e.relatedTarget as HTMLElement;
    const isClickingOnState = relatedTarget && relatedTarget.closest('[data-state-button]');

    if (!isClickingOnState) {
      // Only close dropdown if not clicking on a state button
      setTimeout(() => {
        setIsFocused(false);
        setFilterText(''); // Clear filter text when closing
        setHoveredIndex(-1);
        onBlur?.();
      }, 150); // Small delay to allow state click to process
    }
  };

  const handlePopoverClose = () => {
    setIsFocused(false);
    setFilterText('');
    setHoveredIndex(-1);
    onBlur?.();
  };

  // Handle input click to position cursor at end
  const handleInputClick = () => {
    positionCursorAtEnd();
  };


  return (
    <div className={clsx(styles.MultiStateSelectDropdown, isFocused && styles.selectShade)}>
        {isFocused && <>
              <div className={styles.shape1}>
                <Shape1/>
              </div>
              <div className={styles.shape2}>
                <Shape2/>
              </div>
            </>
      
            }

          <Tooltip title={inputValue}
            placement="bottom"
            arrow
            classes={{
              popper: 'multiCertTooltipPopper',
              tooltip: 'multiCertTooltip',
              arrow: 'arrowTooltip'
            }}
          >
            <div className={clsx(styles.inputContainer, styles.multiInputContainer, isFocused && styles.stateWrapper)}>
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                onClick={handleInputClick}
                placeholder={value.length > 0 ? '' : placeholder}
                className={clsx(styles.input, styles.multiInput, error && styles.inputError, isFocused && styles.inputFocused
                )}
              />
              {isFocused && <DropdownIcon />}
            </div>

          </Tooltip>

      
       <Popover
        open={isFocused}
        anchorEl={inputRef.current}
        onClose={handlePopoverClose}
        classes={{
          paper: styles.popperPaperMulti,
        }}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 250,
        }}
        disableAutoFocus
        // disablePortal
        disableEnforceFocus
      >
        <div>
          <div className={clsx(styles.statesGrid,styles.statesGrid1)}>
              {filteredStates.map((state) => (
              <button
                type="button"
                key={state.state_code}
                className={getStateClass(state.state_code)}
                onMouseDown={(e) => {
                  e.preventDefault(); // Prevent input blur
                  handleStateMouseDown(state.state_code);
                }}
                onMouseEnter={() => handleStateMouseEnter(state.state_code)}
                onMouseLeave={handleStateMouseLeave}
                data-state-button="true"
              >
                {state.state_code}
              </button>
            ))}
          </div>
        </div>
      </Popover>
    </div>
  );
};

export default MultiStateSelector; 