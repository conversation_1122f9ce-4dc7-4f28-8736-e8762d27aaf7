import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useEffect } from "react";
import "./Search2.scss";

const Search2 = ()=>{
    const {setShowLoader}:any = useGlobalStore();

    useEffect(()=>{
        setShowLoader(false)
    },[]);

    return (<>
        <div className="search">
            <div className="searchPanel">
                <h1>This is new search container</h1>
                <p>This will have following parts</p>
                <ul>
                    <li>Search Panel.</li>
                    <li>Search Result</li>
                    <li>Listed Products</li>
                    <li>Left control </li>
                    <li>Right Control</li>
                </ul>
            </div>
            <div className="resultContainer">
                <div className="leftPanel>">
                    <p>LeftPanel</p>
                </div>
                <div className="listsContainer">
                    <div className="productsList">
                        <p>Products list</p>
                    </div>
                </div>
                <div className="rightPanel>">
                    <p>RightPanel</p>
                </div>
            </div>
        </div>
    </>)
}

export default Search2;