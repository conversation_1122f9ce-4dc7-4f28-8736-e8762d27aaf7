.topSection {
  width: 100%;
  box-shadow: 0 5.5px 4.4px -3px rgba(0, 0, 0, 0.91);
  position: relative;
  overflow: hidden;
  z-index: 3;
  background-color: #0f0f14;
  height: 8.59vh;
  display: flex;
  align-items: center;
  // -webkit-app-region: drag;


  .headerMain {
    display: flex;
    .logoSectionMain {
      display: flex;
    }
    .logoSection {
      margin-left: 25px;
      margin-right: 58px;
      display: flex;

      svg {
        width: 40px;
        height: 40px;
      }
    }

    .pageName {
      font-family: Syncopate;
      font-size: 25.6px;
      font-weight: bold;
      line-height: 1.5;
      letter-spacing: -1.02px;
      text-align: left;
      color: #fff;
      display: flex;

      .greenDot {
        width: 5px;
        height: 5px;
        background-color: #43f776;
        border-radius: 50%;
        margin: 4.2px 0px 0px 3px;
      }
    }

    .iconMain {
      display: flex;
      column-gap: 7px;
      margin-left: auto;
      margin-top: 4px;

      .iconTopVc {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        flex-grow: 0;
        padding: 0px 5.5px 0px 5.5px;
        object-fit: contain;
        border-radius: 3px;
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.28;
        letter-spacing: -0.28px;
        text-align: center;
        color: rgba(255, 255, 255, 0.2);
        cursor: pointer;

        &:hover {
          color: #0f0f14;
          background-color: #ff6060;
          font-weight: bold;
        }
      }

      .iconTopInviteUser {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-grow: 0;
        border-radius: 3px;
        border: solid 0.5px rgba(255, 255, 255, 0.2);
        cursor: pointer;
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.28;
        letter-spacing: -0.28px;
        text-align: center;
        color: rgba(255, 255, 255, 0.35);
        width: 132px;
        height: 32px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.91);
          color: #0f0f14;
        }
      }

      .iconDiv {
        background-color: rgba(255, 255, 255, 0.04);
        display: flex;
        height: 32px;
        width: 32px;
        cursor: pointer;

        svg {
          height: 32px;
          width: 32px;
          border-radius: 3px;
        }

        &:hover {
          .iconDivImg1 {
            display: none;
          }

          .iconDivImg2 {
            display: block;
          }
        }

        .iconDivImg2 {
          display: none;
        }

      }
    }
    .iconTopSubscribe {
      margin-top: 7px;
    }
  }

}

.mainContent {
  background-color: #191a20;
  height: 100%;
  .innerContent {
    height: 83.76%;
    box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
    border-style: solid;
    border-width: 1px;
    border-image-source: linear-gradient(179deg, #fff -93%, #1a1b21 25%);
    margin-top: 16px;
    border-image-slice: 1;
    display: flex;
    column-gap: 31px;
    .expiredSearchOverlay {
      position: absolute;
      height: 100%;
      width: 100%;
      background: transparent;
      z-index: 11;
      cursor: not-allowed;
    }

    .leftSection {
      display: flex;
      flex-direction: column;
      row-gap: 16px;

      .leftFilterTitle {
        font-family: Syncopate;
        font-size: 10px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0.4px;
        text-align: center;
        color: #fff;
        margin-bottom: 4px;
      }

      .saveDefaultBtn {
        width: 70px;
        height: 41px;
        margin: 10px 2px 0;
        border-radius: 5px;
        font-family: Syncopate;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.29;
        letter-spacing: -0.6px;
        text-align: center;
        background-image: url(../../assets/New-images/ActiveButtonBG.svg);
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        color: rgba(255, 255, 255, 0.8);
        background-color: transparent;
        border-left: 1px solid rgba(255, 255, 255, 0.3);
        border-right: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.1s;
        &:hover{
          background-color: rgba(255, 255, 255, 0.04);
        }
        &[disabled]{
          background-color: rgba(0, 0, 0, 0.25);
          opacity: unset;
          color: rgba(140, 139, 153, 0.5);
          background-image: unset;
          border: unset;
        }
      }

      .filterBtn {
        width: 70px;
        height: 30px;
        flex-grow: 0;
        margin: 0px 0 1px;
        border-radius: 5px;
        background-color: rgba(0, 0, 0, 0.25);
        font-family: Syncopate;
        font-size: 10px;
        line-height: normal;
        letter-spacing: -0.5px;
        color: #8c8b99;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.1s;
        &:hover{
          background-color: rgba(255, 255, 255, 0.04);
        }
        &:focus-visible{
          background-color: rgba(255, 255, 255, 0.04);
        }

        &:last-child {
          margin-bottom: 0px;
        }

        &.activeBtn {
          background-image: url(../../assets/New-images/ActiveButtonBG.svg);
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          color: rgba(255, 255, 255, 0.8);
          background-color: transparent;
        }

      }

      .orderFilter {
        width: 90px;
        padding: 6px 8px 7px;
        border-radius: 10px;
        background-image: linear-gradient(89deg, #0f0f14 -10%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 122%);
      }

      .pricingUnit {
        width: 90px;
        padding: 6.7px 10px 8px;
        border-radius: 10px;
        background-image: linear-gradient(to right, #0f0f14 -11%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
      }

      .domesticReq {
        width: 90px;
        height: 102px;
        flex-grow: 0;
        padding: 8px 10px;
        border-radius: 10px;
        background-image: linear-gradient(to right, #0f0f14 -11%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
      }
    }

    .middleSection {
      width: 100%;
      height: 100%;
      // border-radius: 14px;
      // background-image: url(../../assets/New-images/PriceSearchActiveBG.svg); 
      // background: url(../../assets/New-images/PriceListBG.svg) no-repeat;
      background-repeat: no-repeat;
      background-size: 514px 542px;
      overflow: hidden;
      position: relative;

      .searchProductDescMain {
        width: 100%;
        box-shadow: 0 4px 9.4px 5px rgba(0, 0, 0, 0.72);
        overflow: auto;
        // border-radius: 14px 14px 0px 0px;
        position: absolute;
        inset: 0;
        // background: url(../../assets/New-images/PriceSearchActiveBG.svg) #0f0f14;
        // background: url(../../assets/New-images/PriceListBG.svg) #1b1b21 no-repeat;
        background-repeat: no-repeat;
        background-size: 514px 542px;
        z-index: 999;
        transition: max-height 0.85s ease-in-out;
        max-height: 0px;
        
        &::-webkit-scrollbar {
          width: 8px;
        }
     
        &::-webkit-scrollbar-track {
          background: transparent;
        }
     
        &::-webkit-scrollbar-thumb {
          border-radius: 8px;
          background-color: #8b91a6;
        }
      }

      .padding0{
          // background: url(../../assets/New-images/PriceListBG1.svg) no-repeat;
          background-size: inherit;
        .searchResult{
          padding-bottom: 0px !important;
        
        }
      }


      .NoResultsToDisplay {
        position: relative;
        padding: 90px 108.5px 230.2px 107px;
        display: flex;
        align-items: center;
        justify-content: center;
        .NoResultsToDisplayText {
          position: absolute;
          text-align: center;
          opacity: 0.5;
          font-family: Syncopate;
          font-size: 14.5px;
          font-weight: bold;
          line-height: 1.4;
          letter-spacing: 0.98px;
          color: #fff;
          top: 385px;

          .marginBottom10 {
            margin-bottom: 10px;
          }
        }

      }

      .searchProductDescMain {
        .searchResult{
          padding: 8px;
          background-color: #191a20;
        }
        .searchProductDescription {
          &.highlightSearchProductDescription {
            .searchProductDesc {
              background-color: rgba(255, 255, 255, 0.07);
            }
          }

          .searchProductDesc {
            padding: 16px 20px 16px 20px;
            font-family: Inter;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.4;
            letter-spacing: 0.98px;
            text-align: left;
            color: #fff;
            border-radius: 10px;
            transition: all 0.1s;
            position: relative;
            text-transform: uppercase;
            cursor: pointer;
            &:hover{
              background-color: rgba(255, 255, 255, 0.07); 
              border-radius: 16px;
            }

            &:active {
              background-image: linear-gradient(342deg, #eaecf0 -10%, #9b9eac 100%);
              color: #000;
            }

            .firstLine {
              font-weight: 500;
            }
            .firstLineSearch{
              letter-spacing: 0;
            }
          }

        }

        .NoResultsToDisplayText1 {
          opacity: 0.5;
          font-family: Syncopate;
          font-size: 14px;
          font-weight: bold;
          line-height: 1.4;
          letter-spacing: 0.98px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          height:300px;

          .marginBottom10 {
            margin-bottom: 10px;
          }
        }
       
      }

      .selectedProductWrapper{
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .selectedProductDescriptionMain {
        flex: 1;
        padding: 8px;
        overflow: auto;
        height: 100%;

        &.searchResult {
          height: 100%;
          background-color: rgba(255, 255, 255, 0.02);
          filter: blur(4px);
          pointer-events: none;
        }

        .selectedSearchProductTop {
          position: relative;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0px;
          }

          .infoPopup {
             width: 379px;
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            border-radius: 8px;
            padding: 9px 10px 7px 11px;
            border-radius: 16.5px;
            background-color: #c3c4ca;
  
            .infoPopupContent {
              display: flex;
              flex-direction: column;

              .infoPopupText {
                  h4 {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.32;
                    letter-spacing: 1.12px;
                    text-align: left;
                    color: #0f0f14;
                    margin-bottom: 4px;
                  }
              
                }

                  .proTipGrid {
                    display: flex;
                    align-items: center;
                    column-gap: 16px;
  
                    p {
                      font-family: Inter;
                      font-size: 14px;
                      font-weight: 300;
                      font-stretch: normal;
                      font-style: normal;
                      line-height: 1.4;
                      letter-spacing: -0.28px;
                      text-align: left;
                      color: #0f0f14;
                    }
  
  
                    .infoPopupButton {
                      border: none;
                      padding: 3px 0px;
                      border-radius: 500px;
                      background-color: #0f0f14;
                      font-family: Inter;
                      font-size: 14px;
                      font-weight: bold;
                      font-stretch: normal;
                      font-style: normal;
                      line-height: 1.4;
                      letter-spacing: normal;
                      text-align: center;
                      color: #32ff6c;
                      cursor: pointer;
                      transition: all 0.2s ease;
                      align-self: flex-end;
                      min-width: 80px;
                      text-transform: uppercase;
                      letter-spacing: 0.5px;
  
                      &:hover {
                        background: #32ff6c;
                        transform: translateY(-1px);
                        color: #000;
                      }
  
                      &:active {
                        transform: translateY(0);
                      }
                    }
                  }

            }
          }

          .selectAllLines {
            height: 23.4px;
            padding: 4.7px 12px 4.7px 12px;
            border-radius: 100px;
            background-color: #0f0f14;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 1.12px;
            text-align: left;
            color: #fff;
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 99;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }


        &::-webkit-scrollbar {
          display: none;
          /* Hides scrollbar in Chrome/Safari */
        }

        .productDescriptionMain {
          cursor: pointer;
          border-radius: 10px;
          transition: all 0.1s;
          display: flex;
          align-items: stretch;
          justify-content: space-between;
          position: relative;
          overflow: hidden;
    
          .searchProductDesc {
            padding: 16px 8px 16px 20px;
            font-family: Inter;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.4;
            letter-spacing: 0.98px;
            text-align: left;
            color: #fff;
            border-radius: 16px;
            transition: all 0.1s;
            text-transform: uppercase;
            cursor: pointer;

            .firstLine {
              font-weight: 500;
            }
            .firstLineSearch{
              letter-spacing: 0;
            }
          }

          &.clickToShare {
            background-color: #434449;
            .overlayForAnimation{
              background-color:  #302e34
            }
          }

          &:hover {
            background-color: #303036;
            .overlayForAnimation{
              background-color:  #302e34
            }
          }

        }
      }

      .priceRating {
        padding: 16px 16px 16px 10px;
        flex: 0 170px;
        position: relative;
        margin-left: auto;

        .priceMain {
          display: flex;
          justify-content: flex-end;
          width: 100%;
          height: 100%;
          position: relative;
        }

        .priceSelectedWrapper{
          display: flex;
          flex: 0 120px;
          margin-left: auto;
          justify-content: flex-end;
        }

        .priceSelected {
          font-size: 24px;
          font-weight: 300;
          font-stretch: normal;
          line-height: 1;
          text-align: right;
          color: #fff;
          display: flex;
          flex-direction: column;
          position: relative;

          .displayRow{
            display: flex;
            align-items: baseline;
          }

          .doller {
            margin: 0 2px 8px 0;
            font-family: Inter;
            font-size: 16px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            position: absolute;
            top: 2px;
            left: -15px;
          }

          .priceUnit {
            font-family: Inter;
            font-size: 10px;
            font-weight: 300;
            line-height: 1.4;
            letter-spacing: 0.4px;
            text-align: right;
            color: rgba(255, 255, 255, 0.92);
            margin-top: 4px;

            .unitLbl {
              color: rgba(255, 255, 255, 0.56);
            }
          }

        }

      }


    }


    .rightSection {
      display: flex;
      flex-direction: column;

      .createPOBTn{
        position: relative;
        width: 90px;
        height: 56px;
        padding: 15px 5px 11px;
        border-radius: 10px;
        background-image: linear-gradient(to right, #0f0f14 -12%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
        margin-bottom: 16px;
        overflow: hidden;
        border: solid 0.5px rgba(0, 0, 0, 0.33);
        transition: all 0.2s;
        &.viewHistoryBtn{
          padding: 13px 0px;
        }
        .createPoBtnInnerSection{
           display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          div{
            font-family: Syncopate;
            font-size: 14.5px;
            font-weight: bold;
            line-height: 1.1;
            letter-spacing: -0.56px;
            text-align: center;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            background-image: linear-gradient(205deg, #fff -14%, #999 47%);
          }
        }
        &:hover{
          .createPoBtnInnerSection{
            div{
              background-image: linear-gradient(205deg, #fec44f -14%, #fec44f 47%);
            }
          }
        }
        &:focus-visible{
          .createPoBtnInnerSection{
            div{
              background-image: linear-gradient(205deg, #fec44f -14%, #fec44f 47%);
            }
          }
        }
      }

      .actionMenu {
        width: 90px;
        height: 270px;
        flex-grow: 0;
        padding: 10px 9px;
        border-radius: 12px;
        background: url(../../assets/New-images/ACTION_MENU_BG.svg) no-repeat;
        // background-image: linear-gradient(89deg, #0f0f14 -7%, rgba(57, 62, 71, 0.71) 30%, rgba(57, 62, 71, 0.68) 65%, #0f0f14 115%);
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        position: relative;

        &.overlay {
          filter: blur(5px);
          pointer-events: none;
        }

        .actionMenuTitle {
          opacity: 0.7;
          font-family: Syncopate;
          font-size: 10px;
          line-height: normal;
          letter-spacing: 0.4px;
          text-align: center;
          color: #fff;
        }

        .actionBtn {
          width: 72px;
          height: 36px;
          flex-grow: 0;
          padding: 5px 2px;
          opacity: 0.7;
          border-radius: 5px;
          background-color: rgba(0, 0, 0, 0.25);
          transition: all 0.1s;
          span{
            display: flex;
            opacity: 0.7;
            font-family: Syncopate;
            font-size: 10px;
            font-weight: normal;
            line-height: 1.25;
            letter-spacing: -0.5px;
            text-align: center;
            color: #fff;
          }
          &:hover{
            background-color: rgba(255, 255, 255, 0.04);
          }
        }
        .activeActionBtn{
          background-color: rgba(255, 255, 255, 0.04);
        }
      }

      .clearList {
        position: relative;
        border-radius: 10px;
        margin-top: auto;
        background-image: linear-gradient(to right, #0f0f14 -12%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
        button{
          width: 90px;
          height: 50px;
          flex-grow: 0;
          padding: 9px 24px;
          border-radius: 10px;
          background-image: url(../../assets/New-images/ActiveButtonBG.svg);
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          color: rgba(255, 255, 255, 0.8);
          background-color: transparent;
          font-family: Syncopate;
          font-size: 10px;
          font-weight: normal;
          line-height: 1.61;
          letter-spacing: 0.4px;
          text-align: center;
          transition: color 0.2s ease-in-out;
          overflow: hidden;
          border-left: 1px solid rgba(255, 255, 255, 0.5);
          border-right: 1px solid rgba(255, 255, 255, 0.5);
        }
  
        button[disabled] {
          background-image: linear-gradient(to right, #0f0f14 -12%, rgba(57, 62, 71, 0.67) 32%, rgba(57, 62, 71, 0.72) 67%, #0f0f14 123%);
          color: rgba(140, 139, 153, 0.53);
          border: unset;
        }

        }
      
    
    }

  }

}


.FeedbackHoverArrowMain {
  .submitFeedbackBtn{
    width: 75px;
    height: 37px;
    flex-grow: 0;
    padding: 3.5px 0px 2.5px 0px;
    border-radius: 5px;
    background-image: url(../../assets/New-images/ActiveButtonBG.svg);
    background-repeat: no-repeat;
    background-size: cover;
    color: rgba(255, 255, 255, 0.8);
    background-color: transparent;
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    line-height: 1.53;
    letter-spacing: normal;
    text-align: center;
    color: #c4c4c4;
    position: absolute;
    left: -88px;
    top: 28px;
    background-position: right;
    border-left: 0.1px solid rgba(255, 255, 255, 0.3);
    &:focus{
      outline: 0.5px solid #fff;
    }
  }
  .btnCancel{
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    line-height: 1.61;
    letter-spacing: 0.4px;
    text-align: center;
    color: #8c8b99;
    position: absolute;
    bottom: -85px;
    right: 0px;
    cursor: pointer;
    &:hover{
      color: #1fbbff;
    }
    &:focus{
      color: #c4c4c4;
    }
  }
}

.FeedbackHoverArrow {
  display: inline-flex;
  flex-direction: column;
  margin-right: auto;
  transition: all 0.1s;
  position: absolute;
  left: -115px;
  top: 18px;

  svg {
    margin-left: auto;
  }

  span {
    transform: rotate(-8deg);
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    line-height: 1.2;
    letter-spacing: normal;
    text-align: center;
    color: #32ff6c;
    text-transform: uppercase;
    margin-top: 5px;
  }
}

.feedbackPosition{
  position: absolute;
  bottom: 0.55px;
  right: -3.3px;
  width: 113px;
  height: 16.6px;
  border-radius: 100px;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;

  .feedbackSaved{
    padding-left: 8px;
    font-family: Inter;
    font-size: 10px;
    font-weight: 300;
    line-height: 1;
    letter-spacing: 0.7px;
    text-align: left;
    color: #10bbff;

  }
}
.editFeedback{
  position: relative;
  .feedbackDetail{
    padding-right: 8px;
  }
}

.feedBackArrow {
  position: absolute;
  bottom: 3px;
  right: 0px;
  display: flex;
}


.feedbackContent.feedbackContent {
  width: auto;
  height: 16.6px;
  padding: 2px 24px 2px 5.5px;
  border-radius: 100px;
  background-color: rgba(255, 255, 255, 0.1);
  font-family: Inter;
  font-size: 10px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: 0.7px;
  text-align: right;
  color: #fff;
  position: relative;
  left: 28.4px;
  top: -2.5px;
  margin: 0px;

  &.focusSingleProduct{
    background-color:  rgba(25, 25, 29, 0.7);
    .overlayForAnimation{
     background-color: rgba(255, 255, 255, 0.5);
    }
  }

  .feedbackInnerContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;

    .editBtn {
      cursor: pointer;
      font-family: Inter;
      font-size: 10px;
      font-weight: 300;
      line-height: 1;
      letter-spacing: 0.7px;
      text-align: right;
      color: #10bbff;
    }
  }
}

.feedbackFormWrapper{
  position: relative;
  width: 100%;
}

.feedBackForm {
  position: absolute;
  top: -2px;
  right: -2px;
  z-index: 99;
  width: 136px;
  height: 68px;
  margin: 0 0 0 10px;
  padding: 2.4px 4px 4px;
  border-radius: 5px;
  box-shadow: inset -2px 4px 4px 0 rgba(0, 0, 0, 0.35), inset 2px -1px 3.9px 0 rgba(0, 0, 0, 0.8);
  background-origin: border-box;
  background-clip: content-box, border-box;
  border: 1.5px solid #6c6c6c;
  background: #cbcbcd;
 .feedbackTopSection{
  display: flex;
  justify-content: space-between;
  padding: 0px 4px 0px 1px;
  position: relative;
    .dollerTxt {
      font-family: Inter;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: rgba(28, 28, 34, 0.5);
    }
    input{
      width: 60px;
      height: 24px;
      background-color: transparent;
      border: 0px;
      box-shadow: none;
      font-family: Inter;
      font-size: 24px;
      font-weight: 300;
      font-stretch: normal;
      line-height: 1;
      text-align: right;
      color: #19191d;
      flex: 0 76%;
      &::placeholder{
        color: rgba(25, 25, 29, 0.2);
      }
      &:focus{
        outline: none;
      }
    }

    .zipLabel {
      position: absolute;
      bottom: -3px;
      left: 0;
      .zipLBL{
        position: absolute;
        left: 0px;
        bottom: 0px;
        display: inline-block;
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #cacacc;
        display: flex;
        align-items: center;
        z-index: 1;
      }

      .bgFeedback {
        display: flex;

      }
      
    }
    

 }
 .feedBackFormMidSection{
  width: 100%;
  height: 16px;
  flex-grow: 0;
  margin: 1.5px 0 3px;
  border-top: solid 1px #19191d;
  border-bottom: solid 1px #19191d;
  background-color: #cbcbcd;
  display: flex;
  align-items: center;
  .inputBox {
    display: flex;
    flex: 1;
    padding: 0px 3px;
    input{
      background-color: transparent;
      border: 0px;
      box-shadow: none;
      width: 100%;
      height: 14px;
      flex-grow: 0;
      font-size: 14px;
      font-weight: 300;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
       color: #19191d;
      &::placeholder{
        color: rgba(25, 25, 29, 0.2);
      }
      &:focus{
        outline: none;
      }
  }
  }
  .selectBox{
    flex: 1;
    height: 16px;
    border-top: solid 1px #19191d;
    border-bottom: solid 1px #19191d;
    background-color: #19191d;
  }
  
 }
 .feedbackbottomSection{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  input{
    background-color: transparent;
    border: 0px;
    box-shadow: none;
    width: 100%;
    height: 14px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: right;
    color: #000;
    padding: 0px 2px 0px 4.1px;
    &::placeholder{
      color: rgba(25, 25, 29, 0.23);
    }
    &:focus{
      outline: none;
    }
  }
  span {
    margin: 0px 0 0 0px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #000;
  }
 }
}

.usaFlag {
  position: absolute;
  top: 18px;
  left: 3px;
  display: flex;
}

.arrowWrapper{
  display: flex;
  align-items: center;
  position: absolute;
  right: 4px;
  flex-direction: column;
  z-index: 1;

}

.overlayForAnimation{
  position: absolute;
  inset: -3px -8px;
  border-radius: 10px 0px 0px 10px;
  background-color: #1b1b21;
}

.animationShowEaseIn{
  animation: increaseWidth 0.85s linear forwards;
  @keyframes increaseWidth {
    0% {
      right: -8px;
      opacity: 1;
    }
    100% {
      right: 230px;
      opacity: 0;
    }
  }
}

.animationHideEaseOut2 {
  animation: decreaseWidth 1s linear forwards;

  @keyframes decreaseWidth {
    0% {
      right: 230px;
      opacity: 0;
    }
    100% {
      right: -8px;
      opacity: 1;
    }
  }
}

.animationHideEaseOut{
  animation: reverseGrow 1s linear forwards;
  @keyframes reverseGrow {
    0%{
      right: 140px;
    }
    30% {
      right: 140px;
    }
    100% {
      right: -8px;
    }
  }
}


.clickToShare{
  .feedBackArrow{
    filter: invert(1);
  }
}

.editArrowStyle {
  display: block;
  margin-top: 80px;
}

.priceClickOverlay{
  position: absolute;
  top: -2px;
  bottom: 0px;
  right: -2px;
  z-index: 999;
  width: 136px;
  height: 68px;
}


.blurEffect{
  .overlayForAnimation{
    background-color:  #302e34
  }
}

.overlayOnBlur{
  .overlayForAnimation{
    background-color:  rgb(233, 236, 240) !important
  }
}
.savePricingInputContainer {
  width: 272px;
  height: 36px;
  padding: 8px;
  border-radius: 5px;
  box-shadow: -4px 4px 4px 0 rgba(0, 0, 0, 0.46);
  background-color: #16171c;
  position: absolute;
  left: -200px;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  opacity: 1;
  z-index: 99;
  input {
    width: 191px;
    height: 20px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
    padding: 0 8px 0 6px;
    border-radius: 3px;
    background-color: #d9d9d9;
    font-family: Inter;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: -0.5px;
    text-align: left;
    color: #1a1c21;
    background-color: #d9d9d9;
    opacity: 1;
    caret-color: #000;
    &::placeholder{
      color: rgba(35, 38, 44, 0.46);
      font-family: Syncopate;
    }
    &:focus{
      outline: none;
      color: #1a1c21;
    }
    
    
  }
  .actionInputBtn {
    width: 61px;
    height: 20px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
    padding: 4px 15px 0;
    border-radius: 3px;
    background-color: #d9d9d9;
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: -0.5px;
    text-align: center;
    color: #1a1c21;
    opacity: 1;
    &:hover{
      background-color: #c3c4ca;
    }
    &:focus-visible{
      outline: none;
      background-color: #c3c4ca;
    }
  }
  
}
.MarginTop16 {
  margin-top: 16px;
}
.positionRelative{
  position: relative;
  opacity: 1 !important;
}

// Hover button styles
.hoverButtonContainer {
  position: relative;
  padding: 8px 11px;
  border-radius: 500px;
  background-color: #27272d;
  min-width: 80px;
  // opacity: 0.5;
  
  .buttonWrapper {
    
    .defaultButton {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 0.91;
      letter-spacing: 1.4px;
      text-align: left;
      color: #9b9eac;
      opacity: 0.5;
      gap: 5px;
      span {
        padding-top: 3px;
      }
      &:hover {
        color: #ff4848;
        opacity: 1;
      }
    }
    
    .hoverButtons {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      z-index: 10;
      border-radius: 500px;
      background-color: #27272d;
      min-width: 113px;
      opacity: 0.5;
      overflow: hidden;
      
      .deleteSelectedBtn {
        min-width: 56px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 0.91;
        letter-spacing: 1.4px;
        text-align: left;
        color: #9b9eac;
        padding: 0px 6px;
        &:hover {
          color: #ff4848;
          opacity: 1;
          svg{
            path{
              fill:#ff4848
            }
          }
        }
      }
      
      .allBtn {
        display: none;
        justify-content: space-between;
        align-items: center;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 0.91;
        letter-spacing: 1.4px;
        text-align: right;
        color: #9b9eac;
        width: 56px;
        height: 36px;
        flex-grow: 0;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.04);
        border-left: 2px solid #0f0f14;
        &:hover {
          color: #ff4848;
          opacity: 1;
        }
      }
    }
    
    &.enableHover:hover {
      .defaultButton {
        opacity: 0;
        visibility: hidden;
      }
      
      
      .hoverButtons {
        opacity: 1;
        visibility: visible;
        .allBtn{
          display: flex;
        }
      }
    }
  }
}


.selectedProductHeaderContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #191a20;
  .selectedProductHeaderButtonsContainer {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .selectedProductHeaderButton.selectedProductHeaderButton {
    padding: 8px;
    opacity: 0.5;
    border-radius: 500px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #9b9eac;
    min-width: 75px;
    align-items: center;
    justify-content: center;
    display: flex;
    cursor: not-allowed;
  }
  .enableHover {
    .selectedProductHeaderButton {
      opacity: 1;
      cursor: pointer;
      &:hover {
        color: #ffe352;
      }
    }
  }
}

// Export dropdown styles
.exportContainer.exportContainer {
  position: relative;
  border-radius: 500px;

  .exportButton {
    height: 34px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0px 0px 0px 12px;
    background-color: rgba(255, 255, 255, 0.04);
    color: #9b9eac;
    border: none;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    cursor: pointer;
    transition: all 0.2s ease;
    &[disabled]{
       opacity: 0.5;
        background-color: rgba(255, 255, 255, 0.04);
        color: #9b9eac;
       cursor: not-allowed;
        &:hover {
       background-color: rgba(255, 255, 255, 0.04);

    }
    }

    .exportArrow {
      height: 100%;
      min-width: 30px;
      transition: transform 0.2s ease;
      border-radius: 0% 50% 50% 0%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-left: solid 1px rgba(255, 255, 255, 0.1);

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  .exportDropdown.exportDropdown {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    border-radius: 6px;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #9c9da5;
    z-index: 1000;
    overflow: hidden;
    padding: 4px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 123px;

    .exportOption {
      width: 100%;
      padding: 6px 7px 6px 5px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      color: #191a20;
      text-align: left;
      margin-bottom: 2px;

      &:hover {
        border-radius: 6px;
        background-color: #e0e0e0;
        font-weight: bold;
      }
    }
  }
}
.itemCountPop {
  border-radius: 5000px;
    background-color: rgba(255, 255, 255, 0.1);
    min-width: 158px;
    height: 37px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 1.12px;
    text-align: center;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: anchor-center;
    position: absolute;
    bottom: 30px;
    left: 40%;
    right: 40%;
}