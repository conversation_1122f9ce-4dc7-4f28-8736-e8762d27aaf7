import React, { useEffect } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import styles from './SubscribeUserTable.module.scss';
import { 
  subscribeUserTableSchema, 
  type SubscribeUserTableFormData,
  type User 
} from './SubscribeUserTable.schema';
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore';

interface SubscribeUserTableProps {
  initialUsers?: User[];
  onUserUpdate?: (users: User[]) => void;
  onSave?: (data: SubscribeUserTableFormData) => void;
}

const SubscribeUserTableClean: React.FC<SubscribeUserTableProps> = ({
  onUserUpdate,
  onSave
}) => {
  const { userList, uploadUserList, setUploadUserList } = useSubscriptionStore();



  const {
    control,
    handleSubmit,
    formState: { errors, isDirty, isSubmitted },
    watch,
    setValue,
    clearErrors
  } = useForm<SubscribeUserTableFormData>({
    resolver: yupResolver(subscribeUserTableSchema),
    mode: 'onSubmit', // Only validate on submit
    reValidateMode: 'onSubmit', // Keep validation only on submit even after first submit
    defaultValues: {
      users: []
    }
  });

  // Set initial user data from store
  useEffect(() => {
    if (userList?.length > 0) {
      const transformedUsers = userList.map((user: any) => ({
        ...user,
        isExisting: true
      }));

      // Always add placeholder row at the end
      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false
      };

      setValue('users', [...transformedUsers, placeholderUser]);
    } else {
      // If no existing users, just show placeholder row
      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false
      };

      setValue('users', [placeholderUser]);
    }
  }, [userList, setValue]);

  // Handle uploaded users from store
  useEffect(() => {
    if (uploadUserList?.length > 0) {
      const transformedUploadedUsers = uploadUserList.map((user: any) => ({
        ...user,
        isExisting: true
      }));

      // Keep the placeholder row at the end
      const placeholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false
      };

      setValue('users', [...transformedUploadedUsers, placeholderUser], { shouldDirty: true });
      setUploadUserList(null);
    }
  }, [uploadUserList, setValue, setUploadUserList]);

  const { fields, update, append } = useFieldArray({
    control,
    name: 'users'
  });

  const watchedUsers = watch('users') || [];

  // Handle license changes
  const handleLicenseChange = (index: number, newLicense: 'Assigned' | 'Unassigned') => {
    if (watchedUsers[index]) {
      const updatedUser = { ...watchedUsers[index], license: newLicense };
      update(index, updatedUser);
      onUserUpdate?.(watchedUsers as User[]);
    }
  };

  // Handle action changes
  const handleActionChange = (index: number, action: string) => {
    if (!watchedUsers[index]) return;
    
    const updatedUser = { ...watchedUsers[index], action, status: action };
    update(index, updatedUser);
    onUserUpdate?.(watchedUsers as User[]);
  };

  // Handle new user input (for placeholder row)
  const handleNewUserInput = (index: number, field: 'user_name' | 'email_id', value: string) => {
    const currentUser = watchedUsers[index];
    if (!currentUser || currentUser.isExisting) return;

    const updatedUser = { ...currentUser, [field]: value };
    update(index, updatedUser);

    // If both fields are filled, add a new placeholder row (only if this is the last row)
    const isLastRow = index === watchedUsers.length - 1;
    if (isLastRow && updatedUser.user_name?.trim() && updatedUser.email_id?.trim()) {
      const newPlaceholderUser: User = {
        id: `new-${Date.now()}`,
        user_name: '',
        email_id: '',
        license: 'Unassigned',
        action: '',
        status: 'Active',
        isExisting: false
      };
      append(newPlaceholderUser);
    }
  };

  // Utility functions
  const isPlaceholderRow = (user: any): boolean => {
    return !user.isExisting && !user.user_name?.trim() && !user.email_id?.trim();
  };

  const isFieldDisabled = (user: any, field: 'user_name' | 'email_id' | 'license' | 'action'): boolean => {
    if (user.isExisting) return false;

    // For new users, username and email are always enabled for tab navigation
    if (field === 'user_name' || field === 'email_id') return false;

    // For new users, disable action buttons and license until username is filled
    if (field === 'action' || field === 'license') {
      return !user.user_name?.trim();
    }

    return false;
  };

  // Form submission with payload filtering
  const onSubmit = (data: SubscribeUserTableFormData) => {
    // Filter out empty new users from payload
    const filteredUsers = (data.users || []).filter(user => {
      if (user.isExisting) return true; // Always include existing users

      // For new users, include only if both username and email are filled
      return user.user_name?.trim() && user.email_id?.trim();
    });

    const payload = { users: filteredUsers };
    console.log('Form submitted with filtered payload:', payload);
    onSave?.(payload);
  };

  // Render editable cell for existing users
  const renderEditableCell = (index: number, field: 'user_name' | 'email_id') => {
    const fieldName = `users.${index}.${field}` as const;
    const error = errors.users?.[index]?.[field];
    const user = watchedUsers[index];

    // Only show errors after form submission attempt
    const shouldShowError = error && isSubmitted;

    return (
      <div className={styles.cellContainer}>
        <Controller
          name={fieldName}
          control={control}
          render={({ field: controllerField }) => (
            <input
              type="text"
              {...controllerField}
              onBlur={(e) => {
                controllerField.onBlur();
                // Only handle new user logic on blur to avoid focus issues
                if (!user?.isExisting) {
                  handleNewUserInput(index, field, e.target.value);
                }
              }}
              disabled={isFieldDisabled(user, field)}
              className={`${styles.editInput} ${shouldShowError ? styles.error : ''} ${
                isPlaceholderRow(user) ? styles.placeholder : ''
              } ${isFieldDisabled(user, field) ? styles.disabled : ''}`}
              placeholder={field === 'user_name' ? 'Enter user name' : 'Enter email'}
            />
          )}
        />
        {shouldShowError && (
          <span className={styles.errorMessage}>
            {error.message}
          </span>
        )}
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={styles.formContainer}>
      <div className={styles.tableContainer}>
        <table className={styles.userTable}>
          <thead>
            <tr className={styles.tableHeader}>
              <th>USER NAME</th>
              <th>Email</th>
              <th>LICENSE</th>
              <th>ACTION</th>
              <th>STATUS</th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, index) => {
              const user = watchedUsers[index];
              const isPlaceholder = isPlaceholderRow(user);
              
              return (
                <tr 
                  key={field.id} 
                  className={`${styles.tableRow} ${isPlaceholder ? styles.placeholderRow : ''}`}
                >
                  <td className={styles.tableCell}>
                    {renderEditableCell(index, 'user_name')}
                  </td>
                  <td className={styles.tableCell}>
                    {renderEditableCell(index, 'email_id')}
                  </td>
                  <td className={styles.tableCell}>
                    <select
                      value={user?.license || 'Unassigned'}
                      onChange={(e) => handleLicenseChange(index, e.target.value as 'Assigned' | 'Unassigned')}
                      disabled={isFieldDisabled(user, 'license')}
                      className={`${styles.dropdown} ${
                        user?.license === 'Assigned' ? styles.assigned : styles.unassigned
                      } ${isFieldDisabled(user, 'license') ? styles.disabled : ''}`}
                    >
                      <option value="Assigned">Assigned</option>
                      <option value="Unassigned">Unassigned</option>
                    </select>
                  </td>
                  <td className={styles.tableCell}>
                    <select
                      value={user?.action || 'Select Action'}
                      onChange={(e) => {
                        if (e.target.value !== 'Select Action') {
                          handleActionChange(index, e.target.value);
                        }
                      }}
                      disabled={isFieldDisabled(user, 'action')}
                      className={`${styles.dropdown} ${isFieldDisabled(user, 'action') ? styles.disabled : ''}`}
                    >
                      <option value="Select Action">Select Action</option>
                      <option value="Invite">Invite</option>
                      <option value="Remove">Remove</option>
                    </select>
                  </td>
                  <td className={styles.tableCell}>
                    <span className={styles.status}>
                      {user?.status || 'Active'}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      
      <div className={styles.saveButtonContainer}>
        <button
          type="submit"
          disabled={!isDirty}
          className={`${styles.saveButton} ${!isDirty ? styles.disabled : ''}`}
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};

export default SubscribeUserTableClean;
