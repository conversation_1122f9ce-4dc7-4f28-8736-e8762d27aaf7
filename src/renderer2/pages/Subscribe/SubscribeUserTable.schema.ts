import * as yup from 'yup';

export interface User {
  id: string;
  user_name: string;
  email_id: string;
  license: 'Assigned' | 'Unassigned';
  action: string;
  status: string;
}

// Individual user validation schema
export const userSchema = yup.object({
  id: yup.string(),
  user_name: yup
    .string()
    .required('User name is required')
    .min(2, 'User name must be at least 2 characters')
    .max(50, 'User name must be less than 50 characters'),
  email_id: yup
    .string()
    .required('Email is required')
    .email('Please enter a valid email address'),
  license: yup
    .string(),
  action: yup.string(),
  status: yup.string()
});

// Form validation schema
export const subscribeUserTableSchema = yup.object({
  users: yup.array().of(userSchema).min(1, 'At least one user is required')
});

export type SubscribeUserTableFormData = yup.InferType<typeof subscribeUserTableSchema>; 