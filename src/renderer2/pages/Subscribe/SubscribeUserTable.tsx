import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import styles from './SubscribeUserTable.module.scss';
import { 
  subscribeUserTableSchema, 
  type SubscribeUserTableFormData,
  type User 
} from './SubscribeUserTable.schema';
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore';

interface SubscribeUserTableProps {
  initialUsers?: User[];
  onUserUpdate?: (users: User[]) => void;
  onSave?: (data: SubscribeUserTableFormData) => void;
}

const SubscribeUserTable: React.FC<SubscribeUserTableProps> = ({
  initialUsers,
  onUserUpdate,
  onSave
}) => {
  const {userList , uploadUserList , setUploadUserList} = useSubscriptionStore();
  const totalNumberOfUsers = 10;

  // Separate placeholder state
  const [placeholderRow, setPlaceholderRow] = useState({
    user_name: '',
    email_id: ''
  });

  // Placeholder error state
  const [placeholderErrors, setPlaceholderErrors] = useState({
    email_id: false
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    watch,
    setValue,
    trigger
  } = useForm<SubscribeUserTableFormData>({
    resolver: yupResolver(subscribeUserTableSchema),
    mode: 'onBlur',
    defaultValues: {
      users: userList
    }
  });

  useEffect(() => {
    if(uploadUserList?.length > 0){
      setValue('users', uploadUserList , {shouldDirty: true});
      setUploadUserList(null);
    }
  }, [uploadUserList]);

  console.log("isValid", isValid);
  console.log("isDirty", isDirty);

  const { fields, remove, update, append } = useFieldArray({
    control,
    name: 'users'
  });

  const watchedUsers = watch('users') || [];

  const handleLicenseChange = (index: number, newLicense: 'Assigned' | 'Unassigned') => {
    if (watchedUsers[index]) {
      update(index, { ...watchedUsers[index], license: newLicense });
      trigger(`users.${index}.license`);
      onUserUpdate?.(watchedUsers);
    }
  };

  const handleActionChange = (index: number, action: string) => {
    if (!watchedUsers[index]) return;
    
    if (action === 'Remove') {
      const updatedUser = { ...watchedUsers[index], status: 'Remove' };
      update(index, updatedUser);
      trigger(`users.${index}.status`);
      onUserUpdate?.(watchedUsers);
    } else if (action === 'Invite') {
      const updatedUser = { ...watchedUsers[index], status: 'Invite' };
      update(index, updatedUser);
      trigger(`users.${index}.status`);
      onUserUpdate?.(watchedUsers);
    }
  };

  // Handle placeholder row changes
  const handlePlaceholderChange = (field: 'user_name' | 'email_id', value: string) => {
    const newPlaceholder = { ...placeholderRow, [field]: value };
    setPlaceholderRow(newPlaceholder);
    
    // Clear error if both fields are empty
    if (!newPlaceholder.user_name.trim() && !newPlaceholder.email_id.trim()) {
      setPlaceholderErrors({ email_id: false });
    }
  };

  // Handle placeholder field blur with validation
  const handlePlaceholderFieldBlur = (field: 'user_name' | 'email_id') => {
    if (field === 'email_id' && placeholderRow.email_id.trim()) {
      const isValid = isValidEmail(placeholderRow.email_id.trim());
      setPlaceholderErrors(prev => ({ ...prev, email_id: !isValid }));
    }
    
    // Try to add to form if both fields are filled and email is valid
    if (placeholderRow.user_name.trim() && placeholderRow.email_id.trim()) {
      const isEmailValid = isValidEmail(placeholderRow.email_id.trim());
      if (isEmailValid) {
        // Add to form
        const newUser: User = {
          id: Date.now().toString(),
          user_name: placeholderRow.user_name.trim(),
          email_id: placeholderRow.email_id.trim(),
          license: 'Unassigned',
          action: '',
          status: 'Active'
        };
        append(newUser);
        
        // Reset placeholder and errors
        setPlaceholderRow({ user_name: '', email_id: '' });
        setPlaceholderErrors({ email_id: false });
      }
    }
  };

  // Email validation function
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };





  const onSubmit = (data: SubscribeUserTableFormData) => {
    console.log('Form submitted:', data);
    onSave?.(data);
  };

  const renderEditableCell = (index: number, field: 'user_name' | 'email_id') => {
    const fieldName = `users.${index}.${field}` as const;
    const error = errors.users?.[index]?.[field];

    return (
      <div className={styles.cellContainer}>
        <Controller
          name={fieldName}
          control={control}
          render={({ field }) => (
            <input
              type="text"
              {...field}
              className={`${styles.editInput} ${error ? styles.error : ''}`}
            />
          )}
        />
        {error && (
          <span className={styles.errorMessage}>
            {error.message}
          </span>
        )}
      </div>
    );
  };

  const renderPlaceholderCell = (field: 'user_name' | 'email_id') => {
    const isEmailField = field === 'email_id';
    const isDisabled = isEmailField && !placeholderRow.user_name.trim() && !placeholderRow.email_id.trim();
    const hasError = isEmailField && placeholderErrors.email_id;
    
    return (
      <div className={styles.cellContainer}>
        <input
          type="text"
          value={placeholderRow[field]}
          onChange={(e) => handlePlaceholderChange(field, e.target.value)}
          onBlur={() => handlePlaceholderFieldBlur(field)}
          className={`${styles.editInput} ${isDisabled ? styles.disabled : ''} ${hasError ? styles.error : ''}`}
          placeholder={field === 'user_name' ? 'Enter user name' : 'Enter email'}
          disabled={isDisabled}
        />
        {hasError && (
          <span className={styles.errorMessage}>
            Please enter a valid email address
          </span>
        )}
      </div>
    );
  };

  console.log('watch', watch())
  console.log('errors', errors)

  // Helper to determine if save button should be disabled
  const isSaveDisabled = !isDirty || !isValid || placeholderErrors.email_id || (placeholderRow.user_name.trim() && !placeholderRow.email_id.trim());

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={styles.formContainer}>
      <div className={styles.tableContainer}>
        <table className={styles.userTable}>
          <thead>
            <tr className={styles.tableHeader}>
              <th>USER NAME</th>
              <th>Email</th>
              <th>LICENSE</th>
              <th>ACTION</th>
              <th>STATUS</th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, index) => (
              <tr key={field.id} className={styles.tableRow}>
                <td className={styles.tableCell}>
                  {renderEditableCell(index, 'user_name')}
                </td>
                <td className={styles.tableCell}>
                  {renderEditableCell(index, 'email_id')}
                </td>
                <td className={styles.tableCell}>
                  <select
                    value={watchedUsers[index]?.license || 'Unassigned'}
                    onChange={(e) => handleLicenseChange(index, e.target.value as 'Assigned' | 'Unassigned')}
                    className={`${styles.dropdown} ${watchedUsers[index]?.license === 'Assigned' ? styles.assigned : styles.unassigned}`}
                  >
                    <option value="Assigned">Assigned</option>
                    <option value="Unassigned">Unassigned</option>
                  </select>
                </td>
                <td className={styles.tableCell}>
                  <select
                    value={watchedUsers[index]?.status || 'Select Action'}
                    onChange={(e) => {
                      if (e.target.value !== 'Select Action') {
                        handleActionChange(index, e.target.value);
                      }
                    }}
                    className={styles.dropdown}
                  >
                    <option value="Select Action">Select Action</option>
                    <option value="Invite">Invite</option>
                    <option value="Remove">Remove</option>
                  </select>
                </td>
                <td className={styles.tableCell}>
                  <span className={styles.status}>
                    {watchedUsers[index]?.status || 'Active'}
                  </span>
                </td>
              </tr>
            ))}
            {/* Placeholder row - always at the end */}
            <tr className={`${styles.tableRow} ${(placeholderRow.user_name.trim() || placeholderRow.email_id.trim() || placeholderErrors.email_id) ? '' : styles.placeholderRow}`}>
              <td className={styles.tableCell}>
                {renderPlaceholderCell('user_name')}
              </td>
              <td className={styles.tableCell}>
                {renderPlaceholderCell('email_id')}
              </td>
              <td className={styles.tableCell}>
                <select
                  value="Unassigned"
                  disabled
                  className={`${styles.dropdown} ${styles.unassigned}`}
                >
                  <option value="Unassigned">Unassigned</option>
                </select>
              </td>
                              <td className={styles.tableCell}>
                  <select
                    value="Select Action"
                    disabled={!placeholderRow.user_name.trim() && !placeholderRow.email_id.trim()}
                    className={`${styles.dropdown} ${(!placeholderRow.user_name.trim() && !placeholderRow.email_id.trim()) ? styles.disabled : ''}`}
                  >
                    <option value="Select Action">Select Action</option>
                  </select>
                </td>
              <td className={styles.tableCell}>
                <span className={styles.status}>
                  Active
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div className={styles.saveButtonContainer}>
        <button
          type="submit"
          disabled={isSaveDisabled}
          className={`${styles.saveButton} ${isSaveDisabled ? styles.disabled : ''}`}
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};

export default SubscribeUserTable; 