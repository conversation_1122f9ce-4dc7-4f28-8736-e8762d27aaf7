import React, { useEffect, useState } from 'react'
import { Dialog, DialogContent, IconButton } from '@mui/material'
import { Close as CloseIcon } from '@mui/icons-material'
import styles from './SubscribeTab.module.scss'
import clsx from 'clsx'
import NextPaymentDetail from './NextPaymentDetail'
import SubscribeUserTable from './SubscribeUserTable'
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore'
import EditLicensesDialog from './components/EditLicensesDialog'
import UploadUserListDialog from './components/UploadUserListDialog'
import SubscriptionSetup from 'src/renderer2/component/SubscriptionDialog/components/SubscriptionSetup'
import { SubscribeUserTableFormData } from './SubscribeUserTable.schema';
import SubscribeUserTableClean from './SubscribeUserTableClean'

const SubscribeTab = ({setActiveTab, setSaveFunctions}: {setActiveTab: (tab: string) => void, setSaveFunctions: (saveFunctions: any) => void}) => {
    const { setSubscriptionDialogOpen , setIsFromSetting , userSubscription , setUserList } = useSubscriptionStore();
    
    // Dialog state management
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogContent, setDialogContent] = useState<React.ReactNode>(null);

    useEffect(() => {
        setUserList([
            {
              id: '1',
              user_name: 'Shep Hickey',
              email_id: '<EMAIL>',
              license: 'Assigned',
              action: 'Select Action',
              status: 'Active'
            },
            {
              id: '2',
              user_name: 'John Smith',
              email_id: '<EMAIL>',
              license: 'Unassigned',
              action: 'Select Action',
              status: 'Pending'
            }
          ]);
    }, [])


    const handleBuyLicense = () => {
        setSubscriptionDialogOpen(true);
        setIsFromSetting(true);
    }

    const handleEditPaymentInfo = () => {
    }

    // Dialog handlers for the three buttons
    const handleEditLicenses = () => {
        setDialogContent(<EditLicensesDialog />);
        setDialogOpen(true);
    };

    const handleEditPaymentInfoDialog = () => {
        setDialogContent(<SubscriptionSetup currentMode={"EDIT_PAYMENT"} />);
        setDialogOpen(true);
    };

    const handleUploadUserList = () => {
        setDialogContent(<UploadUserListDialog closeDialog={handleCloseDialog}/>);
        setDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setDialogContent(null);
    };

    const handleUserTableSave = (data: SubscribeUserTableFormData) => {
        console.log('User table data saved:', data);
        // Here you can implement the logic to save the user data
        // For example, make an API call to update the users
    };

    return (
        <div className={styles.subscribeTab}>
            <div className={clsx(styles.subscribeTabHeader, styles.grid)}>
                <div className={clsx(styles.nextPaymentDetail, styles.equalSpacing, styles.licenseDetailText)}>
                    {
                        userSubscription?.id ? <NextPaymentDetail /> : "Click 'Buy Licenses' to proceed with your purchase."
                    }
                </div>
                <div className={clsx(styles.licenseDetail, styles.equalSpacing)}>

                    <span className={styles.licenseDetailText}>{ `${userSubscription?.assigned_license || 0} OF ${userSubscription?.total_license || 0} LICENSES ASSIGNED`}</span>
                </div>
            </div>
            <div className={clsx(styles.subscriptionActions, styles.grid)}>
                <div className={clsx(styles.actionbarTitle, styles.equalSpacing)}>
                    <p className={styles.title}>ADD USERS & ASSIGN LICENSES</p>
                    <p className={styles.description}>After you purchase licenses, you can assign those licenses to users.</p>
                </div>
                <div className={clsx(styles.actionbarButtons, styles.equalSpacing)}>
                    {
                        userSubscription?.id ? (
                            <>
                    <button className={styles.actionbarButton} onClick={handleEditLicenses}>
                        <span>Edit Licenses</span>
                    </button>
                    <button className={styles.actionbarButton} onClick={handleEditPaymentInfoDialog}>
                        <span>Edit Payment Info</span>
                    </button>
                    <button className={styles.actionbarButton} onClick={handleUploadUserList} >
                        <span>Upload User List</span>
                    </button>
                            
                            </>
                        ) : (
                            <>
                            <button className={styles.actionbarButton} onClick={handleBuyLicense}>
                        <span>Buy Licenses</span>
                    </button>
                            </>
                        )
                    }
                </div>
            </div>
            <div className={styles.subscribeUserTable}>
                    {/* <SubscribeUserTable onSave={handleUserTableSave} /> */}
                    <SubscribeUserTableClean onSave={handleUserTableSave} />
            </div>

            {/* MUI Dialog */}
            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    sx: {
                        backgroundColor: '#2a2a2a',
                        color: 'white',
                        borderRadius: 2,
                    }
                }}
            >
                <DialogContent sx={{ p: 0, position: 'relative' }}>
                    <IconButton
                        onClick={handleCloseDialog}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: '#b0b0b0',
                            zIndex: 1,
                            '&:hover': {
                                color: 'white',
                            }
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                    {dialogContent}
                </DialogContent>
            </Dialog>
        </div>
    )
}

export default SubscribeTab