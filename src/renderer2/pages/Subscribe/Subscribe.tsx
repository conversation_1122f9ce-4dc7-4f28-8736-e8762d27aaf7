import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { commomKeys, useGlobalStore } from '@bryzos/giss-ui-library';
import styles from './Subscribe.module.scss';
import clsx from 'clsx';
import { reactQueryKeys } from '../../common';
import SearchHeader from '../../pages/SearchHeader';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';
import { Fade, Tooltip } from '@mui/material';
import { ReactComponent as DropdownIcon } from '../../assets/New-images/Dropdpown_Up_Arrow.svg';
import { ReactComponent as ArrowIcon } from '../../assets/New-images/Subscription-Arrow.svg';
import { ReactComponent as UpdatePaymentDetails } from '../../assets/New-images/Update-Payment-Details.svg';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import SubscribeRightSideWindow from '../../component/SubscribeRightSideWindow/SubscribeRightSideWindow';
import { CardCvcElement, CardExpiryElement, CardNumberElement, Elements, useElements, useStripe } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import useGetSubscriptionsPricing from 'src/renderer2/hooks/useGetSubscriptionsPricing';
import useGetUserSubscription from 'src/renderer2/hooks/useGetUserSubscription';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useQueryClient } from '@tanstack/react-query';
// import usePostReinviteUser from 'src/renderer2/hooks/usePostReinviteUser';
import usePostUserSubscription from 'src/renderer2/hooks/usePostUserSubscription';
import usePostUpdateSubscribePayment from 'src/renderer2/hooks/usePostUpdateSubscribePayment';
import usePostUpdateSubscribeAccount from 'src/renderer2/hooks/usePostUpdateSuscribeAccount';
import usePostVerifyBankAccount from 'src/renderer2/hooks/usePostVerifyBankAccount';
import usePostAchSubscribe from 'src/renderer2/hooks/usePostAchSubscribe';
import usePostCreateSetupIntent from 'src/renderer2/hooks/usePostCreateSetupIntent';
import usePostStorePaymentMethod from 'src/renderer2/hooks/usePostStorePaymentMethod';
import AchVerificationDialog from 'src/renderer2/component/AchVerificationDialog';
import useUpdateAchSubscription from 'src/renderer2/hooks/useUpdateAchSubscription';
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment';


const options = {
    fonts: [
      {
        cssSrc: "https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap",
      },
    ],
  };
// Payment method constants
const CREDIT_CARD = 'card';
const ACH = 'ach_debit';
const COMPANY = 'company';
const INDIVIDUAL = 'individual';
const SUBSCRIPTION_STATUS = {
    PENDING: 'pending',
    SUCCESS: 'success',
    BANK_VERIFICATION_PENDING: 'bank_verification_pending',
    FAILED: 'failed',
    CANCELLED: 'canceled',
    past_due: 'past_due'
}

// Define validation schema
const subscriptionSchema = yup.object().shape({
    numberOfUsers: yup.number().required().min(1),
    paymentMethod: yup.string().required(),
    // Credit card fields
    cardholderFirstName: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === CREDIT_CARD,
        then: (schema) => schema.required('First name is required')
    }),
    cardholderLastName: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === CREDIT_CARD,
        then: (schema) => schema.required('Last name is required')
    }),
    billingZipCode: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === CREDIT_CARD,
        then: (schema) => schema.required('Billing zip code is required')
    }),
    // ACH fields
    accountName: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === ACH,
        then: (schema) => schema.required('Account name is required')
    }),
    accountType: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === ACH,
        then: (schema) => schema.required('Account type is required')
    }),
    routingNumber: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === ACH,
        then: (schema) => schema.required('Routing number is required')
            .matches(/^\d{9}$/, 'Routing number must be exactly 9 digits')
            .test('valid-routing-number', 'Invalid routing number format', (value) => {
                if (!value) return false;

                // Basic checksum validation for US routing numbers
                const digits = value.split('').map(Number);
                if (digits.length !== 9) return false;

                const sum =
                    3 * (digits[0] + digits[3] + digits[6]) +
                    7 * (digits[1] + digits[4] + digits[7]) +
                    (digits[2] + digits[5] + digits[8]);

                return sum % 10 === 0;
            })
    }),
    bankName: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === ACH,
        then: (schema) => schema.required('Bank name is required')
    }),
    accountNumber: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === ACH,
        then: (schema) => schema.required('Account number is required')
            .matches(/^\d+$/, 'Account number must contain only digits')
            .min(8, 'Account number must be at least 8 digits')
            .max(17, 'Account number must not exceed 17 digits')
    }),
    reEnterAccountNumber: yup.string().optional().when('paymentMethod', {
        is: (val: string) => val === ACH,
        then: (schema) => schema.required('Please confirm account number')
            .oneOf([yup.ref('accountNumber')], 'Account numbers must match')
    }),
    stripePriceId: yup.string().optional(),
    agreeToTerms: yup.boolean().oneOf([true], 'You must agree to terms and conditions').required()
});

// Define form values interface
interface SubscriptionFormValues {
    numberOfUsers: number;
    paymentMethod: string;
    // Credit card fields
    cardholderFirstName?: string;
    cardholderLastName?: string;
    cardNumber?: string;
    expiryDate?: string;
    cvv?: string;
    billingZipCode?: string;
    // ACH fields
    accountName?: string;
    accountType?: string;
    routingNumber?: string;
    bankName?: string;
    accountNumber?: string;
    reEnterAccountNumber?: string;
    // Stripe Price ID
    stripePriceId?: string;
    // Terms agreement
    agreeToTerms: boolean;
    // User email addresses - dynamic
    [key: string]: any; // Allow dynamic properties for email addresses
}

// const pricingTiers = [
//     {
//         "min_user_count": 1,
//         "max_user_count": 20,
//         "subscription_amount": "80.00",
//         "stripe_price_id": "price_1RE5bKR9FrerPn28Cu87ebUT"
//     },
//     {
//         "min_user_count": 21,
//         "max_user_count": 40,
//         "subscription_amount": "40.00",
//         "stripe_price_id": "price_1RE5bKR9FrerPn28Cu87ebUT"
//     },
//     {
//         "min_user_count": 50,
//         "max_user_count": null,
//         "subscription_amount": "20.00",
//         "stripe_price_id": "price_1RE5bKR9FrerPn28Cu87ebUT"
//     }
// ]

const Subscribe: React.FC = () => {
    const navigate = useNavigate();
    const { userData, setShowLoader, setSubscriptionStatus, userSubscription} = useGlobalStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const [step, setStep] = useState(1);
    const stripe = useStripe();
    const elements = useElements();
    const [menuOpen, setMenuOpen] = useState(false);
    const [accountTypeOpen, setAccountTypeOpen] = useState(false);
    const [showUpdateAccount, setShowUpdateAccount] = useState(false);
    const [monthlyPrice, setMonthlyPrice] = useState(0); // Default to the price in the tier

    // Add state for Stripe Elements
    const [stripeError, setStripeError] = useState<string | null>(null);
    
  const [cardError, setCardError] = useState<{
    cardNumber: string | null,
    cardExpiry: string | null,
    cardCvc: string | null,
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvc: null,
  });
    const [cardComplete, setCardComplete] = useState({
        cardNumber: false,
        cardExpiry: false,
        cardCvc: false
    });
    useGetUserSubscription();
    const { data: subscriptionsPricing } = useGetSubscriptionsPricing();
    // const {
    //     mutateAsync: reinviteUser
    // } = usePostReinviteUser();

    const [isUpdatePaymentModule, setIsUpdatePaymentModule] = useState(false);
    const [isCardDetailsRequired, setIsCardDetailsRequired] = useState(false);
    const [showAchDebitVerifyDialog, setShowAchDebitVerifyDialog] = useState(false);
    const query = useQueryClient();

    // Initialize hooks for ACH operations
    const { mutateAsync: verifyBankAccount } = usePostVerifyBankAccount();
    const { mutateAsync: achSubscribe } = usePostAchSubscribe();
    const { mutateAsync: updateAchSubscription } = useUpdateAchSubscription();
    // Initialize the createSetupIntent hook
    const { mutateAsync: createSetupIntent } = usePostCreateSetupIntent();
    const { mutateAsync: storePaymentMethod } = usePostStorePaymentMethod();


    // Handler for ACH verification
    const handleAchVerification = async (data: any) => {
        try {
            let verificationParams: any = {
                data: {
                }
            };

            // Handle verification based on the type
            if (data.type === 'descriptor') {
                // Handle descriptor code verification
                const { descriptorCode } = data;
                if (stripe) {
                    verificationParams.data.descriptor_code = descriptorCode;
                }
            } else if (data.type === 'amounts') {
                // Handle amounts verification
                const { amount1, amount2 } = data;
                if (stripe) {
                    verificationParams.data.amounts = [amount1, amount2];
                }
            }
            
            setShowLoader(true);

            // Call the server to verify the microdeposits using our hook
            const verificationResponse = await verifyBankAccount(verificationParams);
            if(verificationResponse.data.verification_status){
                setShowAchDebitVerifyDialog(false);
                await handleAchSubscribe();
            } else {
                setShowLoader(false);
                // showCommonDialog(null, 'Verification failed. Please check the amounts and try again.', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
                // setStripeError('Verification failed. Please check the amounts and try again.');
            }
        } catch (error: any) {
            setShowLoader(false);
            setStripeError(error.message || 'An error occurred during verification');
        }
    };

    const handleAchSubscribe = async () => {
        try {
                // After verification, create the subscription using our hook
                const subscriptionParams = {
                    data: {
                        total_license: Number(numberOfUsers),
                        payment_method: ACH,
                        payment_details: {
                            account_name: watch('accountName'),
                            account_type: watch('accountType'),
                            bank_name: watch('bankName'),
                            account_holder_type: COMPANY
                        }
                    }
                };
                let subscriptionResponse;
                if(!userSubscription.active_customer || !isUpdatePaymentModule){
                    subscriptionResponse = await achSubscribe(subscriptionParams);
                }
                else{
                    subscriptionResponse = await updateAchSubscription(subscriptionParams);
                }

                if (!subscriptionResponse.data || !subscriptionResponse.data.client_secret) {
                    setShowLoader(false);
                    return;
                }

                const { client_secret: clientSecret } = subscriptionResponse.data;

                // Confirm the payment
                if (stripe) {
                    const { error, paymentIntent } = await stripe.confirmUsBankAccountPayment(clientSecret);

                    if (error) {
                        setStripeError(error.message || null);
                    } else if (paymentIntent) {
                        // Close the verification dialog

                        if (paymentIntent.status === 'requires_payment_method') {
                            showCommonDialog(null, 'Subscription successful!', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
                        } else if (paymentIntent.status === 'processing') {
                            showCommonDialog(null, 'Your subscription is being processed. The payment will be completed shortly.', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
                        } else if (paymentIntent.status === 'requires_action') {
                            showCommonDialog(null, 'Your subscription has been created but payment is still processing. ACH payments typically take 3-5 business days to complete.', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
                        } else {
                            setStripeError('Subscription is being processed. Please check your email for updates.');
                        }
                    }
                }
        } catch (err: any) {
            setStripeError(err.message || 'An error occurred during verification');
        } finally {
            setShowLoader(false);
        }
    }
    const {
        mutateAsync: saveUserSubscription
    } = usePostUserSubscription();
    const {
        mutateAsync: updateUserPayment
    } = usePostUpdateSubscribePayment();
    const {
        mutateAsync: updateUserAccount
    } = usePostUpdateSubscribeAccount();
    const{
        mutateAsync: buyerSettingsPayment
    } = usePostBuyerSettingsPayment();

    const {
        register,
        handleSubmit,
        formState: { errors, isValid, isDirty, dirtyFields },
        watch,
        setValue,
        control,
        setError,
        clearErrors,
        reset,
    } = useForm<SubscriptionFormValues>({
        resolver: yupResolver(subscriptionSchema),
        mode: 'onChange',
        defaultValues: {
            numberOfUsers: 1,
            paymentMethod: '',
            agreeToTerms: false
        }
    });
    const numberOfUsers = watch('numberOfUsers');
    const paymentMethod = watch('paymentMethod');
    const agreementCheck = watch('agreeToTerms');
    const cardholderFirstName = watch('cardholderFirstName');
    const cardholderLastName = watch('cardholderLastName');
    const billingZipCode = watch('billingZipCode');
    const { setLoadComponent} = useRightWindowStore();

    // Memoize the isCardFormComplete calculation
    const isCardFormComplete = useMemo(() => {
        if(paymentMethod === CREDIT_CARD){
            let commonValidation =false
            const isValidCardDetails = !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc
            if(!cardComplete.cardNumber &&
                !cardComplete.cardExpiry &&
                !cardComplete.cardCvc && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!userSubscription?.id && !!userSubscription?.payment_details) || (userSubscription?.status === SUBSCRIPTION_STATUS.CANCELLED))){
                return true;
            }
            if(isUpdatePaymentModule){
                const isUserProfileUpdated = (
                    userSubscription?.payment_details?.first_name !== watch('cardholderFirstName') ||
                    userSubscription?.payment_details?.last_name !== watch('cardholderLastName') ||
                    userSubscription?.payment_details?.zipcode !== watch('billingZipCode')
                )
                commonValidation = (isCardDetailsRequired ?
                    (!stripeError && !cardError.cardNumber && !cardError.cardExpiry && !cardError.cardCvc && ((!cardComplete.cardCvc && !cardComplete.cardExpiry && !cardComplete.cardNumber && isUserProfileUpdated) || (cardComplete.cardCvc && cardComplete.cardExpiry && cardComplete.cardNumber)))
                    :
                    isUserProfileUpdated
                )
            }
            else{
                commonValidation = (isValidCardDetails &&
                    !stripeError)
            }
            return (commonValidation &&
                !!cardholderFirstName &&
                !!cardholderLastName &&
                !!billingZipCode &&
                /^\d{5}$/.test(billingZipCode || '')
            )
        }
    }, [
        paymentMethod,
        isUpdatePaymentModule,
        cardComplete.cardNumber,
        cardComplete.cardExpiry,
        cardComplete.cardCvc,
        stripeError,
        cardholderFirstName,
        cardholderLastName,
        billingZipCode,
        isDirty,
        isCardDetailsRequired,
        userSubscription,
        cardError
    ]);

    // Add custom styling for Stripe Elements
    const stripeElementsStyle = {
        base: {
            color: '#fff',
            fontFamily: 'Inter, sans-serif',
            fontSize: '16px',
            '::placeholder': {
                color: "rgba(255, 255, 255, 0.67)",
            },
            backgroundColor: '#3f4753',
            padding: '6px 16px',
        },
        invalid: {
            color: '#ff6b6b',
            iconColor: '#ff6b6b',
        },
    };

    interface PricingTier {
        min_user_count: number;
        max_user_count: number | null;
        subscription_amount: number | string;
        stripe_price_id: string;
    }

    const calculateSubscriptionAmount = (userCount: number = 0, pricingTiers: PricingTier[] | null | undefined): PricingTier | null => {
        if (!pricingTiers?.length) {
            return null;
        }

        try {
            const tier = pricingTiers.find(tier => {
                const isAboveMinimum = tier.min_user_count <= userCount;
                const isBelowMaximum = tier.max_user_count === null || userCount <= tier.max_user_count;
                return isAboveMinimum && isBelowMaximum;
            });

            if (!tier) {
                return null;
            }

            return tier;

        } catch (error) {
            console.error('Error calculating subscription amount:', error);
            return null;
        }
    };
    
    // useEffect(() => {
    //    setBankPaymentMethodId(undefined);
    //    setSetupIntentId(undefined);
    // //    initializeAchForm2();
    // //    initializeCardForm2();
    // },[paymentMethod])

    // Update prices when the user count changes
    useEffect(() => {
        if(subscriptionsPricing){
        const pricingData = calculateSubscriptionAmount(Number(numberOfUsers), subscriptionsPricing);
            if (pricingData) {
                setMonthlyPrice(Number(pricingData.subscription_amount));
            }
        }
    }, [numberOfUsers, subscriptionsPricing]);

    useEffect(() => {
        setLoadComponent(<SubscribeRightSideWindow />);
        return () => {
            setLoadComponent(null);
            reset();
        }
    }, []);

    useEffect(() => {
        setStripeError(null);
        setCardComplete({
            cardNumber: false,
            cardExpiry: false,
            cardCvc: false
        });
    },[showUpdateAccount])

    useEffect(() => {
        if (userSubscription?.error_message) {
            setShowUpdateAccount(false);
        }else if(subscriptionsPricing && userSubscription){
            const pricingData = calculateSubscriptionAmount(Number(userSubscription?.total_license), subscriptionsPricing);
            const nextPaymentCyclePrice = Number(pricingData?.subscription_amount)* Number(userSubscription?.total_license);
            const isAccountPending = (userSubscription?.status !== SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING);
            if(userSubscription?.id){
                setShowUpdateAccount(isAccountPending && userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED);
                setIsUpdatePaymentModule(!isAccountPending);
                setValue('paymentMethod', userSubscription?.payment_method);
                setValue('numberOfUsers', userSubscription?.total_license);
                setValue('existingNumberOfUsers', userSubscription?.total_license);
                setValue('nextPaymentCyclePrice', nextPaymentCyclePrice);
                if(userSubscription?.subscribed_emails?.length > 0){
                    setValue('emailAddress', userSubscription?.subscribed_emails);
                }
                setValue('agreeToTerms', !!userSubscription?.id);
                setValue('nextPaymentCycleDay', userSubscription?.next_payment_cycle_day);
                setValue('bankNameOrCardBrand', userSubscription?.payment_details?.card_display_brand ?? userSubscription?.payment_details?.bank_name);
            }else{
                setShowUpdateAccount(false);
                setIsUpdatePaymentModule(false);
                setValue('paymentMethod', '');
                setValue('numberOfUsers', 1);
                setValue('existingNumberOfUsers', 1);
                setValue('nextPaymentCyclePrice', 0);
                setValue('nextPaymentCycleDay', 0);
            }
            if(userSubscription?.payment_method === CREDIT_CARD){
                setValue('paymentMethod', userSubscription?.payment_method);
                initializeCardForm();
            } else if(userSubscription?.payment_method === ACH){
                setValue('paymentMethod', userSubscription?.payment_method);
                initializeAchForm();
            }
            setSubscriptionStatus(null);
        }
        setShowLoader(false);
    }, [userSubscription, subscriptionsPricing]);

    const initializeCardForm = () => {
            setValue('cardholderFirstName', userSubscription?.payment_details?.first_name);
            setValue('cardholderLastName', userSubscription?.payment_details?.last_name);
            setValue('billingZipCode', userSubscription?.payment_details?.zipcode);
            setValue('cardNumberLast4Digits', userSubscription?.payment_details?.card_number_last_four_digits);
            setValue('cardExpiry', userSubscription?.payment_details?.expiration_date);
            setValue('email_id', userSubscription?.payment_details?.email_id || userData?.data?.email_id);   
    }

    const initializeAchForm = () => {
        setValue('accountName', userSubscription?.payment_details?.account_name);
        setValue('routingNumber', userSubscription?.payment_details?.routing_number);
        // setValue('accountNumber', userSubscription?.account_number);
        setValue('accountType', userSubscription?.payment_details?.account_type?.toLowerCase());
        setValue('bankName', userSubscription?.payment_details?.bank_name);
        setValue('last4AccountNumber', userSubscription?.payment_details?.account_number);
        // setValue('reEnterAccountNumber', userSubscription?.account_number);
    }

//     const initializeAchForm2 = () => {
//         setValue('accountName', 'Rachel Green');
//         setValue('routingNumber', '*********');
//         setValue('accountNumber', '************');
//         setValue('accountType', 'checking');
//         setValue('bankName', 'Bank of America');
//         // setValue('last4AccountNumber', '************');
//         setValue('reEnterAccountNumber', '************');
//     }

//     const initializeCardForm2 = () => {
//         setValue('cardholderFirstName', 'Ross');
//         setValue('cardholderLastName', 'Geller');
//         setValue('billingZipCode', '10001');
//         setValue('cardNumberLast4Digits', '1234');
//         setValue('cardExpiry', '12/25');
//         // setValue('bankNameOrCardBrand', userSubscription?.card_display_brand ?? userSubscription?.bank_name);
// }

    // Update step based on form progress
    useEffect(() => {
        if (paymentMethod) {
            setStep(2);
        }

        if (agreementCheck) {
            setStep(3);
        }
    }, [paymentMethod, agreementCheck]);

    useEffect(()=>{
        const price = (numberOfUsers * monthlyPrice).toFixed(2);
        setValue('perUserPrice', price ?? '0.00');
    },[numberOfUsers, monthlyPrice])

    const handleStepChange = (newStep: number) => {
        if (newStep <= 3 && newStep > 0) {
            setStep(newStep);
        }
    };

    // Helper function to validate routing number
    const validateRoutingNumber = (routingNumber: string): boolean => {
        // Check if it's exactly 9 digits
        if (!/^\d{9}$/.test(routingNumber)) {
            return false;
        }

        // Perform checksum validation
        const digits = routingNumber.split('').map(Number);
        const sum =
            3 * (digits[0] + digits[3] + digits[6]) +
            7 * (digits[1] + digits[4] + digits[7]) +
            (digits[2] + digits[5] + digits[8]);

        return sum % 10 === 0;
    };

    // Helper function to validate account number
    const validateAccountNumber = (accountNumber: string): boolean => {
        // Check if it contains only digits
        if (!/^\d+$/.test(accountNumber)) {
            return false;
        }

        // Check length (typically between 8-17 digits)
        return accountNumber.length >= 8 && accountNumber.length <= 17;
    };

    // Handle input change for bank account fields with real-time validation
    const handleBankAccountInputChange = (field: string, value: string): void => {
        setValue(field as any, value);

        // Perform real-time validation
        if (field === 'routingNumber') {
            if (value && !validateRoutingNumber(value)) {
                setError(field as any, {
                    type: 'manual',
                    message: value.length !== 9
                        ? 'Routing number must be exactly 9 digits'
                        : 'Invalid routing number format'
                });
            } else {
                clearErrors(field as any);
            }
        } else if (field === 'accountNumber') {
            if (value && !validateAccountNumber(value)) {
                setError(field as any, {
                    type: 'manual',
                    message: !/^\d+$/.test(value)
                        ? 'Account number must contain only digits'
                        : value.length < 8
                            ? 'Account number must be at least 8 digits'
                            : 'Account number must not exceed 17 digits'
                });
            } else {
                clearErrors(field as any);

                // Also validate reEnterAccountNumber if it exists
                const reEnterValue = watch('reEnterAccountNumber');
                if (reEnterValue && reEnterValue !== value) {
                    setError('reEnterAccountNumber', {
                        type: 'manual',
                        message: 'Account numbers must match'
                    });
                } else if (reEnterValue) {
                    clearErrors('reEnterAccountNumber');
                }
            }
        } else if (field === 'reEnterAccountNumber') {
            const accountNumber = watch('accountNumber');
            if (value && accountNumber && value !== accountNumber) {
                setError(field as any, {
                    type: 'manual',
                    message: 'Account numbers must match'
                });
            } else {
                clearErrors(field as any);
            }
        }
    };


    const handlePaymentMethodChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setValue('paymentMethod', e.target.value)
        setValue('accountName', '')
        setValue('routingNumber', '')
        setValue('accountNumber', '')
        setValue('accountType', '')
        setValue('bankName', '')
        setValue('reEnterAccountNumber', '')
        setValue('cardholderFirstName', '')
        setValue('cardholderLastName', '')
        setValue('billingZipCode', '')
        setValue('cardNumberLast4Digits', '')
        setValue('cardExpiry', '')
        if(e.target.value === CREDIT_CARD){
            initializeCardForm();
        }
        else if(e.target.value === ACH){
            initializeAchForm();
        }
        handleStepChange(2);
    };

    const handleAgreementChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue('agreeToTerms', e.target.checked);
        if (e.target.checked) {
            handleStepChange(3);
        }
    };

    const onSubmit = async (data: SubscriptionFormValues) => {
        setShowLoader(true);
        // Toggle to show update account section
        // setShowUpdateAccount(true);

        // return;
        if (data.paymentMethod === CREDIT_CARD) {
            if (!stripe || !elements) {
                // Stripe.js hasn't loaded yet
                setStripeError("Stripe hasn't loaded yet. Please try again.");
                setShowLoader(false);
                return;
            }

            // Check if card details are complete
            if ((!isUpdatePaymentModule && !userSubscription?.payment_details?.card_number_last_four_digits) && (!cardComplete.cardNumber || !cardComplete.cardExpiry || !cardComplete.cardCvc)) {
                setStripeError("Please complete all card details.");
                setShowLoader(false);
                return;
            }

            try {
                let _paymentMethod;
                const cardElement = elements.getElement(CardNumberElement);
                if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc) {
                    const { paymentMethod, error: paymentMethodError } = await stripe.createPaymentMethod({
                        type: 'card',
                        card: cardElement,
                        billing_details: {
                            email: data.email_id,
                            name: `${data.cardholderFirstName} ${data.cardholderLastName}`,
                            address: {
                                country: 'US',
                                postal_code: data.billingZipCode,
                            }
                        }
                    });
                    if (paymentMethodError) {
                    setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
                    setShowLoader(false);
                    return;
                    }
                    else{
                        _paymentMethod = paymentMethod;
                    }
                }
                
                const payload = {
                    "data": {
                        "total_license": !isUpdatePaymentModule ? Number(numberOfUsers) : undefined,
                        "payment_method": CREDIT_CARD,
                        "payment_details": {
                            "first_name": data.cardholderFirstName,
                            "last_name": data.cardholderLastName,
                            "zipcode": data.billingZipCode
                        },
                        "payment_method_id": (_paymentMethod && (userSubscription?.active_customer || isUpdatePaymentModule)) ? _paymentMethod.id : undefined
                    }
                }
                const paymentSettingPayload = {
                    "data": {
                        "payment_method": "card",
                        "payment_details": {
                            "zipcode": data.billingZipCode,
                            "payment_method_id": _paymentMethod ? _paymentMethod.id : undefined,
                            "first_name": data.cardholderFirstName, 
                            "last_name": data.cardholderLastName
                        }
                    }
                }
                let response;
                if(userSubscription?.status !== SUBSCRIPTION_STATUS.CANCELLED && (userSubscription?.active_customer || isUpdatePaymentModule)){
                    response = await updateUserPayment(payload);
                    setSubscriptionStatus(true);
                }else{
                    if(cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvc || dirtyFields.billingZipCode){
                        const buyerSettingsPaymentResponse = await buyerSettingsPayment(paymentSettingPayload);
                        if(buyerSettingsPaymentResponse.error_message){
                            setStripeError(buyerSettingsPaymentResponse.error_message);
                            showCommonDialog(null, buyerSettingsPaymentResponse.error_message, null, resetDialogStore, [
                              { name: commomKeys.errorBtnTitle, action: resetDialogStore }
                            ]);
                            setShowLoader(false);
                            return;
                        }
                    }
                    response = await saveUserSubscription(payload);
                    if (!response || !response?.client_secret) {
                        setStripeError('No client secret received from server');
                        setShowLoader(false);
                        return;
                    }

                    const { client_secret : clientSecret } = response;
                    const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(clientSecret);
                    if (confirmError) {
                        showCommonDialog(
                            null,
                            confirmError.message || 'An error occurred during payment processing',
                            commomKeys.actionStatus.error,
                            resetDialogStore,
                            [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
                          );
                        setStripeError(confirmError.message || 'An error occurred during payment processing');
                    } else if (paymentIntent.status === 'succeeded') {

                    }
                }
              } catch (err) {
                showCommonDialog(
                    null,
                    err?.message || 'An error occurred during payment processing',
                    commomKeys.actionStatus.error,
                    resetDialogStore,
                    [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
                  );
                setStripeError(err.message || 'An error occurred during payment processing');
                setShowLoader(false);
              }
        } else if(!!stripe) {
            try {
                const isNewAccount = ((!!watch('accountNumber') && watch('accountNumber')?.slice(-4) !== userSubscription?.account_number) 
                || 
                userSubscription?.routing_number !== watch('routingNumber'));
                if((userSubscription?.status === SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING && !isNewAccount)){
                    setShowAchDebitVerifyDialog(true);
                    setShowLoader(false);
                }
                else if((!isUpdatePaymentModule || 
                    (isUpdatePaymentModule && isNewAccount))) {
                    // Create setup intent using our hook
                    const setupIntentResponse = await createSetupIntent();

                    if (!setupIntentResponse.data || !setupIntentResponse.data.client_secret) {
                        setStripeError('Failed to initialize bank payment');
                        setShowLoader(false);
                        return;
                    }

                    const clientSecret = setupIntentResponse.data.client_secret;

                    // Create payment method on the client side
                    const routingNumber = watch('routingNumber');
                    const accountNumber = watch('accountNumber');
                    const accountType = watch('accountType');
                    const accountName = watch('accountName');

                    if (!routingNumber || !accountNumber || !accountType || !accountName) {
                        setStripeError('Please complete all bank account fields');
                        setShowLoader(false);
                        return;
                    }

                    const { paymentMethod, error } = await stripe.createPaymentMethod({
                        type: 'us_bank_account',
                        us_bank_account: {
                            routing_number: routingNumber,
                            account_number: accountNumber,
                            account_holder_type: 'company',
                            account_type: accountType,
                        },
                        billing_details: {
                            name: accountName,
                            email: userData?.data?.email_id,
                            address: {
                                country: 'US',
                                postal_code: watch('billingZipCode'),
                            }
                        },
                    });

                    if (error) {
                        showCommonDialog(
                            null,
                            error.message || 'Error creating payment method',
                            commomKeys.actionStatus.error,
                            resetDialogStore,
                            [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
                          );
                        setStripeError(error.message || 'Error creating payment method');
                        setShowLoader(false);
                        return;
                    }

                    // Confirm the setup intent with the payment method
                    const { setupIntent, error: setupError } = await stripe.confirmUsBankAccountSetup(
                        clientSecret,
                        {
                            payment_method: paymentMethod.id,
                        }
                    );

                    if (setupError) {
                        setStripeError(setupError.message || 'Error confirming bank account setup');
                        setShowLoader(false);
                        return;
                    }

                    // Store payment method using our hook
                    const storePaymentParams = {
                        data: {
                            total_license: Number(numberOfUsers),
                            payment_method: data.paymentMethod,
                            payment_details: {
                                account_name: accountName,
                                account_type: accountType,
                                bank_name: watch('bankName') || '',
                                account_holder_type: COMPANY
                            },
                            payment_method_id: paymentMethod.id,
                            setup_intent_id: setupIntent.id,
                        }
                    };

                    const createCustomerResponse = await storePaymentMethod(storePaymentParams);

                    if (!createCustomerResponse.data) {
                        setStripeError('Failed to create customer');
                        setShowLoader(false);
                        return;
                    }

                    if (setupIntent.status === 'requires_action') {
                        // Save the setupIntent ID and show the verification form
                        setShowAchDebitVerifyDialog(true);
                        setShowLoader(false);
                    }
                }
                else{
                    handleAchSubscribe();
                }
            } catch (err: any) {
                
                setStripeError(err.message || 'An error occurred during ACH setup');
                setShowLoader(false);
            }
        }
    };

    // Memoize the handleCardChange function
    const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
        setIsCardDetailsRequired(true);
        setCardComplete(prev => ({
            ...prev,
            [fieldName]: event.complete
        }));

        // Set or clear error based on the event
        if (event.error || (!event.empty && !event.complete)) {
            setCardError(prev => ({
                ...prev,
                [fieldName]: event.error?.message ?? 'Incomplete card details'
            }));
        } else {
            setCardError(prev => ({
                ...prev,
                [fieldName]: null
            }));
            setStripeError(null);
        }
    }, [cardError]);

    // Function to generate an array of length n (for the user inputs)
    const generateUserFields = (count: number) => {
        return Array.from({ length: count }, (_, index) => {return {isEditing: false, inviteSent: false}});
    };

    // Payment method options for dropdown
    const paymentMethodOptions = [
        { title: 'Debit/Credit Card', value: CREDIT_CARD },
        { title: 'ACH Debit', value: ACH, disabled: true }
    ];

    const accountType = [
        { title: 'Checking', value: 'checking' },
        { title: 'Savings', value: 'savings' }
    ];

    const agreementTitle = () => {
        return (
            <div className={styles.debitAuthorizationAgreement}>
                <div className={styles.debitAuthAgreementTitle}>Debit Authorization Agreement</div>
                <p>
                    By checking the box to proceed with your Bryzos subscription, you authorize Bryzos, LLC and/or Bryzos Holdings,
                    LLC to electronically debit and/or credit (ACH, Credit Card and/or Debit Card) your defined bank account(s) as necessary
                    to execute payment for your Bryzos subscription.
                </p>
            </div>
        )
    }

    // Check if ACH form fields are valid using useMemo for performance
    const isAchFormValid = useMemo(() => {
        if (paymentMethod !== ACH) return false;

        const routingNumber = watch('routingNumber');
        const accountNumber = watch('accountNumber');
        const reEnterAccountNumber = watch('reEnterAccountNumber');
        const accountName = watch('accountName');
        const bankName = watch('bankName');
        const accountType = watch('accountType');

        // Check if all required fields are filled and valid
        if(isUpdatePaymentModule){
            const isNewAccount = ((!!watch('accountNumber') && watch('accountNumber')?.slice(-4) !== userSubscription?.account_number) 
                || 
                userSubscription?.routing_number !== watch('routingNumber'));
            if(!isNewAccount && userSubscription?.status === SUBSCRIPTION_STATUS.BANK_VERIFICATION_PENDING){
                return true;
            }
            if(isNewAccount){
                return (
                    routingNumber &&
                    validateRoutingNumber(routingNumber) &&
                    accountNumber &&
                    validateAccountNumber(accountNumber) &&
                    reEnterAccountNumber &&
                    accountNumber === reEnterAccountNumber &&
                    accountName &&
                    bankName &&
                    accountType
                );
            }
            else if(watch('accountName') !== userSubscription?.account_name || watch('bankName') !== userSubscription?.bank_name || watch('accountType') !== userSubscription?.account_type.toLowerCase()){
                return (
                    accountName &&
                    bankName &&
                    accountType
                );
            }
            return false;
        }
        else{
            return (
                routingNumber &&
                validateRoutingNumber(routingNumber) &&
                accountNumber &&
                validateAccountNumber(accountNumber) &&
                reEnterAccountNumber &&
                accountNumber === reEnterAccountNumber &&
                accountName &&
                bankName &&
                accountType
            );
        }
        
    }, [paymentMethod, watch, validateRoutingNumber, validateAccountNumber, userSubscription]);

    // Check if the submit button should be enabled
    const isSubmitEnabled = () => {
        // For credit card payment method
        if (paymentMethod === CREDIT_CARD) {
            return agreementCheck && isCardFormComplete;
        }
        // For ACH payment method
        else if (paymentMethod === ACH) {
            return agreementCheck && isAchFormValid;
        }
        // Default case when no payment method selected
        return false;
    };

    const enableSubmitButton = useMemo(() => {
        return isSubmitEnabled();
    }, [paymentMethod, agreementCheck, isCardFormComplete, isAchFormValid]);

    const handleUpdateAccount = async () => {
        const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const emailAddresses = watch('emailAddress').filter(Boolean) || [];
        let hasEmailError = false;
        const emailList: string[] = [];
        emailAddresses.forEach((emailAddress: any, index: number) => {
        const email = emailAddress.email_id;
        const emailTrimmed = email.trim();
            if(emailTrimmed.length === 0){
                return;
            }
            if(!emailRegex.test(emailTrimmed)){
                setError(`emailAddress.${index}`, { message: 'Please enter a valid email address' });
                hasEmailError = true;
            }
            emailList.push(emailTrimmed);
        });
        if(hasEmailError){
            return;
        }
        const payload ={
            "data": {
                "total_license": Number(watch('numberOfUsers')),
                "subscribed_emails": emailList
            }
        }
        try {
            setShowLoader(true);
            const response = await updateUserAccount(payload);
            if (
                typeof response === "object" &&
                "error_message" in response
            ) {
                const {reason, email:emails} = response.error_message;
                if(reason === 'invalid_domain')

                    emails.forEach((email: any, index: number) => {
                        const _i = watch('emailAddress').findIndex(({email_id})=> email === email_id);
                        setError(`emailAddressDomain.${_i}`, {message: 'User must have same company domain'});
                    });
                else
                    showCommonDialog(null, response.error_message ||'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
                    setShowLoader(false);
            } else {
                query.invalidateQueries([reactQueryKeys.getUserSubscription]);
                showCommonDialog(null, 'Account updated successfully', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
            }
        } catch (err) {
            showCommonDialog(null, 'Something went wrong', null, resetDialogStore, [{ name: 'OK', action: resetDialogStore }]);
            setShowLoader(false);
        } finally{
            setShowLoader(false);
        }
    }

    const emailAddresses = watch('emailAddress') || [];
    const existingEmails = userSubscription?.subscribed_emails || [];

    // Check if number of users is the same
    const usersSame =Number(watch('numberOfUsers')) === 0 || (Number(watch('existingNumberOfUsers')) === Number(watch('numberOfUsers')));

    // Check if all email addresses are the same
    const emailsSame = emailAddresses.every((emailAddress: any, index: number) => {
        const email = emailAddress.email_id;
        if(existingEmails.length === 0){
            return !email;
        }
        // console.log("email", email, existingEmails[index], index, existingEmails.length);
        return email === existingEmails[index]?.email_id || (email.length === 0 && index >= existingEmails.length);
    });

    // Enable update if either users or emails have changed
    const isUpdateAccountDisabled = (usersSame && emailsSame);

    // const inviteUser = async(email: string,index) => {
    //     const _emailList = [...emailList];
    //     _emailList[index].inviteSent = true;
    //     setEmailList(_emailList);
    //     const response = await reinviteUser({
    //         "data": {
    //             "email_id": email
    //         }
    //     });
    // }

    const switchUpdatePaymentModule = (showUpdatePayment: boolean) => {
        setShowUpdateAccount(!showUpdatePayment);
        setIsUpdatePaymentModule(showUpdatePayment);
    }

    const [emailList, setEmailList] = useState<any[]>([]);
    useEffect(() => {
        setEmailList(generateUserFields(Number(watch('existingNumberOfUsers'))));
    }, [watch('existingNumberOfUsers')]);

    const openEmailEditModal = (index: number, isOpen: boolean) => {
        const _emailList = [...emailList];
        emailList[index].isEditing = isOpen;
        setEmailList(_emailList);
    }


    return (
        <div className={styles.subscribePage}>
            <div className={styles.subscriptionContainer}>
                <div className={styles.countdownBanner}>
                    Bryzos: Gone in 60 Seconds
                </div>
                <div className={styles.subscriptionCard}>
                    <div className={clsx(styles.subscriptionHeader, showUpdateAccount && styles.showUpdateAccountHeader)}>
                        <div className={styles.subscriptionHeaderTextContainer}>
                            <div className={styles.subscriptionHeaderText}>
                                {!showUpdateAccount ? 'Enter Number of Users' : 'Adjust Number of Users'} <br />for your Subscription
                            </div>
                            <div className={styles.pricePerUser}>${monthlyPrice} / USER / MONTH</div>
                        </div>
                        <div className={styles.userCalculator}>
                            <div className={styles.arrowIndicator}><ArrowIcon /></div>
                            <div className={styles.userCount}>
                                <InputWrapper>
                                    <CustomTextField
                                        className={styles.numberOfUsersInput}
                                        type='text'
                                        mode='wholeNumber'
                                        register={register("numberOfUsers")}
                                        placeholder='No. of Users'
                                        disabled={isUpdatePaymentModule && userSubscription?.active_customer}
                                    />
                                </InputWrapper>
                                <div className={styles.pricePerMonth}>
                                    <span className={styles.price}>${watch('perUserPrice')}</span>
                                    <span className={styles.perMonth}>per month</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className={styles.paymentInfoContainer}>
                        <p>Your subscription payment will be automatically debited on the 1st of each month.</p>
                        <p>Your first monthly payment, only, will be prorated if you subscribe after the first of the month.</p>
                    </div>
                    
                </div>
                <div className={styles.bottomContainer}>
                    {!showUpdateAccount ? (
                        <div className={styles.steps}>
                            <div className={styles.stepItem}>
                                <div className={styles.stepHeader}>
                                    <span className={clsx(styles.stepNumber, step >= 1 && styles.activeStep)}>
                                        Step 1:
                                    </span>
                                </div>
                                <div className={clsx(styles.stepContent,styles.dropdownContent)}>
                                <div className={`${styles.dropdown} ${menuOpen ? styles.dropdownOpen : ''}`}>
                                        {/* @ts-ignore */}
                                        <CustomMenu
                                            control={control}
                                            name="paymentMethod"
                                            items={paymentMethodOptions}
                                            placeholder="Choose method of payment"
                                            className={styles.paymentMethodSelect}
                                            onChange={handlePaymentMethodChange}
                                            onOpen={() => setMenuOpen(true)}
                                            onClose={() => setMenuOpen(false)}
                                            MenuProps={{
                                                TransitionComponent: Fade,
                                                disablePortal: true,

                                                classes: {
                                                    paper: `${styles.dropdownList} ${styles.menuSlideUp}`,
                                                    list: styles.muiMenuList,
                                                },
                                                anchorOrigin: {
                                                    vertical: 'bottom',
                                                    horizontal: 'left',
                                                },
                                                transformOrigin: {
                                                    vertical: 'top',
                                                    horizontal: 'left',
                                                },
                                            }}
                                            IconComponent={DropdownIcon}
                                        />
                                        {errors.paymentMethod && <p className={styles.errorMessage}>{errors.paymentMethod.message}</p>}
                                    </div>
                                </div>
                            </div>

                            <div className={clsx(styles.stepItem, styles.stepItem1)}>
                                <div className={styles.stepHeader}>
                                    <span className={clsx(styles.stepNumber, step >= 2 && styles.activeStep)}>
                                        Step 2:
                                    </span>
                                </div>
                                <div className={styles.stepContent}>

                                    <div className={styles.stepHeaderText}>
                                        <span>Enter payment details</span>
                                    </div>
                                    <div>
                                        {paymentMethod === CREDIT_CARD ? (
                                            <div className={styles.paymentDetailsContainer}>
                                                <div className={styles.paymentDetails}>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Cardholder First Name"
                                                            className={styles.formField}
                                                            register={register('cardholderFirstName')}
                                                            errorInput={errors.cardholderFirstName?.message}
                                                            aria-label="Cardholder First Name"
                                                        />
                                                    </InputWrapper>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Cardholder Last Name"
                                                            className={styles.formField}
                                                            register={register('cardholderLastName')}
                                                            errorInput={errors.cardholderLastName?.message}
                                                            aria-label="Cardholder Last Name"
                                                        />
                                                    </InputWrapper>
                                                </div>
                                                <div className={styles.paymentDetails}>
                                                    {/* <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Card Number"
                                                            className={styles.formField}
                                                            register={register('cardNumber')}
                                                            errorInput={errors.cardNumber?.message}
                                                            aria-label="Card Number"
                                                        />
                                                    </InputWrapper> */}
                                                    <div className={styles.stripeElement}>
                                                        <CardNumberElement options={{
                                                            style: stripeElementsStyle,
                                                            placeholder: watch('cardNumberLast4Digits') ? `**** **** **** ${watch('cardNumberLast4Digits')}` : 'Card Number'

                                                        }}
                                                        onChange={(e) => handleCardChange(e, 'cardNumber')}
                                                        />
                                                    </div>
                                                    <div className={styles.stripeElement}>
                                                        <CardExpiryElement options={{
                                                            style: stripeElementsStyle,
                                                            placeholder: watch('cardExpiry') ? `${watch('cardExpiry')}` : 'Expiration ( MM / YY )'
                                                        }}
                                                        onChange={(e) => handleCardChange(e, 'cardExpiry')}
                                                        />
                                                    </div>
                                                </div>
                                                <div className={styles.paymentDetails}>
                                                    <div className={styles.stripeElement}>
                                                        <CardCvcElement options={{
                                                            style: stripeElementsStyle,
                                                            placeholder: 'CVV'
                                                        }}
                                                        onChange={(e) => handleCardChange(e, 'cardCvc')}
                                                        />
                                                    </div>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            mode="wholeNumber"
                                                            placeholder="Billing Zip Code"
                                                            className={styles.formField}
                                                            register={register('billingZipCode')}
                                                            errorInput={errors.billingZipCode?.message}
                                                            aria-label="Billing Zip Code"
                                                            maxLength={5}
                                                        />
                                                    </InputWrapper>
                                                </div>
                                            </div>
                                        ) : paymentMethod === ACH ? (
                                            <div className={styles.paymentDetailsContainer}>
                                                <div className={styles.paymentDetails}>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Name of Account"
                                                            className={styles.formField}
                                                            register={register('accountName')}
                                                            errorInput={errors.accountName?.message}
                                                            aria-label="Name of Account"
                                                        />
                                                    </InputWrapper>
                                                    <div className={styles.accountTypeDropdown}>
                                                        <div className={clsx(accountTypeOpen ? styles.dropAccountTypeOpen : '')}>
                                                        <CustomMenu
                                                            control={control}
                                                            name="accountType"
                                                            items={accountType}
                                                            placeholder="Account Type"
                                                            className={styles.paymentMethodSelect}
                                                            // onChange={handlePaymentMethodChange}
                                                            onOpen={() => setAccountTypeOpen(true)}
                                                            onClose={() => setAccountTypeOpen(false)}
                                                            onFocus={() => setAccountTypeOpen(true)}
                                                            onBlur={() => setAccountTypeOpen(false)}
                                                            MenuProps={{
                                                                TransitionComponent: Fade,
                                                                disablePortal: true,

                                                                classes: {
                                                                    paper: `${styles.dropdownList} ${styles.dropListAccountType} ${styles.menuSlideUp}`,
                                                                    list: styles.muiMenuList,
                                                                },
                                                                anchorOrigin: {
                                                                    vertical: 'bottom',
                                                                    horizontal: 'left',
                                                                },
                                                                transformOrigin: {
                                                                    vertical: 'top',
                                                                    horizontal: 'left',
                                                                },
                                                            }}
                                                            IconComponent={DropdownIcon}
                                                        />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={styles.paymentDetails}>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Routing Number (ABA)"
                                                            className={styles.formField}
                                                            register={{
                                                                ...register('routingNumber'),
                                                                onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('routingNumber', e.target.value)
                                                            }}
                                                            errorInput={errors.routingNumber?.message}
                                                            aria-label="Routing Number (ABA)"
                                                            maxLength={9}
                                                        />
                                                    </InputWrapper>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Bank / Financial Institution"
                                                            className={styles.formField}
                                                            register={register('bankName')}
                                                            errorInput={errors.bankName?.message}
                                                            aria-label="Bank / Financial Institution"
                                                        />
                                                    </InputWrapper>
                                                </div>
                                                <div className={styles.paymentDetails}>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder= {watch('last4AccountNumber') ? `****${watch('last4AccountNumber')}` : "Account Number"}
                                                            className={styles.formField}
                                                            register={{
                                                                ...register('accountNumber'),
                                                                onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('accountNumber', e.target.value)
                                                            }}
                                                            errorInput={errors.accountNumber?.message}
                                                            aria-label="Account Number"
                                                            maxLength={17}
                                                        />
                                                    </InputWrapper>
                                                    <InputWrapper>
                                                        <CustomTextField
                                                            type="text"
                                                            placeholder="Re-Enter Account Number"
                                                            className={styles.formField}
                                                            register={{
                                                                ...register('reEnterAccountNumber'),
                                                                onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleBankAccountInputChange('reEnterAccountNumber', e.target.value)
                                                            }}
                                                            errorInput={errors.reEnterAccountNumber?.message}
                                                            aria-label="Re-Enter Account Number"
                                                            maxLength={17}
                                                        />
                                                    </InputWrapper>
                                                </div>

                                                {/* Verify Bank Button */}
                                                {/* {<div className={styles.verifyBankButtonContainer}>
                                                    <button
                                                        type="button"
                                                        className={clsx(
                                                            styles.verifyBankButton,
                                                            !isAchFormValid && styles.disabled
                                                        )}
                                                        onClick={() => setShowAchDebitVerifyDialog(true)}
                                                        disabled={!isAchFormValid}
                                                    >
                                                        Verify Bank Account
                                                    </button>
                                                    <div className={styles.verifyBankInfo}>
                                                        {isAchFormValid
                                                            ? "Click to verify your bank account using micro-deposits"
                                                            : "Complete all bank account fields to enable verification"
                                                        }
                                                    </div>
                                                </div>} */}
                                            </div>
                                        ) : (
                                            // Show 6 empty blocks when no payment method is selected
                                            <div className={styles.paymentDetailsContainer}>
                                                <div className={styles.paymentDetails}>
                                                    <div className={styles.emptyBlock}></div>
                                                    <div className={styles.emptyBlock}></div>
                                                </div>
                                                <div className={styles.paymentDetails}>
                                                    <div className={styles.emptyBlock}></div>
                                                    <div className={styles.emptyBlock}></div>
                                                </div>
                                                <div className={styles.paymentDetails}>
                                                    <div className={styles.emptyBlock}></div>
                                                    <div className={styles.emptyBlock}></div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className={clsx(styles.stepItem, styles.stepItem1)}>
                                <div className={styles.stepHeader}>
                                    <div className={clsx(styles.stepNumber, step >= 3 && styles.activeStep)}>
                                        Step 3:
                                    </div>
                                </div>
                                <div className={styles.stepContent}>
                                    <div className={clsx(styles.stepHeaderText,styles.agreementTitleMain)}>
                                        <span>Authorization</span>
                                        <Tooltip title={agreementTitle()}
                                          arrow
                                          placement={'top-end'}
                                          disableInteractive
                                          TransitionComponent={Fade}
                                          TransitionProps={{ timeout: 200 }}
                                          classes={{
                                              tooltip: styles.agreementTitleTooltip,
                                          }}
                                          componentsProps={{
                                            arrow: {
                                              className: styles.tooltipArrow,
                                            },
                                          }}
                                        >
                                            <span onClick={(e) => { e.preventDefault(); }} className={styles.agreementLink}>Click to read Debit Authorization Agreement</span>
                                        </Tooltip>
                                    </div>
                                    <div className={styles.authorization}>
                                        <label className={styles.checkboxContainer}>
                                            <input
                                                type="checkbox"
                                                {...register('agreeToTerms')}
                                                onChange={handleAgreementChange}
                                                aria-label="Agreement checkbox"
                                            />
                                           <span className={styles.checkmark}></span>
                                        </label>
                                        <label className={styles.lblChk}>
                                                By proceeding, you agree to the terms of the Debit Authorization Agreement.
                                            </label>
                                        {errors.agreeToTerms && <p className={styles.errorMessage}>{errors.agreeToTerms.message}</p>}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className={styles.updateAccount}>
                             {
                                userSubscription?.status === SUBSCRIPTION_STATUS.past_due && (
                                    <div className={styles.pastDueText}>
                                        <p>Your subscription is overdue. Please update your payment method to avoid service interruption.</p>
                                        <br/>
                                    </div>
                                )
                            }
                            <div className={styles.updateAccountText}>
                                <div className={styles.updateAccountPara}><span>Your next automatic payment is for ${watch('nextPaymentCyclePrice')} on {watch('nextPaymentCycleDay')}.</span></div>
                                <div className={styles.updateAccountPara}><span>{watch('bankNameOrCardBrand')} account ending in {watch('cardNumberLast4Digits') || watch('last4AccountNumber')}</span><button onClick={()=>switchUpdatePaymentModule(true)}><UpdatePaymentDetails/>Update Payment Details</button></div>

                            </div>
                            <div className={styles.updateAccountInputs}>
                                {emailList.map(({isEditing}, index) => {
                                    return (
                                        <div key={`user-email-${index}`} className={clsx(styles.inputWrapper)}>
                                            {(watch(`emailAddress.${index}.status`) === 'Pending' && !isEditing) ?
                                                <div className={styles.pendingEmailAddress} onClick={() => openEmailEditModal(index,true)}>
                                                    <div className={styles.pendingEmailAddressSection}>
                                                        <span>{watch(`emailAddress.${index}.email_id`)}</span>
                                                        <span className={styles.pendingEmailAddressText}>(Pending)</span>
                                                    </div>
                                                </div>
                                            :<InputWrapper>
                                                <CustomTextField
                                                type="text"
                                                placeholder={`User ${index+1} Email Address`}
                                                className={styles.formField}
                                                register={register(`emailAddress.${index}.email_id`)}
                                                errorInput={!!errors?.emailAddress?.[index] || !!errors?.emailAddressDomain?.[index]}
                                                aria-label={`User ${index} Email Address`}
                                                onChange={()=>{
                                                    clearErrors(`emailAddress.${index}`);
                                                    clearErrors(`emailAddressDomain.${index}`);
                                                }}
                                                onBlur={() => {if(watch(`emailAddress.${index}.status`) === 'Pending')openEmailEditModal(index,false)}}
                                                autoFocus={watch(`emailAddress.${index}.status`) === 'Pending' && isEditing}
                                            />
                                            </InputWrapper>
                                }
                                            {!!(errors.emailAddressDomain && errors.emailAddressDomain[index]) && <span className={styles.errorMessageDomain}>{errors.emailAddressDomain[index]?.message}</span>}
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {!showUpdateAccount ? (
                <div
                    className={clsx(styles.subscribeButton, !enableSubmitButton && styles.disabled)}
                    onClick={ ()=> enableSubmitButton ? onSubmit(watch()) : undefined}
                    aria-disabled={!enableSubmitButton}
                >
                   {paymentMethod === ACH ? <span>VERIFY & SUBSCRIBE</span> : <span>SUBSCRIBE</span>}
                </div>
            ) : (
                <div
                    className={clsx(styles.subscribeButton, isUpdateAccountDisabled && styles.disabled)}
                    onClick={()=>{
                        if(!isUpdateAccountDisabled){
                            handleUpdateAccount();
                        }
                    }}
                    aria-disabled={isUpdateAccountDisabled}
                >
                   <span>UPDATE ACCOUNT</span>
                </div>
            )}
            {/* ACH Verification Dialog */}
            <AchVerificationDialog
                open={showAchDebitVerifyDialog}
                onClose={() => setShowAchDebitVerifyDialog(false)}
                onVerify={handleAchVerification}
            />
        </div>
    );
};


const SubscribePage = () => {
    const [stripePromise] = useState(loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY));
    return (
        <Elements stripe={stripePromise} options={options}>
            <Subscribe />
        </Elements>
    );
};
export default SubscribePage;