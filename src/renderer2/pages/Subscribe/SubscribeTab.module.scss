.subscribeTab{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .grid{
        display: flex;
        gap:25px;
        .equalSpacing {
            flex: 1;
        }
    }
    .subscribeTabHeader{
        margin-top: 24px;
        width: 100%;
        height: 75px;
        // background-color: #fff;

        .licenseDetail{
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 13px;
            background-color: rgba(50, 204, 255, 0.04);
            .licenseDetailText{
                font-family: Syncopate;
                font-size: 18px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: 0.72px;
                text-align: center;
                color: #32ccff;
            }
        }

    }
    .subscriptionActions{
        margin-top: 24px;
        width: 100%;
        height: 36px;
        .actionbarTitle{
            .title{
                font-family: Syncopate;
                font-size: 18px;
                font-weight: bold;    
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.72px;
                color: #fff;  
            }
            .description{
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: normal;
                text-align: left;
                color: #9b9eac;
            }
        }
        .actionbarButtons{
            display: flex;
            flex-direction: row-reverse;
            gap: 8px;
            .actionbarButton{
                padding: 8px 14px;
                border-radius: 500px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: center;
                color: #71737f;
                &:hover{
                    color: #fff;
                }
            }
        }
    }
    .subscribeUserTable{
        margin-top: 24px;
        width: 100%;
        flex: 1;
        
    }
}