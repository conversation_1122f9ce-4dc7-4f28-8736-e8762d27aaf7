.tableContainer {
  width: 100%;
  background-color: #2a2b32;
  border-radius: 8px;
  overflow: hidden;
  font-family: Inter, sans-serif;
}

.userTable {
  width: 100%;
  border-collapse: collapse;
  background-color: #2a2b32;
  color: #ffffff;
}

.tableHeader {
  background-color: #1f2025;
  height: 60px;
  
  th {
    padding: 16px 20px;
    text-align: left;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    color: #ffffff;
    border-bottom: 1px solid #404149;
    
    &:first-child {
      padding-left: 24px;
    }
    
    &:last-child {
      padding-right: 24px;
    }
  }
}

.tableRow {
  background-color: #2a2b32;
  border-bottom: 1px solid #404149;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #323339;
  }

  &:last-child {
    border-bottom: none;
  }

  // Placeholder row styling - grey and less opacity
  &.placeholderRow {
    opacity: 0.6;
    background-color: #25262c;

    &:hover {
      background-color: #2a2b31;
    }
  }
  }
  
  .placeholderRow {
    background-color: rgba(42, 43, 50, 0.6);
    opacity: 0.7;
    
    &:hover {
      background-color: rgba(50, 51, 57, 0.7);
    }
    
    .tableCell {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .editInput {
      background-color: rgba(64, 65, 73, 0.6);
      border-color: rgba(90, 93, 107, 0.6);
      color: rgba(255, 255, 255, 0.8);
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }
      
      &:focus {
        background-color: rgba(74, 77, 90, 0.7);
        border-color: rgba(99, 102, 241, 0.7);
        color: rgba(255, 255, 255, 0.9);
      }
    }
    
    .dropdown {
      background-color: rgba(64, 65, 73, 0.6);
      border-color: rgba(90, 93, 107, 0.6);
      color: rgba(255, 255, 255, 0.6);
      
      &:hover {
        background-color: rgba(74, 77, 90, 0.7);
      }
    }
    
    .status {
      background-color: rgba(59, 130, 246, 0.05);
      color: rgba(96, 165, 250, 0.6);
      border-color: rgba(59, 130, 246, 0.2);
    }
  }



.tableCell {
  padding: 16px 20px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  vertical-align: middle;
  
  &:first-child {
    padding-left: 24px;
  }
  
  &:last-child {
    padding-right: 24px;
  }
}

.editableCell {
  cursor: pointer;
  display: inline-block;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #404149;
  }
}

.editInput {
  background-color: #404149;
  border: 2px solid #5a5d6b;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  font-family: Inter, sans-serif;
  padding: 6px 10px;
  width: 100%;
  outline: none;
  transition: border-color 0.2s ease, background-color 0.2s ease, opacity 0.2s ease;

  &:focus {
    border-color: #6366f1;
    background-color: #4a4d5a;
  }

  // Placeholder input styling
  &.placeholder {
    background-color: #35363c;
    border-color: #4a4b52;
    color: rgba(255, 255, 255, 0.7);

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      border-color: #5a5d6b;
      background-color: #404149;
    }
  }

  // Disabled input styling
  &.disabled {
    background-color: #2a2b32;
    border-color: #35363c;
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    opacity: 0.6;

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }

    &:focus {
      border-color: #35363c;
      background-color: #2a2b32;
    }
  }
  
  &.error {
    border-color: #dc2626;
    background-color: rgba(220, 38, 38, 0.1);
  }
  
  &.disabled {
    background-color: rgba(64, 65, 73, 0.4);
    border-color: rgba(90, 93, 107, 0.4);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    
    &::placeholder {
      color: rgba(255, 255, 255, 0.2);
    }
  }
}

.dropdown {
  background-color: #404149;
  border: 1px solid #5a5d6b;
  border-radius: 6px;
  color: #ffffff;
  font-size: 13px;
  font-family: Inter, sans-serif;
  padding: 8px 32px 8px 12px;
  cursor: pointer;
  outline: none;
  min-width: 120px;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  appearance: none;
  transition: background-color 0.2s ease, border-color 0.2s ease, opacity 0.2s ease;
  -webkit-appearance: none;
  -moz-appearance: none;
  
  &:hover {
    background-color: #4a4d5a;
    border-color: #6b6e7c;
  }
  
  &:focus {
    border-color: #6366f1;
    background-color: #4a4d5a;
  }
  
  option {
    background-color: #404149;
    color: #ffffff;
    padding: 8px 12px;
  }
  
  &.disabled {
    background-color: #2a2b32;
    border-color: #35363c;
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    opacity: 0.6;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");

    &:hover {
      background-color: #2a2b32;
      border-color: #35363c;
    }

    &:focus {
      background-color: #2a2b32;
      border-color: #35363c;
      border-color: rgba(90, 93, 107, 0.4);
    }
  }
}

.assigned {
  border-color: #059669;
  background-color: rgba(5, 150, 105, 0.1);
  
  &:hover {
    background-color: rgba(5, 150, 105, 0.15);
  }
}

.unassigned {
  border-color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
  
  &:hover {
    background-color: rgba(220, 38, 38, 0.15);
  }
}

.status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  
  // You can add different status colors based on status value
  background-color: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

// New styles for React Hook Form
.formContainer {
  width: 100%;
}

.cellContainer {
  position: relative;
  width: 100%;
}

.errorMessage {
  position: absolute;
  top: 100%;
  left: 0;
  color: #dc2626;
  font-size: 11px;
  font-weight: 500;
  margin-top: 2px;
  z-index: 10;
}

.saveButtonContainer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 0 24px;
}

.saveButton {
  background-color: #6366f1;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  font-family: Inter, sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  
  &:hover:not(.disabled) {
    background-color: #5855eb;
    transform: translateY(-1px);
  }
  
  &:active:not(.disabled) {
    transform: translateY(0);
  }
  
  &.disabled {
    background-color: #6b7280;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .tableContainer {
    overflow-x: auto;
  }
  
  .userTable {
    min-width: 800px;
  }
  
  .tableCell,
  .tableHeader th {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .dropdown {
    min-width: 100px;
    font-size: 12px;
    padding: 6px 28px 6px 10px;
  }
}

@media (max-width: 768px) {
  .tableHeader th {
    font-size: 11px;
    padding: 10px 12px;
  }
  
  .tableCell {
    padding: 10px 12px;
    font-size: 12px;
  }
  
  .dropdown {
    min-width: 90px;
    font-size: 11px;
  }
} 