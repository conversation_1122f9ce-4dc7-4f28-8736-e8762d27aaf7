import React, { useState, useEffect } from 'react'
import styles from './NextPaymentDetail.module.scss'
import { useSubscriptionStore } from 'src/renderer2/store/SubscriptionStore';
import { calculateSubscriptionAmount } from 'src/renderer2/helper';

interface PaymentDetails {
  amount: number;
  currency: string;
  paymentDate: string;
  cardType: string;
  lastFourDigits: string;
}

interface NextPaymentDetailProps {
  initialPaymentDetails?: Partial<PaymentDetails>;
  onPaymentDetailsChange?: (details: PaymentDetails) => void;
}

const NextPaymentDetail: React.FC<NextPaymentDetailProps> = ({ 
  initialPaymentDetails,
  onPaymentDetailsChange
}) => {
  const { userSubscription , subscriptionsPricing } = useSubscriptionStore();
  const [paymentDetails, setPaymentDetails] = useState<any>({
    nextPaymentCyclePrice: 0,
    nextPaymentCycleDay: 0,
    bankNameOrCardBrand: '',
    cardNumberLast4Digits: '',
    last4AccountNumber: '',
  });
  useEffect(() => {
    if(userSubscription?.id && subscriptionsPricing){
      console.log("userSubscription", userSubscription)
      const pricingData = calculateSubscriptionAmount(Number(userSubscription?.total_license), subscriptionsPricing);
      const nextPaymentCyclePrice = Number(pricingData?.subscription_amount)* Number(userSubscription?.total_license);
      setPaymentDetails({
        nextPaymentCyclePrice: nextPaymentCyclePrice,
        nextPaymentCycleDay: userSubscription?.next_payment_cycle_day,
        bankNameOrCardBrand: userSubscription?.payment_details?.card_display_brand ?? userSubscription?.payment_details?.bank_name,
        cardNumberLast4Digits: userSubscription?.payment_details?.card_number_last_four_digits,
        last4AccountNumber: userSubscription?.payment_details?.account_number,
      })
    }else{
      console.log("userSubscription", userSubscription)
    }
  }, [userSubscription])

  console.log("paymentDetails", paymentDetails)
  return (
    <div className={styles.nextPaymentContainer}>
      <div className={styles.paymentInfo}>
        <p className={styles.paymentText}>
          Your next automatic payment is for{' '}
          <span className={styles.amount}>
            {paymentDetails.nextPaymentCyclePrice}
          </span>{' '}
          on {paymentDetails.nextPaymentCycleDay}.
        </p>
        <p className={styles.cardInfo}>
          Auto-Debit from {paymentDetails.bankNameOrCardBrand} ending in {paymentDetails.cardNumberLast4Digits}
        </p>
      </div>
    </div>
  )
}

export default NextPaymentDetail