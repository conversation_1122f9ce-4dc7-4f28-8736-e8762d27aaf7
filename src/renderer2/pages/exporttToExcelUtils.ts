import ExcelJS from 'exceljs';
//import { saveAs } from 'file-saver';

type CellStyle = {
  fill?: ExcelJS.Fill;
  font?: Partial<ExcelJS.Font>;
  border?: ExcelJS.Borders;
  b?: boolean;
  i?: boolean;
  u?: boolean;
  strike?: boolean;
  color?: { argb: string };
  backgroundColor?: string; // Custom field for shortcut
};

type CellData = {
  cellText: string;
  style?: CellStyle;
};

export type RowData = {
  row: CellData[];
  style?: CellStyle | { border: true };
};

function applyCustomSheetData(sheet: ExcelJS.Worksheet, data: RowData[]) {
  data.forEach((rowData, rowIndex) => {
    const row = sheet.addRow(rowData.row.map(cell => cell.cellText));
    rowData.row.forEach((cell, colIndex) => {
      const excelCell = row.getCell(colIndex + 1);

      // Merge row-level and cell-level styles
      const rowStyle: CellStyle = { ...(rowData.style || {}) };
      const cellStyle: CellStyle = { ...(rowStyle || {}), ...(cell.style || {}) };

      // Font merging logic
      const font: Partial<ExcelJS.Font> = {
        bold: cellStyle.b ?? rowStyle.b,
        italic: cellStyle.i ?? rowStyle.i,
        underline: cellStyle.u ?? rowStyle.u,
        strike: cellStyle.strike ?? rowStyle.strike,
        color: cellStyle.color ? { argb: cellStyle.color.argb } : undefined,
      };

      // Clean undefined font props
      if (Object.values(font).some(v => v !== undefined)) {
        excelCell.font = font;
      }

      // Fill (background color)
      if (cellStyle.fill) {
        excelCell.fill = cellStyle.fill;
      } else if (cellStyle.backgroundColor) {
        excelCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: cellStyle.backgroundColor }
        };
      }

      // Border
      if (cellStyle.border) {
        applyBorder(excelCell, cellStyle.border);
      }else if (rowStyle.border) {
        applyBorder(excelCell, rowStyle.border);
      }
    });
  });

    // Optional: Adjust column widths
    sheet.columns.forEach(col => {
        let maxLength = 10;
        col.eachCell?.(cell => {
        const len = cell.value?.toString().length ?? 0;
        if (len > maxLength) maxLength = len;
        });
        col.width = maxLength + 2;
    });
}

const applyBorder = (excelCell: ExcelJS.Cell, borderStyle: any) => {
    const defaultBorder: ExcelJS.Borders = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    right: { style: 'thin' },
    bottom: { style: 'thin' },
  };
    if(borderStyle === true){
        excelCell.border = defaultBorder;
    }else if (borderStyle) {
        excelCell.border = borderStyle;
    }
}


export const createExcelBlob = async (data: RowData[]) => {
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet('Custom');

    // Call the function
    applyCustomSheetData(sheet, data);

    // Then save the workbook
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    return blob;
    //saveAs(blob, `${sanitizeFilename(filename)}.xlsx`);
}