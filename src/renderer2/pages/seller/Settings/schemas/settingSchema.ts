import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const settingSchema = yup.object().shape({
  // Placeholder for User tab fields
  userType: yup.string(),
  firstName: yup.string(),
  email: yup.string().email('Invalid email format').test('is-email', 'Invalid email format', function (value) {
    if (!value) return true;
    return isEmail(value);
  }),
  phoneNumber: yup.string().test('phone-digits', 'Phone number must have at least 10 digits', function(value) {
    if (!value) return true; // Let required validation handle empty values
    const digitCount = (value.match(/\d/g) || []).length;
    return digitCount >= 10;
  }).required('Phone number is required'),
  parentCompanyName: yup.string(),
  companyDBAName: yup.string(),
  companyAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function (value) {
    const { line1, city, state, zip } = value || {};

    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;

    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }

    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state && zip;

    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }

    return true;
  }),
  sellerAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function (value) {
    const { line1, city, state, zip } = value || {};

    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;

    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }

    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state && zip;

    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }

    return true;
  }),

  billingContactName: yup.string(),
  billingContactEmail: yup.string().email('Invalid email format').test('is-email', 'Invalid email format', function (value) {
    if (!value) return true;
    return isEmail(value);
  }),

  stockingAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function (value) {
    const { line1, line2, city, state, zip } = value || {};

    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;

    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }

    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state && zip;

    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }

    return true;
  }),

  newCompanyAddress: yup.object().shape({
    line1: yup.string(),
    line2: yup.string(),
    city: yup.string(),
    state: yup.string(),
    stateCode: yup.string(),
    zip: yup.string()
  }).test('all-or-none', 'If any address field is filled, all fields are required', function (value) {
    const { line1, city, state, zip } = value || {};

    // Check if any field is filled
    const hasAnyValue = line1 || city || state || zip;

    // If no fields are filled, that's valid (optional)
    if (!hasAnyValue) {
      return true;
    }

    // If any field is filled, all must be filled
    const allFieldsFilled = line1 && city && state && zip;

    if (!allFieldsFilled) {
      // Return specific error for missing fields
      if (!line1) return this.createError({ path: `${this.path}.line1`, message: 'Line 1 is required when any address field is filled' });
      if (!city) return this.createError({ path: `${this.path}.city`, message: 'City is required when any address field is filled' });
      if (!state) return this.createError({ path: `${this.path}.state`, message: 'State is required when any address field is filled' });
      if (!zip) return this.createError({ path: `${this.path}.zip`, message: 'Zip code is required when any address field is filled' });
    }

    return true;
  }),
  sendInvoicesTo: yup.string(),
  sendOrderDocsTo: yup.string(),
  
  orderFulfillmentStates: yup.array().of(yup.string()).default([]),
  orderClaimPreferences: yup.boolean().default(false),

  achCheckBox: yup.boolean(),
  bankName1: yup.string().trim().test("isRequired", "ACH Credit is not valid", function(value){
    const achCheckBox = this.parent.achCheckBox;
    if(achCheckBox === false) return true;
    return !!value;
  }),
  routingNo: yup.string().test("isRequired", "ACH Credit is not valid", function(value){
    
    const achCheckBox = this.parent.achCheckBox;
    if(achCheckBox === false) return true;
    if(value && value.includes("x")){
      return true;
    }
      if(!/^x{5}\d{4}$|^\d{9}$/.test(value)){
      return false
    }
    return !!value;
  }),
  accountNo: yup.string().test("isRequired", "ACH Credit is not valid", function(value){
    const achCheckBox = this.parent.achCheckBox;
    if(achCheckBox === false) return true;
    if(!/^x+\d{4}$|^\d+$/.test(value)){
      return false
    }
    return !!value;
  }),
  remittanceEmail: yup.string().email('Invalid email format').test('is-email', 'Invalid email format', function (value) {
    if (!value) return true;
    return isEmail(value);
  }),
});

export type SettingSchema = yup.InferType<typeof settingSchema>; 