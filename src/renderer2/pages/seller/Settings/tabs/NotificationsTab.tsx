import React from 'react';
import styles from './TabContent.module.scss';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { notificationsSchema, NOTIFICATION_CATEGORIES, NotificationsFormData } from '../schemas/notificationsSchema';
import clsx from 'clsx';
import { Tooltip, Fade } from '@mui/material';

// Custom notification checkbox component
const NotificationCheckbox: React.FC<{
  type: 'text' | 'email' | 'desktop';
  name: string;
  control: any;
  defaultChecked?: boolean;
}> = ({ type, name, control, defaultChecked = false }) => {
  const label = type === 'text' ? 'T' : type === 'email' ? 'E' : 'D';
  
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultChecked}
      render={({ field: { onChange, value } }) => (
        <div 
          className={clsx(
            styles.notificationCheckbox, 
            {
              [styles.textType]: type === 'text',
              [styles.emailType]: type === 'email',
              [styles.desktopType]: type === 'desktop',
              [styles.checked]: value
            }
          )}
          onClick={() => onChange(!value)}
        >
          {label}
        </div>
      )}
    />
  );
};

const NotificationsTab: React.FC = () => {
  const { control, watch } = useForm<NotificationsFormData>({
    resolver: yupResolver(notificationsSchema),
    mode: "onBlur"
  });

  console.log("watch", watch());

  return (
    <div className={styles.tabContent}>
      <div className={styles.notificationsFormContainer}>
        {/* Header section */}
        <div className={styles.toggleHeader}>
          <div className={styles.toggleHeaderLabel}>
            Some notifications are required and unable to opt-out. Select the notifications you would
            like to receive and how you would like receive them: by
            <span className={styles.methodText}> Text <span className={styles.methodIcon}>T</span></span>, 
            <span className={styles.methodText}> Email <span className={styles.methodIcon}>E</span></span> or 
            <span className={styles.methodText}> Desktop <span className={styles.methodIcon}>D</span></span>.
          </div>
          <div className={styles.toggleHeaderIcons}>
            <div className={styles.toggleIcon}>T</div>
            <div className={styles.toggleIcon}>E</div>
            <div className={styles.toggleIcon}>D</div>
          </div>
        </div>

        {/* Scrollable notification section */}
        <div className={styles.notificationDisplayContainer}>
          <div className={styles.notificationsSection}>
            {/* Map through notification categories */}
            {NOTIFICATION_CATEGORIES.map(category => (
              <div key={category.id} className={styles.notificationCategory}>
                {/* Category header */}
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryTitle}>
                    {category.title}
                  </div>
                </div>
                
                {/* Map through notification items in this category */}
                {category.items.map(item => (
                  <div key={item.id} className={styles.notificationRow}>
                    <div className={styles.notificationTitle}>
                      <Tooltip
                        title={item.tooltipText}
                        placement={"top"}
                        disableInteractive
                        TransitionComponent={Fade}
                        TransitionProps={{ timeout: 100 }}
                        classes={{
                          tooltip: "inputQtyTooltip",
                        }}
                      >
                        <span>{item.title}</span>
                      </Tooltip>
                    </div>
                    
                    {/* Notification type checkboxes */}
                    <div className={styles.notificationToggle}>
                      <NotificationCheckbox
                        type="text"
                        name={`textNotifications.${item.id}`}
                        control={control}
                        defaultChecked={item.defaultValues.text}
                      />
                    </div>
                    <div className={styles.notificationToggle}>
                      <NotificationCheckbox
                        type="email"
                        name={`emailNotifications.${item.id}`}
                        control={control}
                        defaultChecked={item.defaultValues.email}
                      />
                    </div>
                    <div className={styles.notificationToggle}>
                      <NotificationCheckbox
                        type="desktop"
                        name={`desktopNotifications.${item.id}`}
                        control={control}
                        defaultChecked={item.defaultValues.desktop}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationsTab; 