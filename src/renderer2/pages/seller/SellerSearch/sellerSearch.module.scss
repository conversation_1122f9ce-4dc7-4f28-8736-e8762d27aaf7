 .searchSection {
    margin-top: 17px;
    display: flex;

    .zipBox {
      display: flex;
      flex-direction: column;
      width: 90px;
      height: 41px;
      margin: 0px 30px 0px 0px;
      padding:6px 16px 4.9px 16px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.04);
      transition: background-color 0.1s ease-in-out;
      flex: 0 0 auto;
      
      &:focus-within{
        background: url('../../assets/New-images/DestZIPActive.svg') no-repeat;
        background-size: cover;
      }

      &.searchZipValue{
          background: url('../../assets/New-images/DestZIPActive.svg') no-repeat;
          background-size: cover;
      }

      .destZIP {
        font-family: Syncopate;
        font-size: 11px;
        font-weight: normal;
        letter-spacing: -0.44px;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
      }

      .zipCode {
        display: flex;
        justify-content: center;
        width: 100%;
        line-height: 1;
        input {
          background-color: transparent;
          border: 0px;
          width: 100%;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: normal;
          text-align: center;
          padding: 0px 0px 0px 0px;
          background-image: linear-gradient(to right, #99762f -26%, #ffc44f 30%, #ffc44f 71%, #99762f 127%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          color: #ffc44f;
          letter-spacing: 2.4px;
          position: relative;
          left: 1px;
          &:focus {
            outline: none;
          }
        }

      }

      .zipInput {
        text-align: center;
        padding: 0;
        margin: 0;
        border: none;
        background: transparent;
      }

    }

    .searchBox {
      width: 100%;
      height: 41px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 16px;
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.04);
      transition: background-color 0.1s ease-in-out;
      z-index: 2;
    
      &:focus-within {
        background: url('../../../assets/New-images/SearchInputActive.svg') no-repeat bottom;
        background-size: cover;
      }
    
      input {
        background-color: transparent;
        border: none;
        width: 100%;
        height: 100%;
        padding: 6px 12px 6px 0;
        font-family: Inter;
        font-size: 15px;
        font-weight: normal;
        letter-spacing: 0.6px;
        color: #1fbbfe;
        transition: all 0.1s ease-in-out;
    
        &::placeholder {
          color: #616575;
        }
    
        &:focus {
          outline: none;
        }
      }
    
      svg {
        margin-left: 18px;
      }
    }
    
  }