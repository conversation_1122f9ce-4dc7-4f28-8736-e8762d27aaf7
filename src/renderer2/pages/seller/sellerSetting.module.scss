.buyerSettingInnerContent {
    padding: 12px 16px;
    border-radius: 0px 0px 10px 10px;
    margin: 0px auto;
    max-width: 800px;
    width: 100%;
    background-image: linear-gradient(102deg, #0f0f14 -8%, #393e47 238%);
    position: relative;

    .dFlex {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .impersonateIconStyle{
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 24px;
            color: #70ff00;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 18px;
            cursor: pointer;
            font-size: 14px;
        }
    }

    .generalSettings {
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        margin-bottom: 12px;
        display: block;
    }

    .errorMessage {
        padding: 6px 20px;
        border-radius: 50px;
        border: solid 1px #ff5d47;
        background-color: #ffefed;
        font-family: Noto Sans;
        font-size: 14px;
        line-height: 1.6;
        text-align: left;
        color: #ff5d47;
        display: flex;
        gap: 4px;
    }

    .fundGeneralSettings {
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        margin-bottom: 12px;
    }

    .formInnerContent {
        .FormInputGroup {
            display: flex;
            margin-bottom: 8px;

            .lblInput {
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: normal;
                line-height: 1.6;
                text-align: left;
                color: #fff;
                width: 152px;
                height: 34px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                padding: 6px 4px 6px 10px;
                border: solid 0.5px #000;
                background-color: rgba(0, 0, 0, 0.25);
                border-radius: 4px 0px 0px 4px;
                border-right: 0px;

                .lblCheckbox {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    white-space: nowrap;
                    padding-left: 20px;
                    &:focus-within{
                        .checkmark{
                            border: 1px solid #70ff00;
                        }
                        input:checked~.checkmark {
                            border: solid 0.7px #fff;
                        }
                    }

                    .checkmark {
                        width: 12px;
                        height: 12px;
                        border-radius: 1.7px;
                        border: solid 0.7px #3b4665;
                        background-color: #ebedf0;
                        top:5px;

                        &::after {
                            left: 3.5px;
                            top: 1.5px;
                            width: 2px;
                            height: 5px;
                        }
                    }

                    input:checked~.checkmark {
                        background-color: #70ff00;
                        border: solid 0.7px #70ff00;
                    }

                    span {
                        margin-left: 6px;
                    }
                }
            }

            .lblAdress {
                flex: 0 0 152px;
            }

            .inputSection {
                height: 34px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: flex-start;
                padding: 6px 6px 6px 10px;
                border: solid 0.5px #000;
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 0px 4px 4px 0px;
                flex: 1 auto;

                &.comanyName.comanyName{
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: left;
                    color: #fff;
                    padding: 0px;
                    .comanyNameInput1.comanyNameInput1{
                        padding: 6px 6px 6px 10px;
                      }
                  }

                  .changePassBtn{
                    display: flex;
                    align-items: center;
                    height: 100%;
                    padding: 6px 6px 6px 10px;
                    cursor: pointer;
                    &.disabled {
                        cursor: not-allowed;
                        opacity: 0.5;
                        pointer-events: auto;
                    }
                  }

                &.companyState{
                    flex:0 0 72px
                }

                &.phoneNo {
                    width: 115px;
                }

                &.bdrRadius0 {
                    border-radius: 0px;
                }

                &.bdrRight0 {
                    border-right: 0px;
                }


                input {
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: left;
                    color: #fff;
                    border: 0px;
                    background-color: transparent;
                    padding: 0px;
                    width: 100%;

                    &:focus {
                        outline: none;
                        box-shadow: none;
                    }

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.5);
                        font-weight: normal;
                    }
                }
            }

            &.FormInputGroupError {
                .lblInput {
                    border: solid 0.5px #f00;
                    background-color: #f00;
                    cursor: pointer;
                    white-space: nowrap;
                }

                .borderOfError {
                    border: solid 0.5px #f00;
                }
            }

            .cityInput {
                flex: 0 0 96px;
            }

            .zipCodeInput {
                flex: 0 0 72px;
                padding: 6px 3px 6px 6px;

            }

            &.UploadSection {

                .inputSection2 {
                    height: 34px;
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: flex-start;
                    padding: 6px 6px 6px 10px;
                    border: solid 0.5px #000;
                    background-color: rgba(255, 255, 255, 0.2);
                    border-radius: 0px 4px 4px 0px;
                    flex: 1 1 auto;

                    label {
                        width: 100%;
                        white-space: nowrap;
                    }

                    .uploadText {
                        font-family: Noto Sans;
                        font-size: 12px;
                        font-weight: 300;
                        line-height: 1.6;
                        text-align: center;
                        color: #fff;
                        margin-bottom: 3px;
                        display: flex;
                        width: 100%;
                        cursor: pointer;

                        .uploadIcon {
                            display: flex;
                            width: 100%;
                            justify-content: right;
                        }
                        &:hover,
                        &:focus {
                            color: #70ff00;
                        }
                    }

                    input {
                        display: none;
                    }
                }
            }

            &.netTerms.netTerms {
                .lblInput {
                    flex: 0 0 152px;
                }

                .inputSection {
                    input {
                        font-size: 12px;
                    }

                    &:nth-child(4) {
                        flex: 0 0 144px;
                    }
                }
            }

            &.achCredit {
                height: 40px;
                margin-bottom: 12px;

                .lblInput {
                    height: 40px;
                    flex: 0 0 152px;
                }

                .inputSection {
                    height: 40px;
                    flex-direction: column;

                    label {
                        font-family: Noto Sans;
                        font-size: 10px;
                        font-weight: 300;
                        line-height: 1.2;
                        text-align: left;
                        color: #fff;
                    }

                    input {
                        font-size: 12px;
                    }
                }
            }

            .btnApplyMain {
                padding: 0px;
                background-color: transparent;

                .applyBtn {
                    width: 80px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 3px 10px;
                    border: solid 0.5px #000;
                    background-color: rgba(0, 0, 0, 0.25);
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 1.6;
                    text-align: left;
                    color: #fff;
                    border-radius: 0px 4px 4px 0px;
                    border: 0px;
                    cursor: pointer;
                }
            }

            .pendingBtn {
                flex: 0 0 auto;

                .applyBtn {
                    color: #ff5d47;
                }
            }

        }

        .myReportsTitle {
            font-family: Noto Sans;
            font-size: 20px;
            font-weight: bold;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            margin-top: 8px;
            margin-bottom: 8px;

            span {
                font-size: 12px;
                font-weight: normal;
            }
        }

        .purchaseOrderHistoryText {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            margin-bottom: 4px;
            display: block;
            &:hover,
            &:focus {
                color: #70ff00;
            }
        }


    }

    .btnSection {
        display: grid;
        grid: 20% auto 20%;
        grid-template-columns: 20% 60% 20%;
        border-top: 1px solid #000;
        align-items: center;
        padding-top: 8px;
        margin-top: 19px;

        .termsAndPatent {
            position: relative;
            .TermsandConditions {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.6;
                text-align: center;
                color: #fff;
                cursor: pointer;
            }
    
            .patentPendingText {
                font-family: Noto Sans;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.6;
                text-align: center;
                color: #fff;
            }
            .version{
                color: #70ff00;
            }
        }

        .backBtn {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            border: 0px;
            outline: none;
            background-color: transparent;
            text-align: left;
            cursor: pointer;
            &:not(:disabled){
                &:hover,
                &:focus {
                    color: #b3b3b3;
                }
            }
        }

        .saveBtn {
            margin-left: auto;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }

        .btnRight {
            text-align: right;
        }
    }
}

.Dropdownpaper.Dropdownpaper {
    padding: 3px 4px 8px 8px;
    -webkit-backdrop-filter: blur(30px);
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(255, 255, 255, 0.3);
    margin-top: 7px;
    overflow: hidden;
    width: 72px;
    border-radius: 0px 0px 4px 4px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    ul {
        overflow: auto;
        max-height: 230px;
        padding-right: 4px;
        padding-top: 0px;
        padding-bottom: 0px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        li {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            border-radius: 2px;
            padding: 3px 5px;
            margin-bottom: 2px;

            &:hover {
                background-color: #fff;
                color: #000;
            }

            &[aria-selected="true"]{
                background-color: #fff;
                color: #000;
                border-radius: 2px;
            }
        }
    }
}

.DropdownUploadCertpaper.DropdownUploadCertpaper {
    padding: 3px 4px 8px 12px;
    backdrop-filter: blur(24px);
    background-color: #ffffff4c;
    box-shadow: 0px 8px 30px #000000cc;
    margin-top: 1px;
    overflow: hidden;
    border-radius: 2px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    ul {
        overflow: auto;
        max-height: 260px;
        padding-right: 8px;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        li {
            font-family: Roboto;
            font-size: 11px;
            font-weight: normal;
            line-height: 1.57;
            text-align: left;
            color: #fff;
            border-radius: 2px;
            padding: 2px 6px;

            &:hover {
                font-family: Roboto;
                background-color: #fff;
                color: #000;
            }

            &:nth-child(1) {
                background-color: transparent;
                font-size: 11px;
                font-weight: normal;
                line-height: 1.57;
                color: #fff;
                opacity: unset;
                padding: 2px 0px 3px 0px;

                &:hover {
                    background-color: transparent;
                    color: #fff;
                }
            }

        }
    }
}

.SubmitApp {
    .dialogContent {
        max-width: 349px;
        width: 100%;
        height: 414px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 40px 22px 12px 22px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }

        .hereLink {
            font-weight: bold;
            color: #70ff00;
            cursor: pointer;
        }

        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 40px;
            transition: all 0.1s;

            &:hover {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }
        }


    }

}

.successPopup {
    .dialogContent {
        height: 479px;

        .successPopupTitle {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: normal;
            line-height: 1.6;
            text-align: center;
            color: #70ff00;
        }
    }

}

.questionIcon{
    display: flex;
    align-items: center;
    margin-left: auto;
    transition: all 0.1s;
    .questionIcon2{
      display: none;
    }
    &:hover{
        .questionIcon1{
          display: none;
        }
        .questionIcon2{
          display: block;
        }
    }  
}

.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.viewBtn {
    text-decoration: none;
    padding-right: 4px;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.6;
    color: #fff;
    &:hover,
    &:focus{
        color: #70ff00;
        outline: none;
    }
}
.orText {
    padding-right: 4px;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.6;
    color: #fff;
}
.backToHomePopup {
    .dialogContent {
        height: 234px;

        .bactToHomeTitle {
            font-family: Noto Sans;
            font-size: 20px;
            font-weight: 600;
            line-height: 1.6;
            text-align: center;
            color: #fff;
        }

        .closeIcon {
            position: absolute;
            top: 10px;
            right: 12px;
            cursor: pointer;
            &:focus{
                svg{
                    opacity: unset;
                }
            }

            svg {
                height: 25px;
                width: 25px;
                opacity: 0.5;

                path {
                    fill: #fff;
                }
                &:hover{
                    opacity: unset;
                }
            }
        }

        .btnOfPopup {
            opacity: 0.5;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            margin-bottom: 15px;
            font-weight: normal;
            cursor: pointer;

            &:hover,
            &:focus {
                opacity: unset;
                color: #70ff00;
            }
        }
    }
}
.ErrorDialog {
    .dialogContent {
      max-width: 300px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding: 30px 34px 30px 34px;
      object-fit: contain;
      border-radius: 10px;
      -webkit-backdrop-filter: blur(24px);
      backdrop-filter: blur(24px);
      box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
      background-color: rgba(0, 0, 0, 0.72);
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #fff;
  
      p {
        margin-bottom: 20px;
      }
  
  
      .submitBtn {
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 10px 24px;
        border-radius: 4px;
        border: solid 0.5px #fff;
        background-color: transparent;
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        margin-top: 20px;
        transition: all 0.1s;
  
        &:hover {
          background-color: #70ff00;
          border: solid 0.5px #70ff00;
          color: #000;
        }
  
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
  
          &:hover {
            border: solid 0.5px #fff;
            background-color: transparent;
            color: #fff;
          }
        }
      }
  
  
    }
  
  }
  .errorMesDiv{
    display: flex;
    align-items: center;
    position: absolute;
    width: 100%;
    justify-content: center;
    top: 7px;
}


.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    background-color: #ffffff4c;
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    padding-right: 4px;
    background: url(../../assets/images/DropDownBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 0px 0px 4px 4px;
    margin-top: 1px;
  }
  
  .listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    max-height: 316px;
    padding: 6px 4px 6px 10px;
    margin-top: 4px;
  
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  
    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 20px;
  
    }
  
    li {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      box-shadow: none;
      padding: 6px 8px;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 4px;
      border-radius: 2px;
  
      &:hover {
        background-color: #fff;
        color: #000;
      }

      &[aria-selected="true"] {
        background-color: #fff !important;
        color: #000;
      }
    }
  }

  .companyInput {
    width: 100%;
    height: 100%;
    padding: 6px 6px 6px 10px;
    color: #fff;
    resize: none;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;

    &::placeholder {
      color: #bbb;
    }

    &:focus-within {
      border: solid 1px #70ff00;
      outline: none;
      box-shadow: none;
      border-radius: 0px 2px 2px 0px;
    }
  }


  .changePassDialog{
    .dialogContent{
        width: 520px;
        height: auto;
        max-height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-end;
        object-fit: contain;
       border-radius: 20px;
        background: url(../../assets/New-images/Change-Pass-BG.svg) no-repeat;
        background-size: cover;
        .closeIcon {
            position: absolute;
            top: 10px;
            right: 12px;
            cursor: pointer;
            &:focus{
                svg{
                    opacity: unset;
                }
            }
        
            svg {
                height: 20px;
                width: 20px;
                opacity: 0.5;
        
                path {
                    fill: #fff;
                }
                &:hover{
                    opacity: unset;
                }
                
            }
        }
    }
}

.blurBg{
    filter: blur(4px);
}