#!/usr/bin/env sh

# Set the name of the keychain to be created
KEY_CHAIN=build.keychain

# Decode the base64-encoded app certificate and save it to a file
echo $APPLE_APP_CERTIFICATE_P12 | base64 --decode >app_certificate.p12

# Create a new keychain with a password of "simplable123"
security create-keychain -p simplable123 $KEY_CHAIN

# Set the newly-created keychain as the default keychain
security default-keychain -s $KEY_CHAIN

# Unlock the keychain using the password "simplable123"
security unlock-keychain -p simplable123 $KEY_CHAIN

# Import the app certificate into the keychain using the given password
security import app_certificate.p12 -k $KEY_CHAIN -P $APPLE_APP_CERTIFICATE_PASSWORD -T /usr/bin/codesign

# Set the key partition list to allow access to the app certificate
security set-key-partition-list -S apple-tool:,apple: -s -k simplable123 $KEY_CHAIN

# List the identities in the keychain for codesigning
security find-identity -p codesigning -v
