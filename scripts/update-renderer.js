const fs = require('fs');

const filePath = `${process.argv[3]}`;

fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
        console.error(`Error reading file: ${err}`);
        return;
    }

    // Create a regular expression to match and replace the line
    const regex = /^VITE_RENDERER_DEPLOY_VERSION=.*/m;
    const replacement = `VITE_RENDERER_DEPLOY_VERSION="${process.argv[2]}"`;
    const updatedData = data.replace(regex, replacement);

    fs.writeFile(filePath, updatedData, 'utf8', (err) => {
        if (err) {
            console.error(`Error writing to file: ${err}`);
        } else {
            console.log('File updated successfully.');
        }
    });
});
