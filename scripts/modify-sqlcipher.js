const fs = require('fs');
const path = require('path');

// Path to the sqlite3-binding.js file
const bindingPath = path.resolve(__dirname, '../node_modules/@journeyapps/sqlcipher/lib/sqlite3-binding.js');

// New content that handles macOS bindings for both development and production
const newContent = `
var binary = require('@mapbox/node-pre-gyp');
var path = require('path');
var fs = require('fs');

function findBinding() {  
        if (process.platform === 'darwin') {
            const arch = process.arch;
            const bindingName = 'napi-v6-darwin-' + (arch === 'arm64' ? 'arm64' : 'x64');
            
            // Check webpack path first
            const webpackPath = path.join(__dirname, '..', '..', '..', '.webpack', 'main', 'native_modules', 'lib', 'binding', bindingName, 'node_sqlite3.node');
            if (fs.existsSync(webpackPath)) {
                return webpackPath;
            }

            // Check node_modules path
            const nodePath = path.join(__dirname, '..', 'binding', bindingName, 'node_sqlite3.node');
            if (fs.existsSync(nodePath)) {
                return nodePath;
            }

            // Check resources path (for production)
            if (process.resourcesPath) {
                const resourcePath = path.join(process.resourcesPath, bindingName, 'node_sqlite3.node');
                if (fs.existsSync(resourcePath)) {
                    return resourcePath;
                }
            }
        }

        return binary.find(path.resolve(path.join(__dirname,'../package.json')));
}

var binding_path = findBinding();
var binding = require(binding_path);
module.exports = exports = binding;
`;

// Write the new content
fs.writeFileSync(bindingPath, newContent);

console.log('Successfully modified SQLCipher binding resolution'); 