<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css?family=Noto+Sans:300,400,500,600,700,900&display=swap" rel="stylesheet">
    <title>Text Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,body{
            height: 100%;
        }

        body {
            margin: 0;
            padding: 15px;
            font-family:Noto Sans;
            overflow-y: auto;
            background-color: #000;
            overflow: hidden;
        }
        #content {
            font-family: Noto Sans;
            margin-bottom: 12px;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow: auto;
            height: calc(100% - 55px);
            border: 1px solid #000;
            padding: 12px;
            border-radius: 6px;
            background-color: #fff;
            color: #000;
        }

        #content::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        }

        #content::-webkit-scrollbar-thumb {
        background:#9da2b2;
        border-radius: 50px;
        }

        #content::-webkit-scrollbar-track {
        background: transparent;
        }

        #button-container {
            display: flex;
            justify-content: space-between;
        }

        button {
           font-family: Noto Sans;
            padding: 8px 16px;
            font-size: 16px;
            cursor: pointer;
            border: 0px;
            border-radius: 4px;
            padding: 8px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background: transparent;
            transition: all 0.1s;
            color: #fff;
        }
        button:hover{
            background-color: #70ff00;
            color: #000;
            border: solid 0.5px #70ff00;
        }
        button:focus{
            outline: none;
        }
    </style>
</head>
<body>
    <div id="content"></div>
    <div id="button-container">
        <button id="close-button">Close</button>
        <button id="copy-close-button">Copy & Close</button>
    </div>
    <script>
        // Populate the content dynamically
        window.addEventListener('DOMContentLoaded', () => {
            const params = new URLSearchParams(window.location.search);
            document.getElementById('content').innerText = params.get('content') || '';
        });

        // Close button functionality
        document.getElementById('close-button').addEventListener('click', () => {
            window.electron.send({ channel: 'log-window-close' });
        });

        // Copy & Close button functionality
        document.getElementById('copy-close-button').addEventListener('click', () => {
            const content = document.getElementById('content').innerText;
            window.electron.send({ channel: 'log-window-copy', data: null });
        });
    </script>
</body>
</html>
